#include "sample_images.h"


// width is 160, height is 160
const uint8_t stars[12800] PROGMEM = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x10, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x12, 0x11, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x13, 0x31, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 0x32, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x12, 0x33, 0x33, 0x11, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x33, 0x21, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 0x34, 0x33, 0x31, 0x11, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x43, 0x33, 0x11, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x12, 0x33, 0x44, 0x44, 0x33, 0x21, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x23, 0x33, 0x44, 0x44, 0x33, 0x31, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x44, 0x43, 0x32, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x12, 0x33, 0x44, 0x46, 0x54, 0x43, 0x33, 0x21, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x56, 0x64, 0x44, 0x33, 0x31, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x23, 0x34, 0x44, 0x66, 0x66, 0x44, 0x43, 0x32, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x12, 0x33, 0x34, 0x45, 0x66, 0x66, 0x54, 0x43, 0x33, 
0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x13, 0x33, 0x44, 0x46, 0x66, 0x66, 0x64, 0x44, 0x33, 
0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x34, 0x44, 0x66, 0x67, 0x76, 0x65, 0x44, 0x33, 
0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x11, 0x33, 0x34, 0x45, 0x66, 0x78, 0x86, 0x66, 0x44, 0x43, 
0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 0x88, 0x87, 0x66, 0x64, 0x44, 
0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x23, 0x33, 0x44, 0x56, 0x67, 0x88, 0x88, 0x76, 0x65, 0x44, 
0x33, 0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x33, 0x34, 0x44, 0x66, 0x68, 0x88, 0x88, 0x86, 0x66, 0x44, 
0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x12, 0x33, 0x44, 0x46, 0x66, 0x78, 0x89, 0x98, 0x87, 0x66, 0x54, 
0x43, 0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 
0x13, 0x33, 0x44, 0x56, 0x67, 0x88, 0x89, 0x98, 0x88, 0x66, 0x65, 
0x44, 0x33, 0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 
0x23, 0x34, 0x44, 0x66, 0x68, 0x88, 0x99, 0x99, 0x88, 0x86, 0x66, 
0x44, 0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x12, 
0x33, 0x34, 0x45, 0x66, 0x78, 0x89, 0x99, 0x99, 0x88, 0x87, 0x66, 
0x54, 0x43, 0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x13, 
0x33, 0x44, 0x46, 0x66, 0x88, 0x89, 0x9a, 0x99, 0x98, 0x88, 0x66, 
0x64, 0x44, 0x33, 0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 
0x34, 0x44, 0x66, 0x67, 0x88, 0x99, 0x9b, 0xa9, 0x99, 0x88, 0x76, 
0x65, 0x44, 0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 
0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 
0x34, 0x45, 0x66, 0x78, 0x88, 0x99, 0xab, 0xba, 0x99, 0x88, 0x86, 
0x66, 0x54, 0x43, 0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x11, 0x11, 
0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x33, 0x22, 0x22, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x12, 0x33, 
0x44, 0x46, 0x66, 0x88, 0x89, 0x99, 0xbb, 0xbb, 0x99, 0x98, 0x88, 
0x66, 0x64, 0x44, 0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x12, 0x22, 
0x23, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x23, 0x33, 0x33, 0x33, 0x33, 0x22, 0x22, 0x11, 0x11, 0x11, 
0x11, 0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x11, 0x23, 0x33, 
0x44, 0x56, 0x67, 0x88, 0x99, 0x9a, 0xbb, 0xbb, 0xa9, 0x99, 0x88, 
0x76, 0x65, 0x44, 0x33, 0x31, 0x11, 0x00, 0x00, 0x00, 0x11, 0x11, 
0x11, 0x11, 0x11, 0x11, 0x11, 0x12, 0x22, 0x22, 0x33, 0x33, 0x33, 
0x33, 0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x13, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x22, 
0x22, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x33, 0x34, 
0x44, 0x66, 0x68, 0x88, 0x99, 0xab, 0xbb, 0xbb, 0xb9, 0x99, 0x88, 
0x86, 0x66, 0x44, 0x43, 0x33, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 
0x11, 0x11, 0x22, 0x22, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 
0x33, 0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x44, 0x43, 0x33, 0x33, 0x33, 0x33, 0x33, 
0x33, 0x33, 0x33, 0x22, 0x22, 0x11, 0x11, 0x11, 0x12, 0x33, 0x44, 
0x46, 0x66, 0x78, 0x89, 0x99, 0xbb, 0xbc, 0xbb, 0xba, 0x99, 0x98, 
0x87, 0x66, 0x54, 0x44, 0x33, 0x21, 0x11, 0x11, 0x11, 0x22, 0x22, 
0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x44, 0x44, 0x44, 
0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x33, 
0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x22, 0x23, 0x33, 0x44, 
0x56, 0x67, 0x88, 0x89, 0x9a, 0xbb, 0xbc, 0xcb, 0xbb, 0xa9, 0x98, 
0x88, 0x66, 0x65, 0x44, 0x33, 0x32, 0x22, 0x33, 0x33, 0x33, 0x33, 
0x33, 0x33, 0x33, 0x33, 0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 
0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x11, 0x33, 0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 
0x44, 0x44, 0x43, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x34, 0x44, 
0x66, 0x68, 0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 0xbb, 0xb9, 0x99, 
0x88, 0x86, 0x66, 0x44, 0x43, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 
0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x55, 0x44, 0x43, 
0x33, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x34, 0x44, 0x66, 0x66, 0x65, 0x55, 0x54, 0x44, 
0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x33, 0x33, 0x44, 0x45, 
0x66, 0x78, 0x89, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xbb, 0xba, 0x99, 
0x98, 0x87, 0x66, 0x54, 0x43, 0x33, 0x33, 0x34, 0x44, 0x44, 0x44, 
0x44, 0x44, 0x44, 0x44, 0x45, 0x55, 0x56, 0x66, 0x65, 0x44, 0x43, 
0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x33, 0x44, 0x56, 0x66, 0x66, 0x66, 0x66, 0x66, 
0x55, 0x55, 0x54, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x56, 
0x66, 0x88, 0x89, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xcb, 0xbb, 0x99, 
0x98, 0x88, 0x66, 0x64, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 
0x45, 0x55, 0x56, 0x66, 0x66, 0x66, 0x66, 0x66, 0x65, 0x44, 0x33, 
0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x13, 0x33, 0x44, 0x46, 0x66, 0x66, 0x66, 0x66, 0x66, 
0x66, 0x66, 0x66, 0x66, 0x55, 0x55, 0x44, 0x44, 0x44, 0x44, 0x66, 
0x68, 0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xb9, 
0x99, 0x88, 0x76, 0x66, 0x44, 0x44, 0x44, 0x45, 0x55, 0x56, 0x66, 
0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x67, 0x66, 0x64, 0x44, 0x33, 
0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 0x88, 0x88, 0x77, 0x77, 
0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x55, 0x55, 0x66, 
0x78, 0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xba, 
0x99, 0x88, 0x87, 0x66, 0x55, 0x56, 0x66, 0x66, 0x66, 0x66, 0x66, 
0x66, 0x66, 0x66, 0x77, 0x77, 0x88, 0x87, 0x66, 0x54, 0x43, 0x33, 
0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x11, 0x33, 0x34, 0x45, 0x66, 0x78, 0x88, 0x88, 0x88, 
0x88, 0x87, 0x77, 0x77, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 
0x88, 0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 
0x99, 0x98, 0x88, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x77, 
0x77, 0x88, 0x88, 0x88, 0x88, 0x88, 0x86, 0x66, 0x54, 0x43, 0x33, 
0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x33, 0x34, 0x44, 0x66, 0x68, 0x88, 0x88, 0x88, 
0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x77, 0x76, 0x66, 0x66, 0x67, 
0x88, 0x99, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 
0xa9, 0x99, 0x88, 0x76, 0x66, 0x66, 0x67, 0x77, 0x78, 0x88, 0x88, 
0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x86, 0x66, 0x44, 0x43, 0x32, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x23, 0x34, 0x44, 0x56, 0x67, 0x88, 0x99, 0x99, 
0x99, 0x98, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x78, 
0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xcd, 0xdc, 0xcc, 0xcc, 0xbb, 
0xba, 0x99, 0x88, 0x87, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 
0x88, 0x89, 0x99, 0x99, 0x99, 0x88, 0x76, 0x65, 0x44, 0x33, 0x31, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x56, 0x67, 0x88, 0x89, 0x99, 
0x99, 0x99, 0x99, 0x99, 0x99, 0x98, 0x88, 0x88, 0x88, 0x88, 0x88, 
0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 0xcd, 0xdc, 0xcc, 0xcc, 0xbb, 
0xbb, 0x99, 0x98, 0x88, 0x88, 0x88, 0x88, 0x88, 0x89, 0x99, 0x99, 
0x99, 0x99, 0x99, 0x99, 0x98, 0x88, 0x66, 0x64, 0x44, 0x33, 0x31, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 0x88, 0x89, 0x99, 
0xa9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x98, 0x88, 
0x99, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 
0xbb, 0xa9, 0x98, 0x88, 0x89, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 
0x99, 0x99, 0xaa, 0x99, 0x98, 0x87, 0x66, 0x64, 0x44, 0x33, 0x21, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x12, 0x33, 0x34, 0x45, 0x66, 0x78, 0x89, 0x99, 
0xab, 0xbb, 0xba, 0xaa, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 
0x99, 0x9b, 0xbb, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 
0xbb, 0xb9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0xaa, 0xaa, 
0xab, 0xbb, 0xba, 0x99, 0x98, 0x87, 0x66, 0x54, 0x43, 0x33, 0x11, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x66, 0x68, 0x88, 0x99, 
0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xba, 0xaa, 0xa9, 0x99, 0x99, 
0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xdd, 0xdc, 0xcc, 0xcc, 
0xbb, 0xba, 0x99, 0x99, 0x99, 0x9a, 0xaa, 0xab, 0xbb, 0xbb, 0xbb, 
0xbb, 0xbb, 0xb9, 0x99, 0x88, 0x86, 0x66, 0x44, 0x43, 0x32, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x23, 0x34, 0x44, 0x66, 0x68, 0x88, 0x99, 
0x9b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xaa, 
0xaa, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 
0xcb, 0xbb, 0xaa, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 
0xbb, 0xbb, 0xb9, 0x99, 0x88, 0x76, 0x65, 0x44, 0x43, 0x32, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x56, 0x67, 0x88, 0x99, 
0x9a, 0xbb, 0xbc, 0xcc, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 
0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 0xdd, 0xde, 0xed, 0xdd, 0xcc, 0xcc, 
0xcc, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbc, 0xcc, 
0xcb, 0xbb, 0xa9, 0x98, 0x88, 0x76, 0x65, 0x44, 0x33, 0x31, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x46, 0x66, 0x88, 0x89, 
0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xbb, 
0xbb, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xee, 0xee, 0xdd, 0xdc, 0xcc, 
0xcc, 0xbb, 0xbb, 0xbb, 0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcb, 0xbb, 0x99, 0x98, 0x88, 0x66, 0x64, 0x44, 0x33, 0x21, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 0x78, 0x89, 
0x99, 0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xbc, 0xcc, 0xcc, 0xdd, 0xdd, 0xee, 0xee, 0xdd, 0xdc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xbb, 0xba, 0x99, 0x98, 0x87, 0x66, 0x54, 0x43, 0x33, 0x21, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x45, 0x66, 0x78, 0x88, 
0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xdd, 0xde, 0xee, 0xee, 0xed, 0xdd, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xbb, 0xba, 0x99, 0x88, 0x86, 0x66, 0x54, 0x43, 0x33, 0x11, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 0x34, 0x44, 0x66, 0x68, 0x88, 
0x99, 0x9b, 0xbb, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 0xee, 0xee, 0xed, 0xdd, 0xdc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xbb, 0xb9, 0x99, 0x88, 0x86, 0x66, 0x44, 0x43, 0x32, 0x11, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 0x33, 0x44, 0x56, 0x67, 0x88, 
0x99, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xee, 0xee, 0xee, 0xee, 0xdd, 0xdc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xcc, 0xcc, 0xcb, 
0xbb, 0xa9, 0x99, 0x88, 0x76, 0x65, 0x44, 0x33, 0x31, 0x11, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x56, 0x66, 0x88, 
0x89, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 
0xdc, 0xcc, 0xcc, 0xdd, 0xde, 0xee, 0xee, 0xee, 0xee, 0xed, 0xdd, 
0xcc, 0xcc, 0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 
0xbb, 0xa9, 0x98, 0x88, 0x66, 0x64, 0x44, 0x33, 0x31, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 0x88, 
0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 
0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xee, 0xee, 0xee, 0xee, 0xed, 0xdd, 
0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0xcc, 0xcc, 0xcb, 
0xbb, 0x99, 0x98, 0x87, 0x66, 0x64, 0x44, 0x33, 0x21, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x45, 0x66, 0x78, 
0x89, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 
0xdd, 0xdd, 0xdd, 0xdd, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xdd, 
0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0xcc, 0xcc, 0xbb, 
0xba, 0x99, 0x98, 0x87, 0x66, 0x54, 0x43, 0x33, 0x11, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x66, 0x68, 
0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 0xee, 0xee, 
0xee, 0xdd, 0xdd, 0xdd, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xee, 0xdd, 
0xdd, 0xde, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xdc, 0xcc, 0xcc, 0xbb, 
0xb9, 0x99, 0x88, 0x86, 0x66, 0x44, 0x43, 0x32, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 0x34, 0x44, 0x66, 0x67, 
0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 0xcc, 0xdd, 0xde, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xcc, 0xcc, 0xcc, 0xbb, 
0xb9, 0x99, 0x88, 0x76, 0x65, 0x44, 0x33, 0x32, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x56, 0x67, 
0x88, 0x99, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xde, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xff, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 
0xa9, 0x98, 0x88, 0x76, 0x65, 0x44, 0x33, 0x31, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 
0x88, 0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xdd, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xff, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 
0x99, 0x98, 0x88, 0x66, 0x64, 0x44, 0x33, 0x21, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x12, 0x33, 0x44, 0x45, 0x66, 
0x78, 0x89, 0x99, 0xbb, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0xff, 0xfe, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xbb, 0xba, 
0x99, 0x98, 0x87, 0x66, 0x54, 0x43, 0x33, 0x21, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x45, 0x66, 
0x78, 0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xee, 0xee, 
0xee, 0xff, 0xee, 0xee, 0xee, 0xff, 0xff, 0xff, 0xff, 0xee, 0xee, 
0xee, 0xff, 0xee, 0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xbb, 0xba, 
0x99, 0x88, 0x86, 0x66, 0x44, 0x43, 0x33, 0x11, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 0x34, 0x44, 0x66, 
0x68, 0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 0xee, 
0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xfe, 0xee, 0xee, 0xed, 0xdd, 0xcc, 0xcc, 0xcc, 0xbb, 0xb9, 
0x99, 0x88, 0x86, 0x66, 0x44, 0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x23, 0x33, 0x44, 0x56, 
0x67, 0x88, 0x99, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xde, 0xee, 
0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xfe, 0xee, 0xee, 0xed, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 0xa9, 
0x99, 0x88, 0x76, 0x65, 0x44, 0x33, 0x32, 0x11, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x56, 
0x66, 0x88, 0x89, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 0xdd, 0xde, 0xee, 
0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xfe, 0xee, 0xee, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 0xa9, 
0x98, 0x88, 0x66, 0x65, 0x44, 0x43, 0x33, 0x11, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x44, 0x66, 
0x67, 0x88, 0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 0xcd, 0xdd, 0xee, 
0xee, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xee, 0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xcb, 0xbb, 0x99, 
0x98, 0x88, 0x76, 0x66, 0x44, 0x44, 0x33, 0x31, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x46, 0x66, 
0x78, 0x88, 0x99, 0x9a, 0xbb, 0xbb, 0xcc, 0xcc, 0xcd, 0xdd, 0xee, 
0xee, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xee, 0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xbb, 0xbb, 0xa9, 
0x99, 0x88, 0x87, 0x66, 0x65, 0x44, 0x43, 0x33, 0x11, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x44, 0x66, 0x67, 
0x88, 0x89, 0x99, 0xab, 0xbb, 0xbc, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 
0xee, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xee, 0xee, 0xed, 0xdd, 0xdc, 0xcc, 0xcc, 0xcb, 0xbb, 0xba, 
0x99, 0x98, 0x88, 0x76, 0x66, 0x54, 0x44, 0x33, 0x31, 0x11, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x56, 0x66, 0x78, 
0x88, 0x99, 0x9a, 0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xee, 
0xee, 0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xfe, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 0xbb, 0xbb, 
0xa9, 0x99, 0x88, 0x87, 0x66, 0x65, 0x44, 0x43, 0x33, 0x11, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x45, 0x66, 0x67, 0x88, 
0x89, 0x99, 0xab, 0xbb, 0xbc, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 0xee, 
0xee, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xdc, 0xcc, 0xcc, 0xcb, 0xbb, 
0xba, 0x99, 0x98, 0x88, 0x76, 0x66, 0x54, 0x44, 0x33, 0x32, 0x11, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x56, 0x66, 0x78, 0x88, 
0x99, 0x9a, 0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xee, 0xee, 
0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xfe, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 0xbb, 
0xbb, 0xa9, 0x99, 0x88, 0x87, 0x66, 0x65, 0x44, 0x43, 0x33, 0x21, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x13, 0x33, 0x44, 0x45, 0x66, 0x67, 0x88, 0x89, 
0x99, 0xab, 0xbb, 0xbc, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 0xee, 0xee, 
0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 
0xbb, 0xba, 0x99, 0x98, 0x88, 0x76, 0x66, 0x54, 0x44, 0x33, 0x32, 
0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x11, 0x33, 0x34, 0x44, 0x56, 0x66, 0x78, 0x88, 0x99, 
0x9a, 0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xee, 0xee, 0xee, 
0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xfe, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 
0xcb, 0xbb, 0xa9, 0x99, 0x88, 0x87, 0x66, 0x65, 0x44, 0x43, 0x33, 
0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x11, 0x13, 0x33, 0x44, 0x45, 0x66, 0x67, 0x88, 0x89, 0x99, 
0xab, 0xbb, 0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 0xee, 0xee, 0xee, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xdc, 0xcc, 0xcc, 
0xcc, 0xbb, 0xba, 0x99, 0x98, 0x88, 0x76, 0x66, 0x54, 0x44, 0x33, 
0x32, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x12, 0x33, 0x34, 0x44, 0x56, 0x66, 0x78, 0x88, 0x99, 0x9a, 
0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xee, 0xee, 0xee, 0xee, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xee, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xcc, 0xcc, 
0xcc, 0xcb, 0xbb, 0xa9, 0x99, 0x88, 0x87, 0x66, 0x65, 0x44, 0x43, 
0x33, 0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x23, 0x33, 0x44, 0x45, 0x66, 0x67, 0x88, 0x89, 0x99, 0xab, 
0xbb, 0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xde, 0xee, 0xee, 0xee, 0xee, 
0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xdc, 0xcc, 
0xcc, 0xcc, 0xbb, 0xba, 0x99, 0x98, 0x88, 0x76, 0x66, 0x54, 0x44, 
0x33, 0x32, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 
0x12, 0x33, 0x34, 0x44, 0x56, 0x66, 0x78, 0x88, 0x99, 0x9a, 0xbb, 
0xbc, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xee, 0xee, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xcc, 
0xcc, 0xcc, 0xcb, 0xbb, 0xa9, 0x99, 0x88, 0x87, 0x66, 0x65, 0x44, 
0x43, 0x33, 0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 
0x23, 0x33, 0x44, 0x45, 0x66, 0x67, 0x88, 0x89, 0x99, 0xab, 0xbb, 
0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xdd, 0xde, 0xee, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xdd, 0xdc, 
0xcc, 0xcc, 0xcc, 0xbb, 0xba, 0x99, 0x98, 0x88, 0x86, 0x66, 0x54, 
0x44, 0x33, 0x32, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x12, 
0x33, 0x34, 0x44, 0x56, 0x66, 0x78, 0x88, 0x99, 0x9a, 0xbb, 0xbc, 
0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 
0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 
0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xa9, 0x99, 0x88, 0x88, 0x66, 0x65, 
0x44, 0x43, 0x33, 0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x23, 
0x33, 0x44, 0x45, 0x66, 0x67, 0x88, 0x89, 0x99, 0xab, 0xbb, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xee, 
0xee, 0xee, 0xee, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 
0xee, 0xee, 0xee, 0xee, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xbb, 0x99, 0x98, 0x88, 0x86, 0x66, 
0x54, 0x44, 0x33, 0x32, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x12, 0x33, 
0x34, 0x44, 0x56, 0x66, 0x78, 0x88, 0x99, 0x9a, 0xbb, 0xbc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 
0xee, 0xee, 0xee, 0xef, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 
0xee, 0xee, 0xee, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xb9, 0x99, 0x88, 0x88, 0x66, 
0x65, 0x44, 0x43, 0x33, 0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x23, 0x33, 
0x44, 0x45, 0x66, 0x67, 0x88, 0x89, 0x99, 0xab, 0xbb, 0xbb, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 0xdd, 0xdd, 0xdd, 
0xdd, 0xee, 0xee, 0xef, 0xff, 0xff, 0xee, 0xee, 0xff, 0xff, 0xfe, 
0xee, 0xee, 0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xbb, 0xbb, 0x99, 0x99, 0x88, 0x86, 
0x66, 0x54, 0x44, 0x33, 0x32, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x12, 0x33, 0x34, 
0x44, 0x56, 0x66, 0x88, 0x88, 0x99, 0x9a, 0xbb, 0xbb, 0xbb, 0xbb, 
0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 
0xdd, 0xee, 0xee, 0xef, 0xff, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfe, 
0xee, 0xee, 0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa9, 0x99, 0x98, 0x88, 
0x66, 0x66, 0x44, 0x43, 0x33, 0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x23, 0x33, 0x44, 
0x45, 0x66, 0x68, 0x88, 0x89, 0x99, 0x99, 0x9a, 0xab, 0xbb, 0xbb, 
0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 
0xdd, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xef, 0xfe, 
0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xbb, 0xbb, 0xbb, 0xbb, 0xba, 0xa9, 0x99, 0x99, 0x99, 0x88, 
0x86, 0x66, 0x64, 0x44, 0x33, 0x32, 0x11, 0x10, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x12, 0x33, 0x34, 0x44, 
0x56, 0x66, 0x88, 0x88, 0x88, 0x99, 0x99, 0x99, 0x99, 0xaa, 0xbb, 
0xbb, 0xbb, 0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 
0xdd, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 
0xbb, 0xbb, 0xbb, 0xbb, 0xaa, 0x99, 0x99, 0x99, 0x99, 0x88, 0x88, 
0x88, 0x66, 0x66, 0x44, 0x43, 0x33, 0x21, 0x11, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x23, 0x33, 0x44, 0x45, 
0x66, 0x67, 0x88, 0x88, 0x88, 0x88, 0x89, 0x99, 0x99, 0x99, 0x9a, 
0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 
0xdd, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xbb, 
0xbb, 0xbb, 0xba, 0xa9, 0x99, 0x99, 0x99, 0x98, 0x88, 0x88, 0x88, 
0x87, 0x76, 0x66, 0x64, 0x44, 0x33, 0x32, 0x11, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x12, 0x33, 0x34, 0x44, 0x56, 
0x66, 0x66, 0x66, 0x77, 0x88, 0x88, 0x88, 0x88, 0x99, 0x99, 0x99, 
0x99, 0xaa, 0xbb, 0xbb, 0xbb, 0xbb, 0xbc, 0xcc, 0xcc, 0xcc, 0xcd, 
0xdd, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xbb, 0xbb, 
0xbb, 0xaa, 0x99, 0x99, 0x99, 0x99, 0x88, 0x88, 0x88, 0x88, 0x77, 
0x66, 0x66, 0x66, 0x65, 0x44, 0x43, 0x33, 0x21, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x23, 0x33, 0x44, 0x44, 0x45, 
0x56, 0x66, 0x66, 0x66, 0x67, 0x78, 0x88, 0x88, 0x88, 0x89, 0x99, 
0x99, 0x99, 0x9a, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xcc, 0xcc, 0xcd, 
0xdd, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0xde, 0xee, 0xee, 0xee, 
0xee, 0xee, 0xdd, 0xdc, 0xcc, 0xcc, 0xbb, 0xbb, 0xbb, 0xbb, 0xba, 
0xa9, 0x99, 0x99, 0x99, 0x98, 0x88, 0x88, 0x88, 0x87, 0x76, 0x66, 
0x66, 0x66, 0x65, 0x44, 0x44, 0x44, 0x33, 0x33, 0x11, 0x10, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x11, 0x12, 0x33, 0x33, 0x44, 0x44, 0x44, 
0x44, 0x55, 0x66, 0x66, 0x66, 0x66, 0x77, 0x88, 0x88, 0x88, 0x88, 
0x99, 0x99, 0x99, 0x99, 0xaa, 0xbb, 0xbb, 0xbb, 0xbc, 0xcc, 0xcd, 
0xdd, 0xee, 0xee, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xee, 0xee, 0xee, 
0xee, 0xed, 0xdd, 0xdc, 0xcc, 0xcb, 0xbb, 0xbb, 0xbb, 0xaa, 0x99, 
0x99, 0x99, 0x99, 0x88, 0x88, 0x88, 0x88, 0x77, 0x66, 0x66, 0x66, 
0x66, 0x54, 0x44, 0x44, 0x44, 0x43, 0x33, 0x33, 0x31, 0x11, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x11, 0x23, 0x33, 0x33, 0x33, 0x34, 0x44, 
0x44, 0x44, 0x45, 0x56, 0x66, 0x66, 0x66, 0x67, 0x78, 0x88, 0x88, 
0x88, 0x89, 0x99, 0x99, 0x99, 0x9a, 0xab, 0xbb, 0xbc, 0xcc, 0xcc, 
0xdd, 0xde, 0xee, 0xee, 0xee, 0xdd, 0xdd, 0xdd, 0xdd, 0xee, 0xee, 
0xee, 0xed, 0xdd, 0xdc, 0xcc, 0xcb, 0xbb, 0xba, 0xa9, 0x99, 0x99, 
0x99, 0x98, 0x88, 0x88, 0x88, 0x87, 0x76, 0x66, 0x66, 0x66, 0x65, 
0x44, 0x44, 0x44, 0x44, 0x43, 0x33, 0x33, 0x33, 0x32, 0x11, 0x10, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x12, 0x33, 0x33, 0x33, 0x33, 
0x44, 0x44, 0x44, 0x44, 0x55, 0x66, 0x66, 0x66, 0x66, 0x77, 0x88, 
0x88, 0x88, 0x88, 0x99, 0x99, 0x99, 0x9a, 0xbb, 0xbc, 0xcc, 0xcc, 
0xdd, 0xde, 0xee, 0xee, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xee, 
0xee, 0xed, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 0xa9, 0x99, 0x99, 0x99, 
0x88, 0x88, 0x88, 0x88, 0x77, 0x66, 0x66, 0x66, 0x66, 0x54, 0x44, 
0x44, 0x44, 0x44, 0x33, 0x33, 0x33, 0x33, 0x21, 0x11, 0x11, 0x11, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x11, 0x23, 0x33, 0x33, 
0x33, 0x34, 0x44, 0x44, 0x44, 0x45, 0x56, 0x66, 0x66, 0x66, 0x67, 
0x78, 0x88, 0x88, 0x88, 0x89, 0x99, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 
0xdd, 0xde, 0xee, 0xed, 0xdd, 0xdd, 0xdc, 0xcd, 0xdd, 0xdd, 0xde, 
0xee, 0xed, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 0x99, 0x99, 0x98, 0x88, 
0x88, 0x88, 0x87, 0x76, 0x66, 0x66, 0x66, 0x65, 0x44, 0x44, 0x44, 
0x44, 0x43, 0x33, 0x33, 0x33, 0x32, 0x11, 0x11, 0x11, 0x11, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 0x11, 0x12, 0x33, 
0x33, 0x33, 0x33, 0x44, 0x44, 0x44, 0x44, 0x55, 0x66, 0x66, 0x66, 
0x66, 0x77, 0x88, 0x88, 0x88, 0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 
0xdd, 0xde, 0xed, 0xdd, 0xdd, 0xdc, 0xcc, 0xcc, 0xcd, 0xdd, 0xdd, 
0xde, 0xed, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 0x99, 0x98, 0x88, 0x88, 
0x88, 0x77, 0x66, 0x66, 0x66, 0x66, 0x54, 0x44, 0x44, 0x44, 0x44, 
0x33, 0x33, 0x33, 0x33, 0x21, 0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x11, 
0x23, 0x33, 0x33, 0x33, 0x34, 0x44, 0x44, 0x44, 0x45, 0x56, 0x66, 
0x66, 0x66, 0x67, 0x78, 0x88, 0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 
0xdd, 0xde, 0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 0xcc, 0xdd, 0xdd, 
0xdd, 0xed, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 0x99, 0x98, 0x88, 0x87, 
0x76, 0x66, 0x66, 0x66, 0x65, 0x44, 0x44, 0x44, 0x44, 0x43, 0x33, 
0x33, 0x33, 0x32, 0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 
0x11, 0x12, 0x33, 0x33, 0x33, 0x33, 0x44, 0x44, 0x44, 0x44, 0x55, 
0x66, 0x66, 0x66, 0x66, 0x78, 0x89, 0x99, 0xbb, 0xbc, 0xcc, 0xcc, 
0xdd, 0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xdd, 
0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 0xbb, 0x99, 0x98, 0x87, 0x66, 
0x66, 0x66, 0x66, 0x54, 0x44, 0x44, 0x44, 0x44, 0x33, 0x33, 0x33, 
0x33, 0x21, 0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 
0x11, 0x11, 0x11, 0x23, 0x33, 0x33, 0x33, 0x34, 0x44, 0x44, 0x44, 
0x44, 0x56, 0x66, 0x66, 0x68, 0x88, 0x99, 0xab, 0xbc, 0xcc, 0xcc, 
0xdd, 0xdd, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 
0xdd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 0xba, 0x99, 0x88, 0x86, 0x66, 
0x66, 0x65, 0x44, 0x44, 0x44, 0x44, 0x43, 0x33, 0x33, 0x33, 0x32, 
0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x11, 0x11, 0x11, 0x12, 0x33, 0x33, 0x33, 0x33, 0x44, 0x44, 
0x44, 0x44, 0x55, 0x66, 0x68, 0x88, 0x99, 0xab, 0xbc, 0xcc, 0xcc, 
0xdd, 0xdd, 0xdc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 
0xcd, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 0xba, 0x99, 0x88, 0x86, 0x66, 
0x54, 0x44, 0x44, 0x44, 0x44, 0x33, 0x33, 0x33, 0x33, 0x21, 0x11, 
0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x11, 0x11, 0x11, 0x23, 0x33, 0x33, 0x33, 0x34, 
0x44, 0x44, 0x44, 0x56, 0x68, 0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 
0xdd, 0xdd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0xbc, 0xcc, 0xcc, 0xcc, 
0xcc, 0xdd, 0xdd, 0xcc, 0xcc, 0xcb, 0xba, 0x99, 0x88, 0x86, 0x66, 
0x44, 0x44, 0x44, 0x43, 0x33, 0x33, 0x33, 0x32, 0x11, 0x11, 0x11, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 0x11, 0x12, 0x33, 0x33, 0x33, 
0x33, 0x44, 0x44, 0x56, 0x68, 0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 
0xdd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 
0xcc, 0xcc, 0xdd, 0xcc, 0xcc, 0xbb, 0xba, 0x99, 0x88, 0x86, 0x65, 
0x44, 0x43, 0x33, 0x33, 0x33, 0x33, 0x21, 0x11, 0x11, 0x11, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 0x11, 0x23, 0x33, 
0x33, 0x33, 0x44, 0x56, 0x67, 0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xbb, 0xbb, 0xbb, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xba, 0x99, 0x88, 0x76, 0x65, 
0x44, 0x33, 0x33, 0x33, 0x32, 0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 0x11, 0x12, 
0x33, 0x33, 0x44, 0x56, 0x67, 0x88, 0x99, 0xab, 0xbb, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xba, 0x99, 0x88, 0x76, 0x65, 
0x44, 0x33, 0x33, 0x21, 0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x11, 
0x12, 0x33, 0x44, 0x56, 0x67, 0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcc, 0xcb, 0xbb, 0xbb, 0xa9, 0x9a, 0xbb, 0xbb, 0xbc, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xb9, 0x99, 0x88, 0x76, 0x65, 
0x44, 0x33, 0x21, 0x11, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 
0x12, 0x33, 0x44, 0x46, 0x67, 0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 
0xcc, 0xcc, 0xcb, 0xbb, 0xbb, 0xba, 0x99, 0x99, 0xab, 0xbb, 0xbb, 
0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xb9, 0x99, 0x88, 0x76, 0x65, 
0x44, 0x33, 0x21, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x12, 0x33, 0x44, 0x46, 0x67, 0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 
0xcc, 0xcc, 0xbb, 0xbb, 0xba, 0x99, 0x99, 0x99, 0x99, 0xab, 0xbb, 
0xbb, 0xcc, 0xcc, 0xcc, 0xcc, 0xbb, 0xb9, 0x99, 0x88, 0x76, 0x64, 
0x44, 0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x12, 0x33, 0x44, 0x46, 0x66, 0x88, 0x99, 0x9b, 0xbb, 0xcc, 0xcc, 
0xcc, 0xbb, 0xbb, 0xbb, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x9a, 0xbb, 
0xbb, 0xbb, 0xcc, 0xcc, 0xcc, 0xbb, 0xb9, 0x99, 0x88, 0x66, 0x64, 
0x44, 0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x33, 0x44, 0x46, 0x66, 0x88, 0x89, 0x9a, 0xbb, 0xcc, 0xcc, 
0xcb, 0xbb, 0xbb, 0xa9, 0x99, 0x99, 0x88, 0x88, 0x99, 0x99, 0x9a, 
0xbb, 0xbb, 0xbc, 0xcc, 0xcc, 0xbb, 0xb9, 0x99, 0x88, 0x66, 0x64, 
0x44, 0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x33, 0x34, 0x46, 0x66, 0x88, 0x89, 0x9a, 0xbb, 0xcc, 0xcb, 
0xbb, 0xbb, 0xba, 0x99, 0x99, 0x98, 0x88, 0x88, 0x88, 0x99, 0x99, 
0xab, 0xbb, 0xbb, 0xbc, 0xcc, 0xbb, 0xa9, 0x98, 0x88, 0x66, 0x64, 
0x43, 0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x33, 0x34, 0x45, 0x66, 0x88, 0x89, 0x9a, 0xbb, 0xcc, 0xbb, 
0xbb, 0xba, 0x99, 0x99, 0x98, 0x88, 0x88, 0x88, 0x88, 0x89, 0x99, 
0x99, 0xab, 0xbb, 0xbb, 0xcc, 0xbb, 0xa9, 0x98, 0x88, 0x66, 0x64, 
0x43, 0x33, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x33, 0x34, 0x45, 0x66, 0x88, 0x89, 0x9a, 0xbb, 0xbb, 0xbb, 
0xbb, 0xa9, 0x99, 0x98, 0x88, 0x88, 0x76, 0x67, 0x88, 0x88, 0x89, 
0x99, 0x99, 0xbb, 0xbb, 0xbb, 0xbb, 0xa9, 0x98, 0x88, 0x66, 0x54, 
0x43, 0x33, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x33, 0x34, 0x45, 0x66, 0x78, 0x89, 0x9a, 0xbb, 0xbb, 0xbb, 
0xa9, 0x99, 0x99, 0x88, 0x88, 0x86, 0x66, 0x66, 0x68, 0x88, 0x88, 
0x99, 0x99, 0x9a, 0xbb, 0xbb, 0xbb, 0xa9, 0x98, 0x87, 0x66, 0x54, 
0x43, 0x33, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x23, 0x34, 0x45, 0x66, 0x78, 0x89, 0x9a, 0xbb, 0xbb, 0xba, 
0x99, 0x99, 0x88, 0x88, 0x87, 0x66, 0x66, 0x66, 0x66, 0x78, 0x88, 
0x88, 0x99, 0x99, 0x9a, 0xbb, 0xbb, 0xa9, 0x98, 0x87, 0x66, 0x54, 
0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x23, 0x34, 0x45, 0x66, 0x78, 0x89, 0x99, 0xbb, 0xba, 0x99, 
0x99, 0x98, 0x88, 0x88, 0x66, 0x66, 0x65, 0x56, 0x66, 0x66, 0x78, 
0x88, 0x89, 0x99, 0x99, 0xab, 0xbb, 0xa9, 0x98, 0x87, 0x66, 0x54, 
0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x23, 0x34, 0x44, 0x66, 0x78, 0x89, 0x99, 0xbb, 0xa9, 0x99, 
0x98, 0x88, 0x88, 0x76, 0x66, 0x66, 0x44, 0x44, 0x56, 0x66, 0x67, 
0x88, 0x88, 0x89, 0x99, 0x99, 0xab, 0x99, 0x98, 0x87, 0x66, 0x54, 
0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x23, 0x34, 0x44, 0x66, 0x78, 0x89, 0x99, 0xa9, 0x99, 0x99, 
0x88, 0x88, 0x86, 0x66, 0x66, 0x54, 0x44, 0x44, 0x45, 0x66, 0x66, 
0x67, 0x88, 0x88, 0x99, 0x99, 0x9a, 0x99, 0x98, 0x87, 0x66, 0x44, 
0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x23, 0x34, 0x44, 0x66, 0x68, 0x89, 0x99, 0x99, 0x99, 0x88, 
0x88, 0x87, 0x66, 0x66, 0x64, 0x44, 0x44, 0x44, 0x44, 0x45, 0x66, 
0x66, 0x78, 0x88, 0x88, 0x99, 0x99, 0x99, 0x98, 0x87, 0x66, 0x44, 
0x43, 0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x13, 0x34, 0x44, 0x66, 0x68, 0x88, 0x99, 0x99, 0x98, 0x88, 
0x87, 0x66, 0x66, 0x65, 0x44, 0x44, 0x33, 0x33, 0x44, 0x44, 0x56, 
0x66, 0x66, 0x78, 0x88, 0x88, 0x99, 0x99, 0x98, 0x86, 0x66, 0x44, 
0x43, 0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x13, 0x33, 0x44, 0x66, 0x68, 0x88, 0x99, 0x98, 0x88, 0x88, 
0x76, 0x66, 0x65, 0x44, 0x44, 0x43, 0x33, 0x33, 0x34, 0x44, 0x44, 
0x56, 0x66, 0x67, 0x88, 0x88, 0x89, 0x99, 0x88, 0x86, 0x66, 0x44, 
0x43, 0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x13, 0x33, 0x44, 0x66, 0x68, 0x88, 0x99, 0x88, 0x88, 0x76, 
0x66, 0x66, 0x54, 0x44, 0x43, 0x33, 0x33, 0x33, 0x33, 0x34, 0x44, 
0x45, 0x66, 0x66, 0x67, 0x88, 0x88, 0x89, 0x88, 0x86, 0x66, 0x44, 
0x33, 0x31, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x13, 0x33, 0x44, 0x56, 0x68, 0x88, 0x88, 0x88, 0x87, 0x66, 
0x66, 0x54, 0x44, 0x44, 0x33, 0x33, 0x21, 0x12, 0x33, 0x33, 0x44, 
0x44, 0x45, 0x66, 0x66, 0x78, 0x88, 0x88, 0x88, 0x86, 0x65, 0x44, 
0x33, 0x31, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x13, 0x33, 0x44, 0x56, 0x67, 0x88, 0x88, 0x87, 0x66, 0x66, 
0x65, 0x44, 0x44, 0x33, 0x33, 0x32, 0x11, 0x11, 0x23, 0x33, 0x33, 
0x44, 0x44, 0x46, 0x66, 0x66, 0x78, 0x88, 0x88, 0x86, 0x65, 0x44, 
0x33, 0x31, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x56, 0x67, 0x88, 0x88, 0x76, 0x66, 0x65, 
0x44, 0x44, 0x43, 0x33, 0x32, 0x11, 0x11, 0x11, 0x11, 0x23, 0x33, 
0x34, 0x44, 0x44, 0x56, 0x66, 0x66, 0x88, 0x88, 0x76, 0x65, 0x44, 
0x33, 0x31, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x56, 0x67, 0x88, 0x76, 0x66, 0x66, 0x54, 
0x44, 0x43, 0x33, 0x33, 0x21, 0x11, 0x10, 0x01, 0x11, 0x12, 0x33, 
0x33, 0x34, 0x44, 0x44, 0x66, 0x66, 0x67, 0x88, 0x76, 0x65, 0x44, 
0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x56, 0x67, 0x87, 0x66, 0x66, 0x54, 0x44, 
0x44, 0x33, 0x33, 0x21, 0x11, 0x11, 0x00, 0x00, 0x11, 0x11, 0x12, 
0x33, 0x33, 0x34, 0x44, 0x45, 0x66, 0x66, 0x67, 0x76, 0x65, 0x44, 
0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 0x66, 0x66, 0x65, 0x44, 0x44, 
0x33, 0x33, 0x32, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 
0x13, 0x33, 0x33, 0x44, 0x44, 0x45, 0x66, 0x66, 0x66, 0x65, 0x44, 
0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x12, 0x33, 0x44, 0x46, 0x66, 0x66, 0x65, 0x44, 0x44, 0x43, 
0x33, 0x32, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 
0x11, 0x23, 0x33, 0x33, 0x44, 0x44, 0x56, 0x66, 0x66, 0x64, 0x44, 
0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x11, 0x33, 0x44, 0x46, 0x66, 0x66, 0x44, 0x44, 0x43, 0x33, 
0x33, 0x21, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x11, 0x33, 0x33, 0x34, 0x44, 0x44, 0x56, 0x66, 0x64, 0x44, 
0x33, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x11, 0x33, 0x34, 0x46, 0x66, 0x54, 0x44, 0x44, 0x33, 0x33, 
0x21, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x11, 0x12, 0x33, 0x33, 0x34, 0x44, 0x45, 0x66, 0x64, 0x44, 
0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x33, 0x34, 0x46, 0x64, 0x44, 0x44, 0x33, 0x33, 0x32, 
0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x11, 0x13, 0x33, 0x33, 0x44, 0x44, 0x45, 0x64, 0x43, 
0x33, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x33, 0x34, 0x44, 0x44, 0x44, 0x43, 0x33, 0x32, 0x11, 
0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x11, 0x23, 0x33, 0x33, 0x44, 0x44, 0x44, 0x43, 
0x33, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x33, 0x34, 0x44, 0x44, 0x43, 0x33, 0x33, 0x21, 0x11, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x11, 0x11, 0x23, 0x33, 0x34, 0x44, 0x44, 0x43, 
0x33, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x34, 0x44, 0x43, 0x33, 0x33, 0x21, 0x11, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x11, 0x12, 0x33, 0x33, 0x34, 0x44, 0x43, 
0x33, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x34, 0x44, 0x33, 0x33, 0x31, 0x11, 0x11, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x12, 0x33, 0x33, 0x44, 0x43, 
0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x34, 0x33, 0x33, 0x32, 0x11, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x23, 0x33, 0x33, 0x43, 
0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x33, 0x33, 0x33, 0x11, 0x11, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 0x23, 0x33, 0x33, 
0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x33, 0x33, 0x21, 0x11, 0x10, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x12, 0x33, 0x33, 
0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x13, 0x33, 0x31, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x12, 0x33, 
0x32, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x13, 0x32, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11, 0x13, 
0x31, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x12, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 
0x21, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 
0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00};