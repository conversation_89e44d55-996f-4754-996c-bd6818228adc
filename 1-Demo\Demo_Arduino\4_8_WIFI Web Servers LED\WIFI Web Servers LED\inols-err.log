






Started logging.
2022/06/27 11:25:38 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/27 11:25:38 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/27 11:25:38 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server193424583
2022/06/27 11:25:38 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server193424583\sketch
2022/06/27 11:25:38 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/27 11:25:38 IDE --> initialize 0 [93m locked[0m
2022/06/27 11:25:38 IDE --> initialize 0 [93m unlocked[0m
2022/06/27 11:25:38 INIT--- initializing workbench
2022/06/27 11:25:38 INIT--- [93m locked[0m
2022/06/27 11:25:38 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/27 11:25:38     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/27 11:25:38 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\103513466 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server193424583 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/27 11:25:38 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/27 11:25:39 INIT--- initializing workbench (done)
2022/06/27 11:25:39 INIT--- [93m unlocked[0m
2022/06/27 11:25:39 IDE --> initialized notif1 [93m read-locked[0m
2022/06/27 11:25:39 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/27 11:25:39 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/27 11:25:39 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/27 11:25:39 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/27 11:25:39 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/27 11:25:39 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/27 12:16:39 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/27 12:16:39 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/27 12:16:39 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server122277471
2022/06/27 12:16:39 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server122277471\sketch
2022/06/27 12:16:39 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/27 12:16:39 IDE --> initialize 0 [93m locked[0m
2022/06/27 12:16:39 IDE --> initialize 0 [93m unlocked[0m
2022/06/27 12:16:39 INIT--- initializing workbench
2022/06/27 12:16:39 INIT--- [93m locked[0m
2022/06/27 12:16:39     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/27 12:16:39 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/27 12:16:39 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\090164274 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server122277471 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/27 12:16:39 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/27 12:16:40 INIT--- initializing workbench (done)
2022/06/27 12:16:40 INIT--- [93m unlocked[0m
2022/06/27 12:16:40 IDE --> initialized notif1 [93m read-locked[0m
2022/06/27 12:16:40 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/27 12:16:40 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/27 12:16:40 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/27 12:16:40 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/27 12:16:40 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/27 12:16:40 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/27 12:22:35 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/27 12:22:35 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/27 12:22:35 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server719995719
2022/06/27 12:22:35 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server719995719\sketch
2022/06/27 12:22:35 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/27 12:22:35 IDE --> initialize 0 [93m locked[0m
2022/06/27 12:22:35 IDE --> initialize 0 [93m unlocked[0m
2022/06/27 12:22:35 INIT--- initializing workbench
2022/06/27 12:22:35 INIT--- [93m locked[0m
2022/06/27 12:22:35     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/27 12:22:35 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/27 12:22:35 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\602200570 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server719995719 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/27 12:22:35 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/27 12:22:36 INIT--- initializing workbench (done)
2022/06/27 12:22:36 INIT--- [93m unlocked[0m
2022/06/27 12:22:36 IDE --> initialized notif1 [93m read-locked[0m
2022/06/27 12:22:36 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/27 12:22:36 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/27 12:22:36 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/27 12:22:36 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/27 12:22:36 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/27 12:22:36 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/27 12:30:02 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/27 12:30:02 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/27 12:30:02 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server716751063
2022/06/27 12:30:02 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server716751063\sketch
2022/06/27 12:30:02 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/27 12:30:02 IDE --> initialize 0 [93m locked[0m
2022/06/27 12:30:02 IDE --> initialize 0 [93m unlocked[0m
2022/06/27 12:30:02 INIT--- initializing workbench
2022/06/27 12:30:02 INIT--- [93m locked[0m
2022/06/27 12:30:02     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/27 12:30:02 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/27 12:30:02 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\651496010 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server716751063 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/27 12:30:02 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/27 12:30:04 INIT--- initializing workbench (done)
2022/06/27 12:30:04 INIT--- [93m unlocked[0m
2022/06/27 12:30:04 IDE --> initialized notif1 [93m read-locked[0m
2022/06/27 12:30:04 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/27 12:30:04 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/27 12:30:04 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/27 12:30:04 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/27 12:30:04 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/27 12:30:04 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/27 14:14:51 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/27 14:14:51 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/27 14:14:51 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server468191379
2022/06/27 14:14:51 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server468191379\sketch
2022/06/27 14:14:51 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/27 14:14:51 IDE --> initialize 0 [93m locked[0m
2022/06/27 14:14:51 IDE --> initialize 0 [93m unlocked[0m
2022/06/27 14:14:51 INIT--- initializing workbench
2022/06/27 14:14:51 INIT--- [93m locked[0m
2022/06/27 14:14:51     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/27 14:14:51 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/27 14:14:51 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\043109590 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server468191379 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/27 14:14:51 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/27 14:14:52 INIT--- initializing workbench (done)
2022/06/27 14:14:52 INIT--- [93m unlocked[0m
2022/06/27 14:14:52 IDE --> initialized notif1 [93m read-locked[0m
2022/06/27 14:14:52 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/27 14:14:52 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/27 14:14:52 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/27 14:14:52 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/27 14:14:52 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/27 14:14:52 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/27 15:03:52 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/27 15:03:52 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/27 15:03:52 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server616939203
2022/06/27 15:03:52 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server616939203\sketch
2022/06/27 15:03:52 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/27 15:03:52 IDE --> initialize 0 [93m locked[0m
2022/06/27 15:03:52 IDE --> initialize 0 [93m unlocked[0m
2022/06/27 15:03:52 INIT--- initializing workbench
2022/06/27 15:03:52 INIT--- [93m locked[0m
2022/06/27 15:03:52 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/27 15:03:52     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/27 15:03:52 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\614207302 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server616939203 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/27 15:03:52 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/27 15:03:53 INIT--- initializing workbench (done)
2022/06/27 15:03:53 INIT--- [93m unlocked[0m
2022/06/27 15:03:53 IDE --> initialized notif1 [93m read-locked[0m
2022/06/27 15:03:53 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/27 15:03:53 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/27 15:03:53 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/27 15:03:53 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/27 15:03:53 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/27 15:03:53 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/28 10:43:04 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/28 10:43:04 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/28 10:43:04 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server713063927
2022/06/28 10:43:04 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server713063927\sketch
2022/06/28 10:43:04 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/28 10:43:04 IDE --> initialize 0 [93m locked[0m
2022/06/28 10:43:04 IDE --> initialize 0 [93m unlocked[0m
2022/06/28 10:43:04 INIT--- initializing workbench
2022/06/28 10:43:04 INIT--- [93m locked[0m
2022/06/28 10:43:04 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/28 10:43:04     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/28 10:43:04 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\743278826 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server713063927 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/28 10:43:04 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/28 10:43:05 INIT--- initializing workbench (done)
2022/06/28 10:43:05 INIT--- [93m unlocked[0m
2022/06/28 10:43:05 IDE --> initialized notif1 [93m read-locked[0m
2022/06/28 10:43:05 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/28 10:43:05 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/28 10:43:05 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/28 10:43:05 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/28 10:43:05 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/28 10:43:05 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/28 10:44:03 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED\inols.log
2022/06/28 10:44:03 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/28 10:44:03 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server286746067
2022/06/28 10:44:03 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server286746067\sketch
2022/06/28 10:44:03 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/28 10:44:03 IDE --> initialize 0 [93m locked[0m
2022/06/28 10:44:03 IDE --> initialize 0 [93m unlocked[0m
2022/06/28 10:44:03 INIT--- initializing workbench
2022/06/28 10:44:03 INIT--- [93m locked[0m
2022/06/28 10:44:03     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_8_WIFI%20Web%20Servers%20LED/WIFI%20Web%20Servers%20LED)
2022/06/28 10:44:03 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/28 10:44:03 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\424940822 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server286746067 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_8_WIFI Web Servers LED\WIFI Web Servers LED
2022/06/28 10:44:03 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/28 10:44:05 INIT--- initializing workbench (done)
2022/06/28 10:44:05 INIT--- [93m unlocked[0m
2022/06/28 10:44:05 IDE --> initialized notif1 [93m read-locked[0m
2022/06/28 10:44:05 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/28 10:44:05 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/28 10:44:05 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/28 10:44:05 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/28 10:44:05 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/28 10:44:05 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m
