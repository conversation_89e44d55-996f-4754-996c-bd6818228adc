/*
 * Audio Manager Implementation - Minimal Version
 */

#include "audio_manager.h"

AudioManager::AudioManager() : 
  initialized(false),
  enabled(true),
  volume(128) {
}

AudioManager::~AudioManager() {
  if (initialized) {
    noTone(SPEAKER_PIN);
  }
}

bool AudioManager::begin() {
  Serial.println("Initializing audio manager...");
  
  // Configure speaker pin
  pinMode(SPEAKER_PIN, OUTPUT);
  digitalWrite(SPEAKER_PIN, LOW);
  
  initialized = true;
  
  Serial.println("Audio manager initialized");
  return true;
}

void AudioManager::reset() {
  noTone(SPEAKER_PIN);
}

void AudioManager::setVolume(uint8_t vol) {
  volume = CLAMP(vol, 0, 255);
}

void AudioManager::playStartupSound() {
  if (!enabled || !initialized) return;
  
  tone(SPEAKER_PIN, 800, 150);
  delay(200);
  tone(SPEAKER_PIN, 1000, 150);
  delay(200);
  tone(SPEAKER_PIN, 1200, 150);
  delay(200);
  tone(SPEAKER_PIN, 1500, 300);
}

void AudioManager::playSuccessSound() {
  if (!enabled || !initialized) return;
  
  tone(SPEAKER_PIN, SOUND_SUCCESS, SOUND_DURATION_SHORT);
}

void AudioManager::playErrorSound() {
  if (!enabled || !initialized) return;
  
  tone(SPEAKER_PIN, SOUND_ERROR, SOUND_DURATION_MEDIUM);
  delay(SOUND_DURATION_MEDIUM + 100);
  tone(SPEAKER_PIN, SOUND_ERROR, SOUND_DURATION_MEDIUM);
}

void AudioManager::playWarningSound() {
  if (!enabled || !initialized) return;
  
  tone(SPEAKER_PIN, 800, 200);
  delay(250);
  tone(SPEAKER_PIN, 600, 200);
  delay(250);
  tone(SPEAKER_PIN, 800, 200);
}

void AudioManager::playClickSound() {
  if (!enabled || !initialized) return;
  
  tone(SPEAKER_PIN, SOUND_CLICK, 50);
}

void AudioManager::playPunchSound(bool punchIn) {
  if (!enabled || !initialized) return;
  
  if (punchIn) {
    tone(SPEAKER_PIN, SOUND_PUNCH_IN, SOUND_DURATION_MEDIUM);
    delay(SOUND_DURATION_MEDIUM + 50);
    tone(SPEAKER_PIN, SOUND_PUNCH_IN + 200, SOUND_DURATION_SHORT);
  } else {
    tone(SPEAKER_PIN, SOUND_PUNCH_OUT, SOUND_DURATION_MEDIUM);
    delay(SOUND_DURATION_MEDIUM + 50);
    tone(SPEAKER_PIN, SOUND_PUNCH_OUT - 200, SOUND_DURATION_SHORT);
  }
}

void AudioManager::playBeep(int frequency, int duration) {
  if (!enabled || !initialized) return;
  
  tone(SPEAKER_PIN, frequency, duration);
}

void AudioManager::testSpeaker() {
  if (!initialized) return;
  
  Serial.println("Testing speaker...");
  
  tone(SPEAKER_PIN, 440, 500);  // A note
  delay(600);
  tone(SPEAKER_PIN, 880, 500);  // A note (octave higher)
  delay(600);
  tone(SPEAKER_PIN, 440, 500);  // A note
  
  Serial.println("Speaker test complete");
}
