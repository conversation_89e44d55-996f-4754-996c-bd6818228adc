/*
 * Audio Manager Implementation
 * Handles speaker and sound effects
 */

#include "audio_manager.h"

AudioManager::AudioManager() : 
  initialized(false),
  enabled(true),
  volume(128) {
  
  currentSound.playing = false;
  currentSound.frequency = 0;
  currentSound.duration = 0;
  currentSound.startTime = 0;
}

AudioManager::~AudioManager() {
  if (initialized) {
    stopTone();
  }
}

bool AudioManager::begin() {
  Serial.println("Initializing audio manager...");
  
  // Configure speaker pin
  pinMode(SPEAKER_PIN, OUTPUT);
  digitalWrite(SPEAKER_PIN, LOW);
  
  initialized = true;
  
  Serial.println("Audio manager initialized");
  return true;
}

void AudioManager::reset() {
  stopTone();
  clearQueue();
  currentSound.playing = false;
}

void AudioManager::setVolume(uint8_t vol) {
  volume = CLAMP(vol, 0, 255);
}

void AudioManager::playStartupSound() {
  if (!enabled || !initialized) return;
  
  // Play ascending tone sequence
  queueSound(800, 150);
  queueSound(1000, 150);
  queueSound(1200, 150);
  queueSound(1500, 300);
}

void AudioManager::playSuccessSound() {
  if (!enabled || !initialized) return;
  
  queueSound(SOUND_SUCCESS, SOUND_DURATION_SHORT);
}

void AudioManager::playErrorSound() {
  if (!enabled || !initialized) return;
  
  // Play error pattern: low-high-low
  queueSound(SOUND_ERROR, SOUND_DURATION_MEDIUM);
  queueSound(0, 100); // Silence
  queueSound(SOUND_ERROR, SOUND_DURATION_MEDIUM);
}

void AudioManager::playWarningSound() {
  if (!enabled || !initialized) return;
  
  // Play warning pattern: alternating tones
  queueSound(800, 200);
  queueSound(600, 200);
  queueSound(800, 200);
}

void AudioManager::playClickSound() {
  if (!enabled || !initialized) return;
  
  queueSound(SOUND_CLICK, 50);
}

void AudioManager::playPunchSound(bool punchIn) {
  if (!enabled || !initialized) return;
  
  if (punchIn) {
    // Ascending tone for punch in
    queueSound(SOUND_PUNCH_IN, SOUND_DURATION_MEDIUM);
    queueSound(SOUND_PUNCH_IN + 200, SOUND_DURATION_SHORT);
  } else {
    // Descending tone for punch out
    queueSound(SOUND_PUNCH_OUT, SOUND_DURATION_MEDIUM);
    queueSound(SOUND_PUNCH_OUT - 200, SOUND_DURATION_SHORT);
  }
}

void AudioManager::playBeep(int frequency, int duration) {
  if (!enabled || !initialized) return;
  
  queueSound(frequency, duration);
}

void AudioManager::playMelody(int* frequencies, int* durations, int length) {
  if (!enabled || !initialized) return;
  
  for (int i = 0; i < length; i++) {
    queueSound(frequencies[i], durations[i]);
  }
}

void AudioManager::playChime() {
  if (!enabled || !initialized) return;
  
  // Pleasant chime sequence
  int frequencies[] = {523, 659, 784, 1047}; // C, E, G, C
  int durations[] = {200, 200, 200, 400};
  
  playMelody(frequencies, durations, 4);
}

void AudioManager::playAlarm() {
  if (!enabled || !initialized) return;
  
  // Urgent alarm pattern
  for (int i = 0; i < 5; i++) {
    queueSound(1000, 200);
    queueSound(0, 100); // Silence
  }
}

void AudioManager::playNotification() {
  if (!enabled || !initialized) return;
  
  // Gentle notification
  queueSound(800, 150);
  queueSound(1000, 150);
}

void AudioManager::queueSound(int frequency, int duration) {
  if (!enabled || !initialized) return;
  
  SoundEvent event;
  event.frequency = frequency;
  event.duration = duration;
  event.playing = false;
  event.startTime = 0;
  
  soundQueue.push_back(event);
}

void AudioManager::clearQueue() {
  soundQueue.clear();
  stopTone();
  currentSound.playing = false;
}

bool AudioManager::isPlaying() const {
  return currentSound.playing || !soundQueue.empty();
}

void AudioManager::update() {
  if (!enabled || !initialized) return;
  
  unsigned long currentTime = millis();
  
  // Check if current sound has finished
  if (currentSound.playing) {
    if (currentTime - currentSound.startTime >= currentSound.duration) {
      stopTone();
      currentSound.playing = false;
    }
  }
  
  // Start next sound in queue
  if (!currentSound.playing && !soundQueue.empty()) {
    currentSound = soundQueue[0];
    soundQueue.erase(soundQueue.begin());
    
    if (currentSound.frequency > 0) {
      playTone(currentSound.frequency, currentSound.duration);
    }
    
    currentSound.startTime = currentTime;
    currentSound.playing = true;
  }
}

void AudioManager::testSpeaker() {
  if (!initialized) return;
  
  Serial.println("Testing speaker...");
  
  // Play test sequence
  playBeep(440, 500);  // A note
  delay(600);
  playBeep(880, 500);  // A note (octave higher)
  delay(600);
  playBeep(440, 500);  // A note
  
  Serial.println("Speaker test complete");
}

void AudioManager::playTestTones() {
  if (!initialized) return;
  
  Serial.println("Playing test tone sequence...");
  
  // Play frequency sweep
  for (int freq = 200; freq <= 2000; freq += 200) {
    queueSound(freq, 200);
  }
}

// Private methods

void AudioManager::playTone(int frequency, int duration) {
  if (!enabled || !initialized || frequency <= 0) return;
  
  // Use tone() function with volume adjustment
  // Note: ESP32 tone() doesn't support volume directly,
  // so we use PWM with duty cycle adjustment for volume control
  
  int adjustedFreq = frequency;
  
  // Apply volume by adjusting duty cycle (simplified approach)
  if (volume < 255) {
    // For lower volumes, we could implement PWM-based volume control
    // This is a simplified implementation
    tone(SPEAKER_PIN, adjustedFreq);
  } else {
    tone(SPEAKER_PIN, adjustedFreq);
  }
}

void AudioManager::stopTone() {
  if (!initialized) return;
  
  noTone(SPEAKER_PIN);
  digitalWrite(SPEAKER_PIN, LOW);
}
