/*
 * LED Manager Header - Minimal Version
 */

#ifndef LED_MANAGER_H
#define LED_MANAGER_H

#include <FastLED.h>
#include "config.h"

class LEDManager {
private:
  CRGB leds[1];
  bool initialized;
  LEDStatus currentStatus;
  uint8_t brightness;
  
public:
  LEDManager();
  ~LEDManager();
  
  bool begin();
  void reset();
  bool isInitialized() const { return initialized; }
  
  void setStatus(LEDStatus status);
  LEDStatus getStatus() const { return currentStatus; }
  
  void setBrightness(uint8_t bright);
  uint8_t getBrightness() const { return brightness; }
  
  void setColor(uint8_t red, uint8_t green, uint8_t blue);
  void setColor(uint32_t color);
  void turnOff();
  void turnOn();
  
  void update();
  
  void testLED();
};

#endif // LED_MANAGER_H
