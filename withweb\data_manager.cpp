/*
 * Data Manager Implementation - Minimal Version
 */

#include "data_manager.h"

DataManager::DataManager() : 
  sdInitialized(false),
  spiffsInitialized(false) {
}

DataManager::~DataManager() {
}

bool DataManager::begin() {
  Serial.println("Initializing data manager...");
  
  // Initialize SPIFFS first
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS initialization failed!");
    spiffsInitialized = false;
  } else {
    spiffsInitialized = true;
    Serial.println("SPIFFS initialized successfully");
  }
  
  // Initialize SD card
  if (!SD.begin(SD_CS)) {
    Serial.println("SD card initialization failed!");
    sdInitialized = false;
  } else {
    sdInitialized = true;
    Serial.println("SD card initialized successfully");
  }
  
  // At least one storage system must be available
  if (!sdInitialized && !spiffsInitialized) {
    Serial.println("No storage systems available!");
    return false;
  }
  
  Serial.println("Data manager initialized");
  return true;
}

void DataManager::reset() {
  // Reset implementation
}

bool DataManager::writeFile(String path, String content) {
  return writeFile(path, (const uint8_t*)content.c_str(), content.length());
}

bool DataManager::writeFile(String path, const uint8_t* data, size_t length) {
  fs::File file;
  bool success = false;
  
  // Try SD card first, then SPIFFS
  if (sdInitialized) {
    file = SD.open(path, FILE_WRITE);
    if (file) {
      size_t written = file.write(data, length);
      file.close();
      success = (written == length);
    }
  } else if (spiffsInitialized) {
    file = SPIFFS.open(path, FILE_WRITE);
    if (file) {
      size_t written = file.write(data, length);
      file.close();
      success = (written == length);
    }
  }
  
  if (success) {
    Serial.println("File written: " + path);
  } else {
    Serial.println("Failed to write file: " + path);
  }
  
  return success;
}

String DataManager::readFile(String path) {
  fs::File file;
  String content = "";
  
  // Try SD card first, then SPIFFS
  if (sdInitialized) {
    file = SD.open(path, FILE_READ);
  } else if (spiffsInitialized) {
    file = SPIFFS.open(path, FILE_READ);
  }
  
  if (file) {
    while (file.available()) {
      content += (char)file.read();
    }
    file.close();
    Serial.println("File read: " + path);
  } else {
    Serial.println("Failed to read file: " + path);
  }
  
  return content;
}

bool DataManager::deleteFile(String path) {
  bool success = false;
  
  if (sdInitialized) {
    success = SD.remove(path);
  } else if (spiffsInitialized) {
    success = SPIFFS.remove(path);
  }
  
  if (success) {
    Serial.println("File deleted: " + path);
  } else {
    Serial.println("Failed to delete file: " + path);
  }
  
  return success;
}

bool DataManager::fileExists(String path) {
  if (sdInitialized) {
    return SD.exists(path);
  } else if (spiffsInitialized) {
    return SPIFFS.exists(path);
  }
  
  return false;
}

size_t DataManager::getFileSize(String path) {
  fs::File file;
  
  if (sdInitialized) {
    file = SD.open(path, FILE_READ);
  } else if (spiffsInitialized) {
    file = SPIFFS.open(path, FILE_READ);
  }
  
  if (file) {
    size_t size = file.size();
    file.close();
    return size;
  }
  
  return 0;
}

bool DataManager::writeJSON(String path, JsonDocument& doc) {
  String jsonString;
  serializeJson(doc, jsonString);
  return writeFile(path, jsonString);
}

bool DataManager::readJSON(String path, JsonDocument& doc) {
  String content = readFile(path);
  if (content.length() == 0) {
    return false;
  }
  
  DeserializationError error = deserializeJson(doc, content);
  if (error) {
    Serial.println("JSON parsing failed: " + String(error.c_str()));
    return false;
  }
  
  return true;
}

bool DataManager::appendCSV(String path, String row) {
  fs::File file;
  
  if (sdInitialized) {
    file = SD.open(path, FILE_APPEND);
  } else if (spiffsInitialized) {
    file = SPIFFS.open(path, FILE_APPEND);
  }
  
  if (file) {
    file.println(row);
    file.close();
    return true;
  }
  
  return false;
}

bool DataManager::logAttendance(const AttendanceRecord& record) {
  // Create CSV row
  String csvRow = String(record.employeeId) + "," +
                  record.timestamp + "," +
                  record.action + "," +
                  record.location + "," +
                  (record.synced ? "1" : "0");
  
  // Append to daily attendance file
  String filename = "/attendance_" + getCurrentDateString() + ".csv";
  
  // Create header if file doesn't exist
  if (!fileExists(filename)) {
    String header = "EmployeeID,Timestamp,Action,Location,Synced";
    if (!writeFile(filename, header + "\n")) {
      return false;
    }
  }
  
  return appendCSV(filename, csvRow);
}

bool DataManager::saveConfig(const SystemConfig& config) {
  DynamicJsonDocument doc(JSON_BUFFER_SIZE);
  
  doc["deviceName"] = config.deviceName;
  doc["wifiSSID"] = config.wifiSSID;
  doc["wifiPassword"] = config.wifiPassword;
  doc["ntpServer"] = config.ntpServer;
  doc["timezone"] = config.timezone;
  doc["adminPin"] = config.adminPin;
  doc["autoBrightness"] = config.autoBrightness;
  doc["soundEnabled"] = config.soundEnabled;
  doc["displayTimeout"] = config.displayTimeout;
  doc["sleepTimeout"] = config.sleepTimeout;
  doc["webServerEnabled"] = config.webServerEnabled;
  doc["webServerPort"] = config.webServerPort;
  
  return writeJSON(CONFIG_FILE, doc);
}

bool DataManager::loadConfig(SystemConfig& config) {
  DynamicJsonDocument doc(JSON_BUFFER_SIZE);
  
  if (!readJSON(CONFIG_FILE, doc)) {
    // Create default config if file doesn't exist
    config.deviceName = DEFAULT_DEVICE_NAME;
    config.wifiSSID = "";
    config.wifiPassword = "";
    config.ntpServer = DEFAULT_NTP_SERVER;
    config.timezone = DEFAULT_TIMEZONE;
    config.adminPin = DEFAULT_ADMIN_PIN;
    config.autoBrightness = true;
    config.soundEnabled = true;
    config.displayTimeout = ACTIVITY_TIMEOUT;
    config.sleepTimeout = SLEEP_TIMEOUT;
    config.webServerEnabled = true;
    config.webServerPort = WEB_SERVER_PORT;
    
    // Save default config
    saveConfig(config);
    return true;
  }
  
  config.deviceName = doc["deviceName"] | DEFAULT_DEVICE_NAME;
  config.wifiSSID = doc["wifiSSID"] | "";
  config.wifiPassword = doc["wifiPassword"] | "";
  config.ntpServer = doc["ntpServer"] | DEFAULT_NTP_SERVER;
  config.timezone = doc["timezone"] | DEFAULT_TIMEZONE;
  config.adminPin = doc["adminPin"] | DEFAULT_ADMIN_PIN;
  config.autoBrightness = doc["autoBrightness"] | true;
  config.soundEnabled = doc["soundEnabled"] | true;
  config.displayTimeout = doc["displayTimeout"] | ACTIVITY_TIMEOUT;
  config.sleepTimeout = doc["sleepTimeout"] | SLEEP_TIMEOUT;
  config.webServerEnabled = doc["webServerEnabled"] | true;
  config.webServerPort = doc["webServerPort"] | WEB_SERVER_PORT;
  
  return true;
}

uint64_t DataManager::getTotalSpace() {
  if (sdInitialized) {
    return SD.totalBytes();
  } else if (spiffsInitialized) {
    return SPIFFS.totalBytes();
  }
  
  return 0;
}

uint64_t DataManager::getUsedSpace() {
  if (sdInitialized) {
    return SD.usedBytes();
  } else if (spiffsInitialized) {
    return SPIFFS.usedBytes();
  }
  
  return 0;
}

uint64_t DataManager::getFreeSpace() {
  return getTotalSpace() - getUsedSpace();
}

String DataManager::formatBytes(uint64_t bytes) {
  if (bytes < 1024) return String(bytes) + " B";
  if (bytes < 1024 * 1024) return String(bytes / 1024.0, 1) + " KB";
  if (bytes < 1024 * 1024 * 1024) return String(bytes / (1024.0 * 1024.0), 1) + " MB";
  return String(bytes / (1024.0 * 1024.0 * 1024.0), 1) + " GB";
}

String DataManager::getCurrentDateString() {
  // Simple date string for filenames (YYYY-MM-DD)
  time_t now;
  time(&now);
  struct tm* timeinfo = localtime(&now);
  
  char buffer[16];
  strftime(buffer, sizeof(buffer), "%Y-%m-%d", timeinfo);
  
  return String(buffer);
}

String DataManager::getCurrentTimeString() {
  // Simple time string for logs (HH:MM:SS)
  time_t now;
  time(&now);
  struct tm* timeinfo = localtime(&now);
  
  char buffer[16];
  strftime(buffer, sizeof(buffer), "%H:%M:%S", timeinfo);
  
  return String(buffer);
}
