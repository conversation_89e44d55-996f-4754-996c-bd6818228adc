/* The font vlw file can be converted to a byte array using:

   https://tomeko.net/online_tools/file_to_hex.php?lang=en

   Paste the byte array into a sketch tab and add two lines
   at the start with a unique font name:

                const uint8_t  fontName[] PROGMEM = {

   At the end add:

        };

   See example below. Include the tab in the main sketch, e.g.:

        #include "NotoSansBold36.h"
*/


const uint8_t  NotoSansBold36[] PROGMEM = {
0x00, 0x00, 0x00, 0x5E, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 0x00, 0x1B, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0D, 
0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x23, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x17, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 
0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x27, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 
0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x00, 0x00, 0x00, 0x20, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x12, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x2B, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x05, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2D, 0x00, 0x00, 0x00, 0x05, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x07, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x2F, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1B, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x35, 0x00, 0x00, 0x00, 0x1B, 
0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x37, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x00, 0x00, 0x00, 0x1B, 
0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3A, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x07, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3B, 0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x17, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x12, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x11, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x00, 0x1D, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x43, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x17, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x47, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x0C, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x1A, 0xFF, 0xFF, 0xFF, 0xFD, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4B, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x18, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4D, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x17, 
0x00, 0x00, 0x00, 0x1D, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4F, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x00, 0x1D, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x51, 0x00, 0x00, 0x00, 0x22, 
0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x00, 0x1D, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x53, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x54, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x00, 0x00, 0x00, 0x1B, 
0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x56, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x18, 
0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x23, 0x00, 0x00, 0x00, 0x23, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5A, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5B, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0C, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5C, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x1A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0x00, 0x00, 0x00, 0x20, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5E, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5F, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0F, 
0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x00, 0x00, 0x00, 0x1D, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 
0x00, 0x00, 0x00, 0x1D, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x65, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x67, 0x00, 0x00, 0x00, 0x1D, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x17, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 
0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0x00, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x0C, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x1C, 0xFF, 0xFF, 0xFF, 0xFD, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6B, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x16, 
0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6C, 
0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x23, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6F, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x16, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 
0x00, 0x00, 0x00, 0x1D, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x00, 0x00, 0x00, 0x1D, 
0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x0E, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x73, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x12, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 
0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x18, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x75, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x15, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x1F, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 
0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x14, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0x00, 0x00, 0x00, 0x1D, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7A, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x7B, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x0E, 
0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7C, 
0x00, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x1C, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0x00, 0x00, 0x00, 0x20, 
0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x12, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x9F, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x2A, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0E, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFA, 0x00, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 
0x00, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xCE, 0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x59, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x4A, 0xFF, 0xFF, 
0xFF, 0xFF, 0x9F, 0x00, 0x46, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 0x00, 0x2E, 0xFF, 0xFF, 0xFF, 0xFF, 
0x90, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 
0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x61, 0x63, 0x63, 0x63, 0x1F, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x6A, 0x7D, 
0x3D, 0x00, 0x00, 0x57, 0xFC, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x0C, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x0A, 0x4A, 
0xF8, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x19, 0x5B, 0x5F, 0x30, 0x00, 0x00, 0x6E, 0xAE, 0xAE, 
0xAE, 0xAE, 0x2C, 0x00, 0x2E, 0xAE, 0xAE, 0xAE, 0xAE, 0x6E, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0x15, 
0x00, 0x1D, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0x04, 0x00, 0x08, 0xFF, 
0xFF, 0xFF, 0xFF, 0x74, 0x5B, 0xFF, 0xFF, 0xFF, 0xFA, 0x00, 0x00, 0x00, 0xFC, 0xFF, 0xFF, 0xFF, 
0x5B, 0x52, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0xF4, 0xFF, 0xFF, 0xFF, 0x50, 0x48, 0xFF, 
0xFF, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0x48, 0x2C, 0xFF, 0xFF, 0xFF, 0xB2, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0x2E, 0x08, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 
0xAA, 0xFF, 0xFF, 0xFF, 0x08, 0x00, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x9F, 0xFF, 0xFF, 
0xFF, 0x00, 0x00, 0x96, 0x9B, 0x9B, 0x59, 0x00, 0x00, 0x00, 0x5B, 0x9B, 0x9B, 0x94, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xA7, 0xA7, 0xA7, 0x33, 0x00, 0x00, 0x28, 0xA7, 
0xA7, 0xA7, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 
0xFF, 0xFF, 0x15, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x92, 0xFF, 0xFF, 0xFF, 
0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0xFF, 0xFF, 0xFF, 0xAE, 
0x00, 0x00, 0x00, 0xB8, 0xFF, 0xFF, 0xFC, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0xF6, 0xFF, 0xFF, 0xCE, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 
0x1F, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xAA, 0xFF, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x90, 0x94, 0x94, 0x94, 0x94, 0xE7, 0xFF, 0xFF, 0xFF, 0x94, 0x94, 0x94, 0xC1, 0xFF, 
0xFF, 0xFF, 0xB6, 0x94, 0x94, 0x94, 0x3B, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 
0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x3B, 0x3D, 
0x3D, 0x3D, 0xB8, 0xFF, 0xFF, 0xFF, 0x68, 0x3D, 0x3D, 0x7D, 0xFF, 0xFF, 0xFF, 0x96, 0x3D, 0x3D, 
0x3D, 0x3D, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFC, 0x02, 0x00, 0x00, 
0x81, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0xFA, 0xFF, 0xFF, 0xCE, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0xF2, 0xFF, 
0xFF, 0xE3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xB4, 0xB4, 0xB4, 0xB4, 0xCC, 0xFF, 0xFF, 
0xFF, 0xD6, 0xB4, 0xB4, 0xB6, 0xFF, 0xFF, 0xFF, 0xE7, 0xB4, 0xB4, 0xB4, 0xB4, 0x35, 0x00, 0x44, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x3D, 0xEB, 0xEB, 
0xEB, 0xEB, 0xFF, 0xFF, 0xFF, 0xF8, 0xEB, 0xEB, 0xEB, 0xF8, 0xFF, 0xFF, 0xFF, 0xEB, 0xEB, 0xEB, 
0xEB, 0xEB, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 
0xEE, 0xFF, 0xFF, 0xF4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 
0xFF, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x11, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 
0xFF, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAC, 0xFF, 0xFF, 
0xFF, 0x0E, 0x00, 0x00, 0x79, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xDF, 0xFF, 0xFF, 0xEE, 0x00, 0x00, 0x00, 0xAA, 0xFF, 0xFF, 0xFF, 0x28, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 
0x00, 0x00, 0xDD, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x41, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0x50, 0x2E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xEB, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xEB, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1F, 0x59, 0x92, 0xF8, 0xFF, 0xDA, 0xA3, 0x8A, 0x57, 0x2A, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x41, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE7, 0x92, 0x2C, 0x00, 0x00, 0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 0x15, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD8, 0xF8, 0xFF, 0xDA, 0xB6, 0xF6, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x55, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0xEB, 0xFF, 0x94, 0x00, 0x00, 0x22, 0x68, 0xC3, 0x55, 0x00, 0x00, 
0x61, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0xEB, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x26, 0x00, 0xEB, 0xFF, 0x94, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x6E, 0xEB, 0xFF, 
0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xE7, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 0x5D, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x19, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x72, 0x02, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD0, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xBD, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xEB, 0xFF, 0x96, 0x6A, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0x94, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x28, 0x2C, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0x94, 0x00, 0x1B, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x26, 0x5D, 0xFF, 0xC7, 0x74, 0x2A, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0x94, 0x06, 
0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x02, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0xB2, 0xA1, 0xF8, 
0xFF, 0xDF, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x19, 0x00, 0x5D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x30, 0x00, 0x00, 
0x0A, 0x66, 0xBB, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x7F, 0x0C, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x39, 0x57, 0x66, 0x94, 0xF8, 0xFF, 0xBD, 0x57, 0x28, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 
0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xEB, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x06, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x85, 0xDD, 0xFF, 0xFF, 0xF0, 0x9F, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0xA5, 0xAE, 0xAE, 0xAE, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x15, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x74, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0xEE, 0xFF, 0xFF, 0xFF, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFF, 
0xFF, 0xFF, 0xFF, 0xD4, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x8A, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 
0xFF, 0xFA, 0x13, 0x00, 0x9D, 0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFC, 
0xFF, 0xFF, 0xF6, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 
0xB6, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0x11, 0x00, 0x00, 0x00, 0x00, 0xAA, 0xFF, 0xFF, 
0xFF, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC9, 0xFF, 0xFF, 0xFF, 0x9F, 
0x00, 0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xE7, 
0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 
0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 0x6A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 
0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 0x55, 0xFF, 0xFF, 0xFF, 0xD6, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x26, 
0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x02, 0xDD, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x50, 0xFF, 
0xFF, 0xFF, 0xFF, 0x11, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x3F, 0x9D, 0xB2, 0xB4, 
0xA5, 0x61, 0x02, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xFF, 0xFF, 0xFA, 0x13, 0x00, 0x9B, 0xFF, 0xFF, 
0xFF, 0xE5, 0x00, 0x11, 0xEE, 0xFF, 0xFF, 0xFF, 0x2E, 0x02, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD4, 0x1B, 0x00, 0x00, 0x1D, 0xFC, 0xFF, 0xFF, 0xFF, 0xD0, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 
0x8E, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD4, 0x04, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x1D, 
0x22, 0xFA, 0xFF, 0xFF, 0xF8, 0x1B, 0x15, 0xFA, 0xFF, 0xFF, 0xFF, 0xFC, 0xF4, 0xFF, 0xFF, 0xFF, 
0xFF, 0x6C, 0x00, 0x00, 0x0C, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x55, 0x00, 0xA7, 
0xFF, 0xFF, 0xFF, 0x81, 0x00, 0x68, 0xFF, 0xFF, 0xFF, 0xFF, 0x35, 0x02, 0xC5, 0xFF, 0xFF, 0xFF, 
0xCE, 0x00, 0x00, 0x00, 0x06, 0x7B, 0xD6, 0xFF, 0xFF, 0xF6, 0xA7, 0x30, 0x00, 0x30, 0xFF, 0xFF, 
0xFF, 0xE7, 0x0C, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFC, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 
0x6A, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xD6, 0x00, 
0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0x99, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xDA, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 
0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 
0xD0, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x17, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xEB, 0xFF, 0xFF, 0xFF, 0x2E, 0x00, 0x00, 0x00, 0x00, 0xAE, 
0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x88, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x85, 0xFF, 
0xFF, 0xFF, 0xEB, 0x02, 0x00, 0x7D, 0xFF, 0xFF, 0xFF, 0xF6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1D, 0xF8, 0xFF, 0xFF, 0xF8, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0xFF, 0xFF, 
0xFF, 0xFF, 0x9B, 0x5F, 0xEE, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA3, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0xFF, 0xFF, 0xFF, 0xE7, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xE9, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 
0xFF, 0xFF, 0x6E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xBF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xEB, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x48, 0x4C, 
0x37, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x6E, 0xBB, 0xF6, 0xFF, 0xFF, 0xF8, 0xC9, 0x85, 0x1F, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0xE7, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x7B, 0x1B, 0x13, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0xAC, 0xFF, 
0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 
0x00, 0x15, 0xEE, 0xFF, 0xFF, 0xFF, 0xFC, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x1F, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF2, 0x83, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE9, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x22, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0x1D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xC9, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xB4, 0xB4, 0xB4, 0xB4, 
0x94, 0x00, 0x00, 0x00, 0x3D, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x28, 
0x00, 0x00, 0x00, 0x00, 0x04, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x00, 0x00, 0x2A, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x2E, 0x00, 0x00, 0x00, 0x59, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x00, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x3D, 0xF2, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x37, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 
0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x37, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 
0x39, 0x35, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3F, 0x00, 0x00, 0x00, 0x37, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xDA, 0x02, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x37, 
0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x00, 0x94, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAC, 0x00, 0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x35, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x13, 0x00, 0x00, 
0x00, 0x00, 0x24, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x5F, 0x2C, 0x19, 0x4A, 0x79, 0xDA, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x00, 0x13, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x66, 0x00, 0x00, 
0x00, 0x00, 0x15, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0x85, 0x3D, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 0xB4, 
0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x7B, 0x15, 0x00, 0x00, 0x2E, 0xE9, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x30, 0x4C, 0x50, 0x50, 0x46, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xAE, 
0xAE, 0xAE, 0xAE, 0x2C, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0x15, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0x04, 
0x5B, 0xFF, 0xFF, 0xFF, 0xFA, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x48, 0xFF, 0xFF, 0xFF, 
0xCC, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x08, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0xFF, 
0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x96, 0x9B, 0x9B, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 
0xAE, 0xAE, 0xAE, 0x94, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFA, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 
0x00, 0x02, 0xD4, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFC, 
0x26, 0x00, 0x00, 0x00, 0x0C, 0xEE, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x74, 0xFF, 
0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x00, 0x00, 0x00, 0xDA, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 
0x00, 0x35, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 
0x2A, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFA, 
0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 
0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0xFF, 
0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00, 0x00, 0x00, 
0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 
0x00, 0x00, 0x00, 0x00, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0x26, 0x00, 0x00, 0x00, 0x00, 0x61, 0xFF, 
0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 
0x00, 0x00, 0x39, 0xFF, 0xFF, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFC, 0xFF, 0xFF, 
0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0xFF, 
0x6A, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE3, 0xFF, 0xFF, 0xFF, 0xC5, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0x00, 0x00, 0x15, 0xF4, 0xFF, 0xFF, 0xFF, 0x96, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFA, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x06, 
0xD8, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0xFA, 0xFF, 0xFF, 0xFF, 0x3F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xA7, 0xA7, 0xA7, 0x8C, 0x70, 0xAE, 0xAE, 0xAE, 0x81, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1F, 0xF4, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 
0xFF, 0xFF, 0xFF, 0xF2, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x06, 0xE5, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xF4, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFC, 0x15, 0x00, 
0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 0x02, 0xF6, 0xFF, 
0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 0xF8, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 
0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0x00, 
0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFA, 0xFF, 0xFF, 0xFF, 0xAC, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xF4, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF2, 
0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 
0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x19, 0xFF, 0xFF, 0xFF, 
0xFF, 0x99, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 
0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0xAA, 0xFF, 0xFF, 0xFF, 0xFA, 0x04, 
0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 
0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x92, 0xFF, 0xFF, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 
0x06, 0xF2, 0xFF, 0xFF, 0xFF, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x39, 
0x00, 0x00, 0x00, 0x04, 0xE1, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x77, 0xFF, 0xFF, 
0xFF, 0xF4, 0x1F, 0x00, 0x00, 0x00, 0x1B, 0xF2, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 
0x68, 0xA7, 0xA7, 0xA7, 0x7B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x2A, 0x57, 0x57, 0x57, 0x57, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x33, 0x81, 0x37, 0x00, 0x00, 0x00, 0x00, 0xFA, 0xFF, 0xFF, 0x96, 0x00, 
0x00, 0x00, 0x06, 0x50, 0x92, 0x00, 0x5B, 0xFF, 0xFF, 0xEB, 0xA5, 0x57, 0x11, 0xE1, 0xFF, 0xFF, 
0x5F, 0x2C, 0x74, 0xBD, 0xFA, 0xFF, 0xFF, 0x06, 0x92, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xF8, 
0xFF, 0xFF, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x90, 0xB6, 0xE1, 0xF6, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xF4, 0xE1, 0xB6, 0x44, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3D, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0x88, 0xF2, 0xFF, 0xFF, 0xFF, 0x44, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFA, 0x13, 0x83, 0xFF, 0xFF, 
0xFF, 0xE9, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xFA, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x11, 
0xF4, 0xFF, 0xFF, 0xFF, 0xC3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xF2, 0xFF, 0xFF, 0xFF, 0x28, 
0x00, 0x00, 0x83, 0xFF, 0xFF, 0xFF, 0xD6, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x83, 0xF4, 
0xB8, 0x00, 0x00, 0x00, 0x13, 0xF4, 0xE1, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x13, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x3B, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x63, 0x63, 0x63, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 
0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 
0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xA1, 0xA1, 0xA1, 
0xA1, 0xA1, 0xA1, 0xDD, 0xFF, 0xFF, 0xFF, 0xBB, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x63, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x1B, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0xBB, 0xFF, 0xFF, 0xFF, 0x77, 
0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 
0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 
0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x04, 0x3D, 0x3D, 0x3D, 0x3D, 0x33, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 
0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0xBB, 
0xFF, 0xFF, 0xFF, 0xFA, 0x0A, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x2A, 0xFF, 0xFF, 0xFF, 
0xFF, 0x44, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x74, 0x00, 
0x00, 0x81, 0xA1, 0xA1, 0xA1, 0x0E, 0x00, 0x00, 0xD8, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 
0xE5, 0x83, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0xF2, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 
0x8C, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x55, 0x00, 0x26, 0x6A, 0x7D, 0x3D, 0x00, 
0x00, 0x57, 0xFC, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0xF4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x0A, 0x4A, 0xF8, 0xFF, 
0xFF, 0xFF, 0x83, 0x00, 0x00, 0x19, 0x5B, 0x5F, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x37, 0xAE, 0xAE, 0xAE, 0xAE, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0xF4, 0xFF, 0xFF, 0xFF, 0xD8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 
0xFF, 0xFF, 0xFF, 0xFF, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xFF, 0xFF, 
0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0xFF, 0xFF, 0xFF, 
0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 0xF6, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xF0, 0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x15, 0xFC, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xD2, 0xFF, 0xFF, 0xFF, 0xF8, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 
0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xEB, 0xFF, 0xFF, 0xFF, 
0xE1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0x81, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xFC, 0xFF, 0xFF, 0xFF, 0xC5, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0x66, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xCE, 0xFF, 0xFF, 0xFF, 0xFA, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x06, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x61, 0xB4, 0xF6, 0xFF, 0xFF, 0xFC, 0xD6, 
0x8A, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xE5, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x24, 0x00, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x63, 0x52, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA5, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x0E, 0x00, 0x00, 0x00, 0x72, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFC, 0x17, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x72, 0x00, 0x00, 0x00, 
0x00, 0x02, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xAC, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x00, 0x7D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x02, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x2A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x13, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xA7, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x46, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x37, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x00, 0x00, 0xF4, 0xFF, 
0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x48, 0x00, 0x00, 0x3B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x1B, 0x02, 0x4C, 0xE9, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 0x24, 0xEE, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 
0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x15, 0x94, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD8, 0x6A, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x37, 0x4E, 0x50, 0x4C, 0x17, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x9D, 0xAE, 0xAE, 
0xAE, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x5B, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 
0x92, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x15, 0xC1, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x33, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xEB, 0x5D, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 
0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x1F, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x8E, 0xFF, 
0xFF, 0xB6, 0x11, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0xBD, 0x88, 0x00, 0x00, 
0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 
0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 
0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 
0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x7D, 
0xBB, 0xF2, 0xFF, 0xFF, 0xFC, 0xE9, 0xA7, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 
0x9D, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x1D, 0x00, 0x00, 0x00, 
0x00, 0x44, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 
0x1D, 0x00, 0x00, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xC3, 0x00, 0x00, 0x0C, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xAC, 0x7B, 0x70, 
0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x19, 0xE7, 0xFF, 0xFF, 0x8E, 0x13, 
0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x00, 0x00, 0x00, 0x33, 0xD6, 
0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC7, 0xFF, 
0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x17, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 
0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xDA, 0xFF, 0xFF, 0xFF, 
0xFF, 0xEE, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xD2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF0, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 
0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x19, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x15, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xB6, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x39, 0x90, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x9B, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x5D, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x81, 
0xB8, 0xF2, 0xFC, 0xFF, 0xFF, 0xF8, 0xD0, 0x9B, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 
0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x26, 0x00, 0x00, 
0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEE, 0x2A, 0x00, 0x00, 0x06, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xCE, 0x00, 0x00, 0x00, 0x35, 0xFF, 0xFF, 0xFF, 0xD6, 0x85, 0x59, 0x50, 
0x5D, 0xAA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2A, 0x00, 0x00, 0x00, 0x88, 0xD4, 0x4A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x04, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF6, 0xFF, 0xFF, 0xFF, 
0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 
0xFF, 0xFF, 0xFF, 0xEB, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0E, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x4A, 0x4A, 
0x52, 0x5D, 0xA1, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x4E, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x92, 0x13, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xBF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xEB, 0xEB, 0xF0, 0xFA, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x19, 0x63, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x4E, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x39, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0xA1, 0xFF, 0xC3, 0x74, 0x41, 0x06, 
0x00, 0x00, 0x00, 0x28, 0x77, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x00, 0xA1, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFA, 0xF8, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x19, 0x00, 
0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 
0x48, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xDD, 0x3B, 0x00, 0x00, 0x00, 0x06, 0x5F, 0xAA, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFC, 0xBD, 0x66, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x44, 0x4E, 
0x50, 0x50, 0x4C, 0x2C, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xAE, 0xAE, 0xAE, 0xAE, 0xA1, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x11, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xEB, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xE9, 
0xFF, 0xFF, 0xFF, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 0x9B, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xE7, 0x13, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xEE, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x57, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xA7, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 
0xFF, 0xFF, 0xFF, 0xE9, 0x13, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1D, 0xF2, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xAC, 0x00, 0x00, 0x00, 0x00, 0x5D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xEE, 0x17, 0x00, 
0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x24, 0xF6, 0xFF, 0xFF, 
0xFF, 0xCE, 0x94, 0x94, 0x94, 0x94, 0x94, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x94, 0x94, 0x90, 
0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x39, 0xA7, 0xA7, 0xA7, 
0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0xA7, 0xA7, 0xA3, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAE, 0xAE, 0xAE, 0xAE, 0x2A, 0x00, 0x00, 0x00, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0xFC, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x08, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0x39, 0x00, 0x00, 0x1B, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x46, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x3D, 0x55, 0x5D, 
0x59, 0x4A, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xB6, 0x30, 0x00, 0x00, 0x00, 0x00, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x00, 0xAA, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x66, 0x00, 0x00, 0x68, 
0xF4, 0xFF, 0xFF, 0xFF, 0xFC, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x1B, 
0x00, 0x00, 0x15, 0x6E, 0x50, 0x17, 0x00, 0x00, 0x02, 0x30, 0x85, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB6, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x2C, 0x61, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0x44, 0xFF, 
0xF8, 0xA7, 0x68, 0x44, 0x0A, 0x06, 0x11, 0x4C, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x1F, 
0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x8A, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xBD, 0x04, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x96, 0x06, 0x00, 0x00, 0x00, 0x3F, 0xA3, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE9, 0x9B, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x44, 0x4E, 0x50, 
0x50, 0x48, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x5F, 0xA5, 0xCC, 0xF0, 0xF8, 0xF8, 0xF6, 0xEE, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x17, 0x94, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x48, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 
0x85, 0x57, 0x4C, 0x4A, 0x4C, 0x5D, 0x5D, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE7, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE9, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xE9, 0xFF, 0xFF, 0xFF, 0xEE, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x44, 0x94, 0xA7, 
0xA7, 0x9B, 0x57, 0x06, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0x6A, 0x17, 0xC3, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x5B, 0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 
0x5F, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0xA1, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 
0x00, 0xAA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0xA1, 0x66, 0x8E, 0xD8, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xB4, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x17, 0x00, 0x00, 0x00, 0x06, 
0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x04, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x1B, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x2A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0xAA, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x9B, 0xFF, 0xFF, 
0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0x59, 
0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 
0xFF, 0xFF, 0x55, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x8E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x02, 0x00, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x22, 0x00, 0x19, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x02, 0xD2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x2A, 0x00, 0x00, 
0x00, 0x24, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFC, 0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x72, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFA, 0xA1, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 
0x4A, 0x50, 0x4E, 0x37, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x3F, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x59, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 
0xEB, 0xEB, 0xEB, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x2A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 
0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 
0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1D, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0x81, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFA, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xE5, 
0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x68, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x68, 0xAC, 
0xE9, 0xFC, 0xFF, 0xF8, 0xD2, 0x9D, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6C, 
0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x2A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x30, 
0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xD6, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x57, 0x02, 0x00, 0x1B, 
0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0xF6, 0xFF, 0xFF, 0xFF, 
0xFF, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0xCC, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 
0x00, 0x00, 0x88, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x11, 0x00, 0x00, 0x00, 0x48, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFA, 0x15, 0x00, 0x00, 0x19, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x3F, 0x06, 0x83, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 0x44, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x35, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5F, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x0A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x8E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0xF2, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xD4, 0x19, 0x00, 0x00, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x19, 0x19, 
0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x0C, 0x00, 0x1F, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x74, 
0x00, 0x00, 0x00, 0x00, 0x52, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x74, 0xFF, 0xFF, 0xFF, 
0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x04, 0xA7, 
0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 
0xFF, 0x44, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0C, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x35, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 
0x00, 0x00, 0x00, 0x13, 0xB6, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x00, 0x15, 0xF4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xDD, 0xAA, 0xA3, 0xB4, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 0x00, 0x5F, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x04, 
0x00, 0x00, 0x00, 0x61, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA1, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0x8C, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0xA5, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x48, 0x50, 
0x50, 0x4C, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x6E, 
0xAE, 0xEE, 0xF8, 0xF6, 0xDD, 0xA3, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x68, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x37, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x4E, 
0x00, 0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF6, 0x2C, 0x00, 0x00, 0x04, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x5B, 0x59, 
0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x61, 
0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x9D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0xB2, 
0xFF, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE9, 0x00, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x13, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x92, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF4, 0x19, 0x00, 0x00, 0x00, 0x00, 0x17, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x48, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x57, 0x17, 0x22, 0x6A, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x48, 0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x39, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xC1, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x00, 0x00, 0x48, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xCC, 0x15, 0xCE, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x19, 0x8A, 0xDA, 
0xFC, 0xFF, 0xFC, 0xD8, 0x81, 0x08, 0x02, 0xFC, 0xFF, 0xFF, 0xFF, 0xC9, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x06, 0x00, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 
0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x35, 0xFC, 
0xFF, 0xFF, 0xFF, 0xE5, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x39, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x4C, 0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x06, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFC, 
0xF0, 0xEE, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x2E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x41, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x22, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x9D, 
0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x4A, 0x50, 0x50, 0x4E, 0x48, 
0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x9F, 0xF6, 0xFC, 0xC1, 
0x30, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x02, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 
0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x28, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x00, 0x0A, 0x8C, 
0xE7, 0xF2, 0xAA, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x6A, 
0x7D, 0x3D, 0x00, 0x00, 0x57, 0xFC, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x0C, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x0A, 
0x4A, 0xF8, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x19, 0x5B, 0x5F, 0x30, 0x00, 0x00, 0x00, 0x15, 
0x9F, 0xF6, 0xFC, 0xC1, 0x30, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x02, 0x00, 0xF2, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x28, 0x00, 0x96, 
0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 0x0A, 0x8C, 0xE7, 0xF2, 0xAA, 0x1F, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x3D, 0x3D, 0x3D, 0x3D, 0x33, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x99, 
0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFA, 0x0A, 0x00, 0x00, 0xF8, 
0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x2A, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x5B, 0xFF, 
0xFF, 0xFF, 0xDD, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x81, 0xA1, 
0xA1, 0xA1, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xBD, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xBF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD8, 
0x6A, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 
0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xA3, 
0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x90, 
0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x8C, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFA, 0xA5, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 
0x94, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x6E, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0E, 0x77, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x99, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x59, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xCC, 0x63, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xAA, 0xFC, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x1F, 0x8A, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0A, 0x6E, 0xDD, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xC1, 0xFF, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x63, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 
0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0x44, 0x44, 0x44, 0x44, 0x44, 
0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x63, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x41, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 
0xA7, 0xA7, 0xA7, 0xA7, 0x22, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xF2, 0x7F, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xF2, 0x81, 0x0E, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 
0x81, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF2, 0x81, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 
0x96, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x83, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x19, 0x8A, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x83, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x77, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 
0x83, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x68, 0xD8, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF4, 0x83, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x50, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3D, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0C, 0x6C, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x99, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x68, 0x04, 0x00, 0x00, 
0x00, 0x06, 0x63, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB8, 0x3F, 0x00, 0x00, 0x00, 
0x00, 0x26, 0x8E, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x94, 0x26, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x79, 0x11, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x5B, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFC, 0xAC, 0x3B, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xF2, 0x8C, 0x1F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x0A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x4E, 0x99, 0xC9, 0xF6, 0xFF, 0xFF, 0xFC, 0xE9, 0xA7, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x17, 0x83, 
0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0x19, 0x00, 0x00, 0xAE, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x13, 0x00, 
0x2E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 
0x00, 0x00, 0xB6, 0xFF, 0xFF, 0xE1, 0x94, 0x57, 0x4A, 0x4A, 0x6C, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0x06, 0x00, 0x33, 0xBF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xF8, 0xFF, 0xFF, 
0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE3, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x9B, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xCC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xD2, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xE9, 0xFF, 0xFF, 0xFF, 0xF4, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x63, 0x63, 0x63, 0x63, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x5B, 0x85, 0x52, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xEE, 0xFF, 0xFF, 0xFF, 0xCC, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE5, 0xFF, 0xFF, 0xFF, 0xBD, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x55, 0x61, 0x44, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x50, 0x7F, 0x9F, 0xA7, 0xA7, 0xA5, 0x94, 0x59, 0x24, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x5D, 
0xBF, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x6C, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 0xDA, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x44, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 
0xAE, 0xA3, 0xA1, 0xA7, 0xCC, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x66, 0x0E, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x72, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 0xFA, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0E, 0xAA, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 
0xFF, 0xFF, 0xFF, 0xF0, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xDD, 0x02, 0x00, 0x00, 0x00, 0x1D, 0xF6, 0xFF, 0xFF, 
0xFF, 0x41, 0x00, 0x00, 0x00, 0x0C, 0x6E, 0xB6, 0xF2, 0xFC, 0xFC, 0xF4, 0xD2, 0xA7, 0x7D, 0x41, 
0x02, 0x06, 0xD6, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0x88, 0x00, 
0x00, 0x00, 0x50, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 
0x4C, 0xFF, 0xFF, 0xFF, 0xC3, 0x00, 0x00, 0x11, 0xFA, 0xFF, 0xFF, 0xF0, 0x0E, 0x00, 0x00, 0x68, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x00, 0xE3, 
0xFF, 0xFF, 0xFC, 0x08, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0x8E, 0x00, 0x00, 0x39, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD4, 0x66, 0x46, 0x44, 0x4C, 0xF2, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 
0xFF, 0x4A, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 0xC9, 0xFF, 0xFF, 0xFF, 0xBF, 0x08, 
0x00, 0x00, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0x5B, 
0x00, 0xEB, 0xFF, 0xFF, 0xF8, 0x02, 0x00, 0x28, 0xFF, 0xFF, 0xFF, 0xFC, 0x1B, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0x63, 0x06, 0xFF, 
0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0x63, 0x1D, 0xFF, 0xFF, 0xFF, 
0xB0, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xFF, 
0xFF, 0xF2, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0x5B, 0x44, 0xFF, 0xFF, 0xFF, 0xAA, 0x00, 
0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x35, 0xFF, 0xFF, 0xFF, 0xEB, 
0x00, 0x00, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0x4A, 0x37, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x79, 
0xFF, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0xFC, 0x08, 0x0C, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 
0xFF, 0xDD, 0x02, 0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x17, 0xFC, 
0xFF, 0xFF, 0xBF, 0x00, 0x00, 0xFA, 0xFF, 0xFF, 0xF6, 0x00, 0x00, 0x04, 0xE9, 0xFF, 0xFF, 0xFF, 
0xAC, 0x1D, 0x00, 0x17, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x08, 0xB0, 0xFF, 0xFF, 0xFF, 
0x50, 0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 
0x00, 0x83, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x61, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x17, 0x00, 0x00, 0x00, 0x28, 
0xFF, 0xFF, 0xFF, 0xF4, 0x19, 0x00, 0x00, 0x00, 0x55, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 0x37, 
0x00, 0x00, 0x72, 0xF0, 0xFF, 0xFF, 0xFA, 0x9B, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 0xFF, 
0xFF, 0xFF, 0xBF, 0x02, 0x00, 0x00, 0x00, 0x00, 0x06, 0x37, 0x3F, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x33, 0x37, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xF8, 0xFF, 0xFF, 
0xFF, 0xB2, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xFF, 0xFF, 0xFF, 0xFF, 
0xDF, 0x61, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x52, 0xA5, 0x44, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF4, 0xAA, 0x79, 0x59, 0x50, 0x50, 0x5B, 0x83, 0xA7, 0xD6, 0xFC, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x81, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xB6, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x46, 0x8A, 0xB2, 0xEB, 0xFA, 0xFF, 0xFF, 
0xFA, 0xE9, 0xB0, 0x92, 0x52, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xA3, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5F, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x15, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xC5, 0xFF, 0xFF, 0xFF, 0xFC, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 
0x00, 0xE9, 0xFF, 0xFF, 0xFF, 0xEE, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 
0xFF, 0x30, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x2E, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 0x04, 0xFA, 0xFF, 0xFF, 
0xFF, 0xFA, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xFF, 0xFF, 
0xFF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x5D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF4, 0x02, 0x00, 0x00, 0x00, 0x11, 0xFC, 0xFF, 0xFF, 0xFF, 0xFC, 0x13, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 
0xC5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0xA7, 
0xA7, 0xA7, 0xA7, 0xA7, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x88, 
0x00, 0x00, 0x00, 0x11, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x68, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x1D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC7, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x02, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0xD0, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x28, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAA, 0xA7, 0x9F, 0x8A, 0x57, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x7D, 0x0A, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xDF, 0x1D, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0xB4, 0xB4, 
0xB4, 0xB8, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA1, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x94, 0x94, 0x94, 0x94, 0xA1, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x96, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x04, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x57, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x30, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0xB4, 0xB4, 0xB4, 0xBB, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF2, 0x28, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 
0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x04, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xDA, 0x94, 0x94, 0x94, 0x94, 0x9B, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x2A, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x4E, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x2E, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xF2, 0xB8, 0x90, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0x9B, 0xD2, 0xF8, 0xFF, 
0xFF, 0xFA, 0xE1, 0xAA, 0x68, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xDA, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0x3D, 0x00, 0x00, 0x00, 0x00, 
0x92, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 
0x00, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xEB, 0x02, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0xA7, 0x7B, 
0x6A, 0x9D, 0xCC, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x1B, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x9D, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x85, 0xE9, 0x26, 0x00, 0x00, 0x90, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x04, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE9, 0xFF, 0xFF, 0xFF, 
0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB6, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x2C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x44, 0x00, 0x00, 0x0C, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFC, 0x9B, 0x52, 0x41, 0x3D, 0x46, 0x57, 0x99, 0xD4, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x4E, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 
0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x6E, 0xC7, 
0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xBD, 0x72, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x3D, 0x4E, 0x50, 0x50, 0x4A, 0x28, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAA, 0xA5, 0x9B, 0x68, 0x41, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF2, 0x96, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x8E, 0x08, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xCC, 0x15, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0xB4, 0xB4, 
0xB4, 0xC1, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x08, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x96, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x48, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2A, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDA, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x02, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x37, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x57, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x79, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x94, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x74, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x37, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x02, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x9F, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 
0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0E, 0x7D, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xDA, 0x94, 0x94, 0x94, 0x9B, 0xBB, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xDF, 0x13, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x22, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB2, 0x15, 0x00, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x46, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xF2, 
0xC5, 0xA5, 0x68, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xAE, 0xAE, 0xAE, 
0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0xB4, 0xB4, 
0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 
0x35, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0xEB, 0x50, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x94, 0x94, 0x94, 
0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAA, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE3, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB0, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0xA1, 0xA1, 0xA1, 
0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x33, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x50, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x50, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 
0xE5, 0x48, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x5B, 0xA3, 0xD2, 0xF6, 0xFF, 0xFF, 0xFF, 0xF6, 0xD0, 0xA3, 
0x5B, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x90, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xBD, 0x02, 0x00, 0x00, 0x00, 0x00, 0x4C, 0xEE, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB8, 0x00, 
0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF2, 0xAA, 0x8A, 0x63, 0x83, 0xA1, 0xD0, 0xFF, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x11, 0xE9, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x74, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0x7B, 0x66, 
0x00, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xC9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0x5D, 0x5D, 0x5D, 0x5D, 
0x5D, 0x5D, 0x5D, 0x5D, 0x5D, 0x17, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0xF2, 0xFF, 0xFF, 0xFF, 
0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0xB6, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0xA7, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFC, 0x06, 0x00, 0x00, 0x00, 0x00, 0x06, 0x06, 0x06, 0x06, 0x13, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x35, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 
0x00, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x4E, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x06, 0xDD, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB8, 0x5D, 0x44, 0x3D, 0x41, 0x4C, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x37, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x48, 0xF6, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x2C, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xAE, 0xF8, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xD6, 0xA5, 0x5F, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x24, 0x4C, 0x50, 0x50, 0x4E, 0x48, 0x24, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 
0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x33, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xDF, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 
0xF2, 0xF2, 0xF2, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x9D, 0xAE, 0xAE, 0xAE, 
0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xA1, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xEB, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 
0x7F, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x83, 0x00, 0x0C, 0x74, 0xE7, 
0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x74, 0x0C, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 
0xD0, 0x28, 0x00, 0x00, 0x3B, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x3D, 
0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0xE5, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0x72, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x55, 0x33, 0x1D, 0x02, 0x06, 0x5D, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x19, 
0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x2A, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0x52, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 0x30, 0x00, 0x00, 0x00, 
0x28, 0x5B, 0x8A, 0x94, 0x88, 0x57, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xAE, 0xAE, 0xAE, 
0xAE, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0x59, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xF6, 
0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x08, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x11, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x1F, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x81, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x50, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x28, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x11, 0x00, 0x00, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x0E, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x2E, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 
0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x33, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x17, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x7B, 0xC9, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE3, 0x35, 0x00, 0x2A, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x0A, 
0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x2A, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x6C, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x1B, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x19, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x4E, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x0C, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x7B, 0xAE, 
0xAE, 0xAE, 0xAE, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 
0x94, 0x94, 0x94, 0x08, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x7F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8A, 0xAE, 0xAE, 0xAE, 
0xAE, 0xAE, 0xAE, 0x77, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x68, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF0, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0xB4, 0xFF, 0xFF, 0xFF, 0xBD, 0xDA, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 
0xE9, 0x8E, 0xFF, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xFC, 0xFF, 
0xFF, 0xFF, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xF0, 0x41, 0xFF, 0xFF, 
0xFF, 0xFF, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xFF, 0xFF, 0xFF, 0xDA, 0x46, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xF6, 0x02, 0xF2, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0x83, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0xA3, 0xFF, 0xFF, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0C, 0xFC, 0xFF, 0xFF, 0xFF, 0x30, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0x00, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0x19, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 
0xDD, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x06, 0xFA, 
0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0x88, 0x00, 0x57, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xB4, 
0x00, 0x00, 0x00, 0x08, 0xFA, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x68, 0xFF, 0xFF, 0xFF, 0xFA, 0x08, 0x00, 0x00, 0x59, 
0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x19, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0x8E, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0xCC, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x04, 0xFA, 0xFF, 0xFF, 0xFF, 0x37, 0x00, 0x00, 0x57, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 
0xF4, 0x02, 0x57, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0xA7, 0xFF, 
0xFF, 0xFF, 0x8E, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0xDF, 0xFF, 0xFF, 0xFF, 0x94, 0xFA, 0xFF, 0xFF, 0xFF, 0x39, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x02, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFC, 
0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xA1, 0xAE, 0xAE, 0xAE, 0xAE, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x0C, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x22, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xE1, 0xF8, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 
0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xC3, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xE9, 0x0C, 0xE5, 0xFF, 
0xFF, 0xFF, 0xFF, 0xD6, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 
0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xF6, 0x00, 0x00, 0xCE, 0xFF, 0xFF, 
0xFF, 0xFF, 0xEE, 0x13, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFA, 0x00, 0x00, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0xEB, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFC, 0x2A, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0x06, 0x00, 0x00, 0x1B, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x00, 0xDA, 0xFF, 0xFF, 
0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x52, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 
0x00, 0x00, 0x06, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x06, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x7F, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x1B, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 
0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x06, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x5F, 0xA7, 0xE1, 0xFC, 0xFF, 0xFF, 0xFF, 0xF8, 0xCE, 0x96, 
0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x79, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x44, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x15, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0xD2, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0xA5, 0x72, 0x63, 
0x83, 0xB2, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xC1, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE3, 0x02, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x19, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x04, 
0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x88, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x5B, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x46, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xC9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x94, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0xDF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0xA1, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x0E, 0x28, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x74, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x50, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x13, 0x00, 0x00, 0x00, 0xCE, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x66, 0x48, 0x44, 0x4C, 0x7F, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x24, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x35, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xBD, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xB6, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0x9D, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xD2, 0x7F, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x17, 0x48, 0x50, 0x50, 0x4E, 0x3F, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xA7, 0x9B, 0x6C, 0x39, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE1, 0x6A, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB2, 0x04, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 
0xB4, 0xB4, 0xB4, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x26, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF6, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0E, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x28, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x72, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 
0xA1, 0xA1, 0xA7, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x15, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD8, 0x4C, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0xEB, 0xEB, 0xE9, 0xB8, 0xAE, 0x83, 0x41, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x06, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x5F, 0xA7, 0xE1, 0xFC, 0xFF, 0xFF, 0xFF, 0xF8, 0xCE, 0x96, 
0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x79, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x44, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x15, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0xD2, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0xA5, 0x72, 0x63, 
0x83, 0xB2, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xC1, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE3, 0x02, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x19, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x04, 
0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x88, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x5B, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x46, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xC9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x94, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0xDF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0xA1, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x11, 0x28, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCE, 0x00, 0x00, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x77, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x50, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x13, 0x00, 0x00, 0x00, 0xCE, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x66, 0x48, 0x44, 0x4C, 0x7F, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x24, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x35, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xC1, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xB6, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x7F, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0x9D, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x17, 0x48, 0x50, 0x5F, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 
0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xD0, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xDF, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x0C, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x04, 0x7B, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAA, 0xA3, 0x92, 0x59, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x63, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xBD, 0x0C, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE5, 0xB4, 0xB4, 0xC1, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xB8, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF6, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x4A, 0x4A, 0x52, 0x72, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 
0x1B, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x1B, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xAC, 0x0C, 0x0C, 
0x33, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x15, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x0A, 0xDF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xAC, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 
0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x17, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xF2, 0xFF, 0xFF, 
0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x1B, 0x00, 
0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x8E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x61, 0xAC, 0xE7, 0xFC, 0xFF, 0xFF, 0xF6, 0xC7, 0x92, 0x46, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x44, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x8E, 0x15, 
0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEB, 0x02, 0x00, 0x22, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x88, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x90, 0x63, 0x8C, 0xAA, 
0xE7, 0xFF, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x35, 0x90, 0xA7, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x4E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 
0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x48, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x9B, 0x22, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0x83, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0x99, 0xFC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1F, 0x94, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x0C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFC, 0xFF, 0xFF, 0xFF, 
0xFF, 0x57, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 
0xFF, 0xFF, 0xFF, 0x55, 0x50, 0xE3, 0x7D, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x37, 0x50, 0xFF, 0xFF, 0xFF, 0xC5, 0x7D, 0x4A, 0x2C, 0x0C, 0x41, 
0x79, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x02, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x48, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x08, 0x00, 0x00, 0x00, 0x28, 
0x85, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0xA1, 0x39, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x35, 0x4C, 0x50, 0x50, 0x4A, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x33, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x08, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x4A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x0C, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x37, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xCE, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xBF, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xA5, 0xAE, 0xAE, 0xAE, 0xAE, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x33, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x0C, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 
0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 
0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 
0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 
0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 
0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x02, 0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x1D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xBF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x88, 0x00, 0x02, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x7F, 0x4C, 0x44, 0x48, 0x6A, 0xD0, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x1B, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 
0x79, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x7D, 
0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x83, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x44, 0x4E, 0x50, 0x4E, 0x44, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xAE, 0xAE, 0xAE, 0xAE, 0x6E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x33, 0x92, 0xFF, 
0xFF, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x04, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0xE3, 
0xFF, 0xFF, 0xFF, 0xFF, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x8E, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x02, 0x00, 0x00, 0x35, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0x72, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 
0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xFF, 0xFF, 
0xFF, 0xFF, 0xEE, 0x02, 0x00, 0x00, 0x00, 0x00, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x13, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFA, 0xFF, 0xFF, 
0xFF, 0xFF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x2E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x06, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 
0xFF, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 
0x00, 0x00, 0x00, 0x02, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 
0x02, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x02, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 
0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0xFF, 0xFF, 
0x90, 0x00, 0x3B, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x24, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0x88, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xCE, 0xFF, 0xFF, 0xFF, 
0xFF, 0x15, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0xF8, 0xFF, 0xFF, 0xFF, 0xDA, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 
0xFF, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xCC, 0xFF, 0xFF, 0xFF, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0x2A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC5, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xAE, 0xAE, 0xAE, 0xAE, 0x44, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x33, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0xAE, 0xAE, 0xAE, 0xAE, 0x77, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x6A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xDA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x2C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFA, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xC9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0x02, 0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x85, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB6, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 0x26, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFC, 0x04, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFC, 0xFF, 0xFF, 0xFF, 0xB8, 0xFF, 0xFF, 0xFF, 
0xD4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x02, 0x00, 0x00, 0x00, 
0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 
0xFF, 0xFF, 0xFF, 0xFF, 0x15, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 
0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x8C, 0xFF, 0xFF, 
0xFF, 0xDA, 0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0xAC, 0xFF, 0xFF, 0xFF, 
0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 
0xCC, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0xD8, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0xF0, 
0xFF, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x00, 
0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0x6A, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 
0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE1, 0xFF, 0xFF, 
0xFF, 0xFF, 0x33, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 
0xFF, 0x1F, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xAC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0x6A, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xF4, 0x00, 0x00, 0x26, 
0xFF, 0xFF, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0x6C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 0xAA, 
0x00, 0x00, 0x00, 0xE7, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0x2E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x13, 0xFF, 0xFF, 
0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x0A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xEE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0x15, 
0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0x2A, 0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0x19, 0x4C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9F, 0xFF, 0xFF, 
0xFF, 0xFF, 0x4C, 0x81, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xFF, 0xFF, 
0xFF, 0x50, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0xAC, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xDA, 0xFF, 0xFF, 0xFF, 0x7D, 0xAC, 0xFF, 0xFF, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x17, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0xDD, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x9D, 0xFF, 0xFF, 0xFF, 0xA7, 0xDD, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0xD2, 0xFC, 0xFF, 0xFF, 0xFF, 
0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xD2, 0xFC, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x1B, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x59, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x24, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x1B, 0x00, 0xAA, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFC, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x17, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x13, 0x00, 0x00, 0x00, 0x61, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x19, 0x00, 0x00, 
0x00, 0x00, 0x06, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 
0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 
0x00, 0x1F, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x02, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x74, 
0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0C, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x39, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 
0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x19, 0x68, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x6C, 0x00, 
0x00, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00, 
0x00, 0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xF4, 
0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE9, 0x13, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x0E, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x52, 0x00, 0x15, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x24, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x13, 0xAA, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFA, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAA, 0x8A, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x63, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x2C, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xCE, 0x00, 0x00, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x00, 0x00, 
0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 
0x00, 0x00, 0x00, 0x00, 0x13, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x0E, 0x00, 0x00, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 
0x00, 0x15, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x8E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x13, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x0E, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0x83, 0x17, 0xF8, 
0xFF, 0xFF, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x04, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x72, 0x00, 0xBB, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x83, 0xB4, 0xB4, 0xB4, 0xB4, 
0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x2E, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xE9, 0xFF, 
0xFF, 0xFF, 0xFF, 0xC5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE9, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x30, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x06, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x0A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x37, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0xE7, 
0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xAA, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6A, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xEB, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 
0x94, 0x90, 0x08, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x0C, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x44, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 
0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 
0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 
0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 
0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 
0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 
0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 
0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 
0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 
0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x63, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 
0xA7, 0xA7, 0x70, 0xAE, 0xAE, 0xAE, 0xAE, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0xEB, 0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD2, 
0xFF, 0xFF, 0xFF, 0xF6, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 
0xFF, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xFC, 0xFF, 
0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB8, 0xFF, 0xFF, 
0xFF, 0xFF, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 
0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xF0, 0xFF, 0xFF, 0xFF, 
0xDD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0xF4, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFC, 0x1B, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xF4, 0xFF, 0xFF, 0xFF, 0xD6, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0x37, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0xF2, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFC, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x61, 0xFF, 0xFF, 0xFF, 0xFF, 0x72, 0x04, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 
0xAE, 0x3B, 0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x06, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x57, 0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x04, 0xA7, 
0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x8E, 0xE5, 0xE5, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x19, 0xFA, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xFF, 0xFF, 0xFF, 0xFF, 0x3B, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 
0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0xE3, 0xFF, 0xFF, 0xE7, 0xFC, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0x79, 0x96, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD6, 0xFF, 0xFF, 0xFA, 0x13, 0x1F, 0xFC, 
0xFF, 0xFF, 0xD8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 
0x96, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xC3, 0xFF, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0x1F, 0xFC, 0xFF, 0xFF, 0xE1, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 
0x6E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 
0x1F, 0xFC, 0xFF, 0xFF, 0xE7, 0x08, 0x00, 0x00, 0x00, 0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xDA, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 
0xFF, 0xFF, 0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFC, 0xFF, 0xFF, 0xEE, 0x0E, 0x00, 
0x00, 0x19, 0xFA, 0xFF, 0xFF, 0xF0, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0xFF, 
0xFF, 0xFF, 0x83, 0x00, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1F, 0xFC, 0xFF, 0xFF, 0xF4, 0x13, 0x06, 0xDF, 0xEB, 0xEB, 0xE9, 0x1F, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xEB, 0xEB, 0xEB, 0x7F, 0x04, 0xB4, 0xB4, 0xB4, 
0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xA7, 0x06, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x04, 0xA7, 0xA7, 0xA7, 
0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x9B, 0x55, 0x9B, 0x9B, 0x9B, 
0x9B, 0x9B, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0xEE, 0xFF, 0xFF, 
0xFF, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xC7, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0x3D, 0x3D, 0x24, 0x00, 0x00, 0x00, 0x00, 0x28, 0x74, 0xAC, 
0xE3, 0xFA, 0xFF, 0xFF, 0xF6, 0xC9, 0x8E, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0xC7, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x08, 0x00, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 
0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xF2, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x5B, 0x00, 0x00, 0x00, 0x46, 0xFF, 0xC3, 0x68, 0x22, 0x00, 0x00, 0x02, 0x57, 0xF4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x0C, 0x28, 0x3F, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x50, 0xA1, 0xD2, 
0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x4A, 0xE3, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x52, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xF4, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x04, 
0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8E, 0x35, 0x02, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x61, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 
0x00, 0x04, 0x8E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x28, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xB4, 0x9B, 0xA3, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x24, 0xE9, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x1F, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 
0x00, 0x1B, 0xA5, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x92, 0x11, 0x00, 0x00, 0xE1, 0xFF, 0xFF, 
0xFF, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x4A, 0x50, 0x4E, 0x3B, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x15, 0x57, 0x57, 0x57, 0x57, 0x57, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0E, 0x00, 0x3F, 0xAC, 0xF2, 0xFC, 0xF6, 
0xC9, 0x77, 0x08, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x8A, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x2A, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFC, 0x7D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x1D, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xBD, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x7D, 0x4C, 0x4E, 0x9B, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x2A, 0x00, 
0x00, 0x00, 0x00, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFC, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x11, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x1D, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x0A, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x83, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x02, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF4, 0x17, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x44, 0x02, 0x02, 0x59, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x4E, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0xF6, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0x83, 0x3B, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFC, 0x1D, 0x00, 0x1F, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x4A, 0x50, 0x4C, 
0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x6A, 0xAC, 0xE9, 
0xFC, 0xFF, 0xF6, 0xE1, 0xA7, 0x61, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xF6, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x30, 0x00, 0x00, 0x02, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x8E, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA3, 0x57, 0x50, 0x5D, 0xA3, 0xF0, 0xFF, 0x26, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x48, 0x00, 0x00, 0x00, 0xF0, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x48, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x26, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x4E, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x5D, 0x06, 
0x00, 0x06, 0x41, 0x85, 0xE9, 0xB4, 0x00, 0x00, 0x15, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x57, 0xF2, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0x90, 
0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x8A, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x24, 0x4C, 0x50, 0x50, 0x4C, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0x57, 0x57, 0x57, 0x57, 0x55, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x13, 0x7D, 0xCE, 0xF6, 0xFC, 0xF0, 0xAA, 0x3F, 
0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x41, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x92, 0x35, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x35, 0xF6, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x02, 0xDD, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0x57, 0x59, 0x96, 0xF8, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF8, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 0x35, 
0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x02, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xB8, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x4E, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x55, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x04, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x48, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0xDD, 
0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x2E, 0x00, 0x00, 0x22, 0xAE, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x1F, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0xF4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x99, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x04, 0xBB, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 
0x00, 0x5D, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x7B, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xF8, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0x4E, 0x50, 0x3B, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x77, 0xBB, 0xF2, 0xFC, 0xFA, 0xF0, 0xAE, 
0x6E, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x88, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0xCC, 0xCE, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 
0x26, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x13, 0x00, 0x00, 0x15, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xDA, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xE1, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x1B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x06, 
0x06, 0x06, 0x06, 0x06, 0x06, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x55, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAE, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAE, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6E, 0x6A, 0x6A, 0x6A, 0x6A, 0x6A, 
0x6A, 0x6A, 0x6A, 0x6A, 0x6A, 0x6A, 0x48, 0x3B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x26, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 
0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE1, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 
0x00, 0x46, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x50, 
0xB0, 0x9F, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0xA5, 0xA5, 0xAC, 
0xD4, 0xFC, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x11, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xAA, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x39, 0xA5, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xBF, 0x6C, 0x0A, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0x4C, 0x50, 0x50, 0x4C, 0x2E, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0x55, 0x7D, 0x94, 0x8E, 0x5F, 0x4A, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFC, 0x55, 0x00, 0x00, 0x00, 0x00, 0x26, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x1B, 0x00, 0x00, 0x00, 0x00, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xC3, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0xAE, 0xC3, 0xFC, 
0x6A, 0x00, 0x00, 0x00, 0x00, 0x48, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x04, 
0x0E, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x19, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0xA7, 0xA7, 0xA7, 0x6E, 
0x00, 0x00, 0x15, 0x90, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x19, 0x57, 0x57, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x57, 0x57, 0x57, 0x39, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x83, 0xD2, 0xFA, 0xFC, 0xEB, 0xA7, 0x39, 0x00, 0x00, 
0x6A, 0xA7, 0xA7, 0xA7, 0xA3, 0x00, 0x00, 0x00, 0x46, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x94, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x33, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0xF2, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x02, 0xD6, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x5D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x94, 0x57, 0x59, 0x94, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x00, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x37, 0xF8, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0x02, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x90, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x2A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x59, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x55, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF8, 0x52, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x48, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0xDA, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE5, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 
0x85, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x1D, 0x00, 0x00, 0x11, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x00, 0x1F, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0xEE, 0xFC, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB8, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x6C, 
0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x7F, 0x02, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x35, 0x50, 0x50, 0x37, 0x02, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x00, 0x2C, 0x83, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6A, 0x00, 0x3D, 0xFF, 0xFF, 0xE7, 0xAA, 
0x7D, 0x59, 0x50, 0x4C, 0x57, 0xAA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x0C, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 
0x63, 0x00, 0x00, 0x00, 0x1D, 0xB2, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0xA5, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x41, 0x5D, 0x88, 0x9B, 0xA1, 0xA1, 
0x99, 0x72, 0x4E, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x57, 0x57, 0x57, 0x57, 0x57, 0x15, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x00, 0x3B, 0xA5, 0xE5, 0xFC, 0xFA, 0xE1, 0xA3, 
0x3D, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x02, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAC, 0x0C, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x85, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x52, 0x4A, 0x68, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xF8, 0xFF, 0xFF, 
0xFF, 0xFF, 0xD2, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x37, 
0x50, 0x39, 0x00, 0x00, 0x06, 0xC9, 0xFF, 0xFF, 0xFF, 0xC9, 0x06, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x50, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2E, 
0x00, 0x4E, 0xB8, 0xEB, 0xB8, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x28, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x50, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0xC9, 0xFF, 0xFF, 0xFF, 0xC9, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x5B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2E, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4E, 0xB8, 0xEB, 0xB8, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x28, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x04, 0x48, 0xD2, 0xB0, 0xB0, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCE, 0x00, 0x4A, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x92, 0x08, 0x00, 
0x00, 0x13, 0x55, 0x8A, 0x9F, 0xA1, 0x99, 0x5B, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x15, 0x57, 0x57, 
0x57, 0x57, 0x57, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x52, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x59, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x06, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x39, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 
0xCE, 0x0C, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x28, 0xEB, 
0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x15, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 
0x00, 0x00, 0x1D, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x17, 0x00, 0x15, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x2C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x0A, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF6, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x02, 0xAC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF6, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x4C, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x02, 0x00, 0x7B, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x37, 0x00, 0x00, 0x00, 0x00, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x1D, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE1, 0x11, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 
0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6E, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xDF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFC, 0x33, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x0E, 0x15, 0x57, 0x57, 0x57, 0x57, 0x57, 0x15, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x28, 0xA7, 0xA7, 
0xA7, 0xA5, 0x00, 0x00, 0x00, 0x5D, 0xB0, 0xF2, 0xFF, 0xFA, 0xDA, 0x9B, 0x2E, 0x00, 0x00, 0x00, 
0x00, 0x3F, 0xA5, 0xE5, 0xFC, 0xFC, 0xE5, 0xA7, 0x44, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0x39, 0x15, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 0x08, 0xAC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x02, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0x88, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xA1, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x13, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x88, 0x4E, 0x4C, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x52, 0x4A, 
0x77, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 
0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x28, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 
0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 
0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 
0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 
0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 
0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x28, 0xA7, 0xA7, 0xA7, 0xA5, 0x00, 0x00, 
0x00, 0x4C, 0xA7, 0xE5, 0xFC, 0xFC, 0xE7, 0xA7, 0x44, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0x39, 0x15, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x0C, 0x00, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0x88, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xB8, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0x52, 0x4A, 
0x66, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 
0x00, 0x00, 0x00, 0x00, 0x17, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xCE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF6, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x6C, 0xAE, 0xEE, 0xFA, 0xFF, 
0xF2, 0xBF, 0x81, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xF4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x8A, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x0C, 0x00, 
0x00, 0x00, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x26, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x8E, 0x52, 0x50, 
0x77, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3F, 0x00, 0x00, 0x00, 0x00, 0x15, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x00, 0xE9, 0xFF, 
0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x2A, 0x22, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x61, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x57, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 
0xA3, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEE, 
0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x2A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x74, 0x02, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0xA1, 0xFF, 
0xFF, 0xFF, 0xFF, 0xEE, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 
0x02, 0x00, 0x39, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x3D, 0x00, 0x00, 0x1F, 0xA3, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xFA, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x13, 0x00, 0x00, 0x00, 0x0C, 0xCE, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x11, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x3D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xBD, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x7F, 
0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x44, 0x50, 0x50, 
0x4A, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xA7, 0xA7, 0xA7, 0xA7, 0x2C, 0x00, 
0x00, 0x39, 0xAC, 0xF2, 0xFC, 0xF6, 0xC9, 0x77, 0x08, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0x7D, 0x00, 0x85, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x2A, 0x00, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x7D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE9, 0x1D, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xEE, 0x7D, 0x4C, 0x4C, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFA, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 
0xFF, 0xFF, 0xFF, 0xD2, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x88, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x11, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x1D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x02, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x17, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x44, 0x02, 0x02, 
0x4A, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x3B, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x3B, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFA, 0x55, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x1D, 0xB0, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB8, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 
0x00, 0x00, 0x15, 0x4A, 0x50, 0x4C, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x26, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x13, 0x7F, 0xD0, 0xF6, 0xFC, 0xF0, 0xA7, 0x3B, 0x00, 0x00, 0x6A, 0xA7, 0xA7, 0xA7, 0xA3, 
0x00, 0x00, 0x00, 0x41, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8A, 0x00, 0xBB, 0xFF, 
0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x35, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x83, 0xF8, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x02, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFC, 0x96, 0x57, 0x59, 0x94, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0xBF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x35, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x02, 
0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3B, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x15, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x52, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x48, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xFF, 
0xFF, 0xFF, 0xFF, 0xF8, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x0C, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xC5, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xBD, 0x1D, 0x00, 0x00, 0x11, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x24, 0xFC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0xEE, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC9, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 
0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x63, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF4, 0x7F, 0x02, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x4E, 0x50, 0x3D, 0x02, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xA1, 
0xA1, 0xA1, 0xA1, 0x9D, 0x28, 0xA7, 0xA7, 0xA7, 0xA1, 0x00, 0x00, 0x00, 0x1B, 0x8E, 0xE1, 0xFC, 
0xFA, 0x8E, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 0x48, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0x74, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x41, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xBB, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xAE, 0x4C, 0x06, 0x00, 0x02, 0x06, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAC, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 
0x72, 0xAE, 0xEB, 0xFA, 0xFF, 0xF8, 0xE7, 0xAE, 0x7D, 0x30, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xF4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x22, 0x00, 0x72, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x06, 0xF4, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFC, 0xF8, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x77, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x4E, 0x02, 0x00, 0x02, 0x35, 0x83, 0xE1, 0xFA, 0x15, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFA, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xAE, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xFC, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFA, 0x96, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xB6, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 
0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x6A, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x46, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x08, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x50, 0xD8, 0x7D, 0x2A, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x50, 0xFF, 0xFF, 0xFF, 
0xEE, 0xAE, 0x9D, 0x9B, 0xA5, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x1F, 0x50, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x04, 0x00, 0x17, 0x81, 0xCE, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xB6, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x3B, 0x4C, 0x50, 0x50, 0x4C, 0x30, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x99, 0xBB, 0xBB, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 
0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0xFF, 
0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE3, 0xFF, 0xFF, 
0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x8A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE5, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x30, 0x00, 0x46, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x37, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x4A, 0x15, 0x57, 0x57, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, 0xCE, 0x57, 0x57, 0x57, 
0x57, 0x57, 0x19, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x70, 0x02, 0x00, 0x02, 0x41, 0x28, 0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xF8, 0xF4, 0xFC, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x02, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xCC, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0x4C, 0x50, 0x4E, 0x41, 
0x06, 0x00, 0x00, 0x30, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x61, 0xA7, 0xA7, 0xA7, 0xA7, 0x96, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 
0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 
0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x41, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 
0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x5B, 0x02, 0x00, 0x08, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE5, 0x00, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0xF4, 0xFC, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xBB, 0xF6, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCC, 0x13, 0xAC, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x46, 
0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x7D, 0x06, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x15, 0x4A, 0x50, 0x4E, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x90, 0xA7, 0xA7, 0xA7, 0xA7, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x26, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x37, 0x88, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x0C, 0x28, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 
0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x41, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 0x00, 0x0C, 0xFA, 0xFF, 0xFF, 
0xFF, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 
0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x06, 0x00, 0x00, 
0x00, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x7D, 0xFF, 0xFF, 0xFF, 0xFF, 0xAC, 0x00, 0x00, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x08, 0x00, 0x88, 
0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 
0xFF, 0xFF, 0x59, 0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xF4, 0xFF, 0xFF, 0xFF, 0xDD, 0x5F, 0xFF, 0xFF, 
0xFF, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 
0xFF, 0xFC, 0x9F, 0xFF, 0xFF, 0xFF, 0xFC, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x15, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5F, 0xA7, 0xA7, 0xA7, 0xA7, 0x7B, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x6A, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 
0xA7, 0xA7, 0xA7, 0xA7, 0x37, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x19, 0x04, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x0A, 
0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD0, 0x00, 0x00, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x24, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 
0x85, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x92, 0xFF, 0xFF, 
0xFF, 0xDA, 0xFF, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x00, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 
0x00, 0x00, 0x28, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x04, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xFC, 
0x5B, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x02, 0x00, 
0x00, 0x00, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x0E, 0xFF, 0xFF, 0xFF, 0xD2, 0x0C, 
0xFF, 0xFF, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 
0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0x88, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0xD2, 
0xFF, 0xFF, 0xFF, 0x1F, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x00, 
0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x99, 0xFF, 
0xFF, 0xFF, 0x57, 0x00, 0x02, 0xFA, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0x00, 0x00, 0x00, 0x00, 0x06, 
0xFC, 0xFF, 0xFF, 0xFF, 0xFC, 0x06, 0x00, 0xD6, 0xFF, 0xFF, 0xFF, 0x1F, 0x00, 0x55, 0xFF, 0xFF, 
0xFF, 0xA1, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xCE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 
0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x06, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x1B, 0xFF, 0xFF, 0xFF, 
0xD4, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 0xFF, 
0xFF, 0xFF, 0xFF, 0x68, 0x44, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0xE9, 0xFF, 0xFF, 0xFC, 
0x02, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFF, 0xFF, 
0xFF, 0xFF, 0x9B, 0x5D, 0xFF, 0xFF, 0xFF, 0x6A, 0x00, 0x00, 0x00, 0xAA, 0xFF, 0xFF, 0xFF, 0x39, 
0xD2, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE7, 0xFF, 0xFF, 
0xFF, 0xAE, 0x9B, 0xFF, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0x59, 0xFC, 
0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9D, 0xFF, 0xFF, 0xFF, 
0xE7, 0xAE, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0x94, 0xFF, 0xFF, 
0xFF, 0xFF, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0xFC, 
0xDF, 0xFF, 0xFF, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFC, 0xFF, 0xFF, 0xF4, 0xFF, 0xFF, 0xFF, 
0xFC, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x81, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 
0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x37, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC9, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x2A, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x9B, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x13, 0x00, 0xB6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x1B, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xDD, 0x06, 0x00, 0x00, 0x00, 0x19, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x08, 
0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x22, 
0x00, 0x46, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xF2, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x06, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x08, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x6C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 
0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x39, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0xFC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xFC, 0xFF, 0xFF, 0xFF, 
0xFF, 0x9F, 0x00, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x13, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x79, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x96, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x28, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xC9, 0x00, 
0x00, 0x00, 0x00, 0x11, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x0C, 0x00, 0x00, 0xCC, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFC, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 
0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x8E, 0xA7, 0xA7, 0xA7, 0xA7, 0xA3, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3B, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x37, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x0C, 
0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xF6, 0xFF, 
0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 
0x02, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x02, 0xF2, 0xFF, 0xFF, 0xFF, 
0xFF, 0x85, 0x00, 0x00, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 
0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x26, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x41, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 0x02, 0xEE, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x41, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFA, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xE5, 0xFF, 0xFF, 0xFF, 
0xFF, 0x41, 0x00, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 0x00, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xFF, 0xFF, 
0xFF, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0x26, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC9, 0xFF, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0xF4, 
0xFF, 0xFF, 0xFF, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 
0xB2, 0xAE, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x44, 0x92, 0xA1, 0x9F, 0x7B, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 
0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0xA7, 0x0C, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x13, 0x26, 0x63, 0x63, 0x63, 0x63, 0x63, 0x63, 0x63, 0x74, 
0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xDD, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFC, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xF6, 0xFF, 
0xFF, 0xFF, 0xFF, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xD8, 0xFF, 0xFF, 
0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE9, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 
0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8C, 0xFF, 0xFF, 0xFF, 0xFF, 0xEE, 0x1D, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x0C, 0x0C, 
0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x04, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x52, 
0x83, 0x9F, 0xA7, 0x7B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6C, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xC9, 0x63, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 
0xFF, 0xDF, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xAA, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 
0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFC, 0xFF, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 
0x02, 0x17, 0x4C, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x66, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 
0xAC, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0xAC, 0x59, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x02, 0x00, 0x00, 
0x00, 0x04, 0x1B, 0x4C, 0x9D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0xFC, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 
0xFF, 0xFF, 0xE3, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 
0xCE, 0x68, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6A, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x06, 0x4C, 0x74, 0x9D, 0xA5, 0x77, 0x4A, 0x4A, 0x4A, 0x41, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 
0xFF, 0xFF, 0xE5, 0xFF, 0xFF, 0xFF, 0xE5, 0x3D, 0x3D, 0x3D, 0x35, 0x6A, 0xAA, 0xA3, 0x8E, 0x55, 
0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x92, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x81, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 
0x59, 0xB0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 
0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 
0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 
0xFF, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x99, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x52, 0x22, 
0x04, 0x00, 0x00, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x39, 0xA1, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x3B, 0xA5, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x88, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB8, 0x55, 
0x26, 0x08, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 
0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 
0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x2C, 0x59, 0xBB, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x08, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x81, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xA5, 
0x9F, 0x81, 0x52, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x72, 0x9F, 
0xA3, 0x96, 0x57, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0x00, 0x8C, 0xFC, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xB4, 0x46, 0x00, 0x00, 0x00, 0x00, 0x02, 0x79, 0xFA, 0x5F, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0xAE, 0x9D, 0xA3, 0xE7, 0xFF, 
0xFF, 0x63, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x63, 0xFF, 0xD0, 0x5D, 0x41, 0x46, 0x61, 0xB4, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xC9, 0x63, 0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0x79, 0xCE, 
0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x81, 0x06, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x41, 0x4A, 0x39, 0x02, 0x00, 0x00, 0x00, 0x0E, 0x4E, 0x6F, 0x74, 0x6F, 0x20, 
0x53, 0x61, 0x6E, 0x73, 0x20, 0x42, 0x6F, 0x6C, 0x64, 0x00, 0x0D, 0x4E, 0x6F, 0x74, 0x6F, 0x53, 
0x61, 0x6E, 0x73, 0x2D, 0x42, 0x6F, 0x6C, 0x64, 0x01, 
};
