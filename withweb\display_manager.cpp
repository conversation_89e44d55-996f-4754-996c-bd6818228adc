/*
 * Display Manager Implementation - Minimal Version
 */

#include "display_manager.h"
#include <SPIFFS.h>
#include <FS.h>

DisplayManager::DisplayManager() : 
  tft(TFT_eSPI()),
  touch(XPT2046_Touchscreen(TOUCH_CS, TOUCH_IRQ)),
  calibrated(false),
  backlightOn(true),
  currentBrightness(255),
  lastTouchTime(0),
  touchPressed(false),
  touchDebounceTime(0),
  screenDirty(true) {
}

DisplayManager::~DisplayManager() {
}

bool DisplayManager::begin() {
  Serial.println("Initializing display...");
  
  // Initialize TFT
  tft.init();
  tft.setRotation(0); // Portrait mode
  tft.fillScreen(COLOR_BACKGROUND);
  
  // Initialize touch
  touch.begin();
  touch.setRotation(0);
  
  // Set up backlight
  pinMode(TFT_BL, OUTPUT);
  setBrightness(255);
  
  // Load or perform calibration
  loadCalibration();
  if (!calibrated) {
    performCalibration();
  }
  
  Serial.println("Display initialized successfully");
  return true;
}

void DisplayManager::reset() {
  tft.fillScreen(COLOR_BACKGROUND);
  screenDirty = true;
}

void DisplayManager::turnOnBacklight() {
  digitalWrite(TFT_BL, HIGH);
  backlightOn = true;
}

void DisplayManager::turnOffBacklight() {
  digitalWrite(TFT_BL, LOW);
  backlightOn = false;
}

void DisplayManager::setBrightness(uint8_t brightness) {
  currentBrightness = brightness;
  analogWrite(TFT_BL, brightness);
}

void DisplayManager::updateBrightness() {
  if (!backlightOn) return;
  
  int lightLevel = analogRead(LDR_PIN);
  int brightness = map(lightLevel, 0, 4095, 50, 255);
  brightness = CLAMP(brightness, 50, 255);
  
  if (abs(brightness - currentBrightness) > 10) {
    setBrightness(brightness);
  }
}

bool DisplayManager::isTouched() {
  if (!touch.touched()) {
    touchPressed = false;
    return false;
  }
  
  unsigned long currentTime = millis();
  if (currentTime - touchDebounceTime < TOUCH_DEBOUNCE) {
    return false;
  }
  
  if (!touchPressed) {
    touchPressed = true;
    touchDebounceTime = currentTime;
    lastTouchTime = currentTime;
    return true;
  }
  
  return false;
}

TouchPoint DisplayManager::getTouchPoint() {
  TouchPoint point;
  point.pressed = false;
  point.timestamp = millis();
  
  if (touch.touched()) {
    TS_Point p = touch.getPoint();
    
    // Apply calibration
    point.x = map(p.x, calData[0], calData[2], 0, SCREEN_WIDTH);
    point.y = map(p.y, calData[1], calData[3], 0, SCREEN_HEIGHT);
    
    // Validate coordinates
    if (validateTouch(point.x, point.y)) {
      point.pressed = true;
      lastTouch = point;
    }
  }
  
  return point;
}

void DisplayManager::showWelcomeScreen() {
  clearScreen();
  
  // Draw company logo area
  tft.fillRect(0, 0, SCREEN_WIDTH, HEADER_HEIGHT, COLOR_HEADER);
  drawCenteredText("PUNCH CLOCK SYSTEM", 0, 0, SCREEN_WIDTH, HEADER_HEIGHT, 
                   FONT_MEDIUM, COLOR_TEXT);
  
  // Draw welcome message
  drawCenteredText("Initializing...", 0, 120, SCREEN_WIDTH, 40, 
                   FONT_LARGE, COLOR_INFO);
  
  // Draw version info
  String version = "Version " + String(FIRMWARE_VERSION);
  drawCenteredText(version, 0, 280, SCREEN_WIDTH, 20, 
                   FONT_SMALL, COLOR_TEXT);
}

void DisplayManager::showMainScreen() {
  clearScreen();
  
  // Draw header
  drawHeader("Employee Punch Clock");
  
  // Draw status area
  drawCenteredText("Ready - Scan Your Card", 0, HEADER_HEIGHT + TIME_HEIGHT + 20, 
                   SCREEN_WIDTH, 40, FONT_MEDIUM, COLOR_SUCCESS);
  
  // Draw instructions
  drawCenteredText("Hold your RFID card near the reader", 
                   0, HEADER_HEIGHT + TIME_HEIGHT + STATUS_HEIGHT, 
                   SCREEN_WIDTH, 40, FONT_SMALL, COLOR_TEXT);
  
  // Draw footer with admin button
  drawFooter();
}

void DisplayManager::updateMainScreen(String currentTime, String lastEmployee, String lastAction) {
  // Update time display
  drawTime(currentTime, 0, HEADER_HEIGHT + 10);
  
  // Update last activity if available
  if (lastEmployee.length() > 0) {
    String statusText = lastEmployee + " - " + lastAction;
    tft.fillRect(10, HEADER_HEIGHT + TIME_HEIGHT + 60, SCREEN_WIDTH - 20, 30, COLOR_BACKGROUND);
    drawCenteredText(statusText, 10, HEADER_HEIGHT + TIME_HEIGHT + 60, 
                     SCREEN_WIDTH - 20, 30, FONT_SMALL, COLOR_INFO);
  }
}

void DisplayManager::showProcessing() {
  tft.fillRect(0, HEADER_HEIGHT + TIME_HEIGHT, SCREEN_WIDTH, STATUS_HEIGHT, COLOR_BACKGROUND);
  drawCenteredText("Processing...", 0, HEADER_HEIGHT + TIME_HEIGHT + 40, 
                   SCREEN_WIDTH, 40, FONT_MEDIUM, COLOR_WARNING);
}

void DisplayManager::showError(String message) {
  tft.fillRect(0, HEADER_HEIGHT + TIME_HEIGHT, SCREEN_WIDTH, STATUS_HEIGHT, COLOR_ERROR);
  drawCenteredText(message, 0, HEADER_HEIGHT + TIME_HEIGHT + 40, 
                   SCREEN_WIDTH, 40, FONT_MEDIUM, COLOR_TEXT);
}

void DisplayManager::showPunchSuccess(String employeeName, String action, String timestamp) {
  clearScreen();
  
  // Draw header
  drawHeader("Punch Recorded");
  
  // Draw employee name
  drawCenteredText(employeeName, 0, 80, SCREEN_WIDTH, 40, FONT_LARGE, COLOR_SUCCESS);
  
  // Draw action
  String actionText = (action == "punch_in") ? "PUNCHED IN" : "PUNCHED OUT";
  drawCenteredText(actionText, 0, 130, SCREEN_WIDTH, 40, FONT_MEDIUM, COLOR_TEXT);
  
  // Draw timestamp
  drawCenteredText(timestamp, 0, 180, SCREEN_WIDTH, 30, FONT_SMALL, COLOR_INFO);
}

void DisplayManager::showAdminLogin() {
  clearScreen();
  drawHeader("Admin Access");
  
  drawCenteredText("Enter Admin PIN:", 0, 80, SCREEN_WIDTH, 30, FONT_MEDIUM, COLOR_TEXT);
  
  // Draw PIN entry area
  tft.drawRect(40, 120, SCREEN_WIDTH - 80, 40, COLOR_TEXT);
}

void DisplayManager::clearScreen() {
  tft.fillScreen(COLOR_BACKGROUND);
  screenDirty = true;
}

// Private helper methods

void DisplayManager::loadCalibration() {
  if (SPIFFS.exists(CALIBRATION_FILE)) {
    fs::File file = SPIFFS.open(CALIBRATION_FILE, "r");
    if (file) {
      file.read((uint8_t*)calData, sizeof(calData));
      file.close();
      calibrated = true;
      Serial.println("Touch calibration loaded");
    }
  }
}

void DisplayManager::saveCalibration() {
  fs::File file = SPIFFS.open(CALIBRATION_FILE, "w");
  if (file) {
    file.write((uint8_t*)calData, sizeof(calData));
    file.close();
    Serial.println("Touch calibration saved");
  }
}

void DisplayManager::performCalibration() {
  Serial.println("Performing touch calibration...");
  
  // Simple calibration - use default values for ESP32-2432S028R
  calData[0] = 300;   // Min X
  calData[1] = 3600;  // Max X  
  calData[2] = 300;   // Min Y
  calData[3] = 3600;  // Max Y
  calData[4] = 1;     // Orientation
  
  calibrated = true;
  saveCalibration();
  
  Serial.println("Touch calibration completed");
}

bool DisplayManager::validateTouch(uint16_t x, uint16_t y) {
  return (x >= 0 && x < SCREEN_WIDTH && y >= 0 && y < SCREEN_HEIGHT);
}

void DisplayManager::drawCenteredText(String text, uint16_t x, uint16_t y, uint16_t w, uint16_t h, 
                                     uint8_t font, uint16_t color) {
  tft.setTextColor(color);
  tft.setTextSize(font);
  
  int16_t textWidth = tft.textWidth(text);
  int16_t textHeight = tft.fontHeight();
  
  int16_t textX = x + (w - textWidth) / 2;
  int16_t textY = y + (h - textHeight) / 2;
  
  tft.setCursor(textX, textY);
  tft.print(text);
}

void DisplayManager::drawHeader(String title) {
  tft.fillRect(0, 0, SCREEN_WIDTH, HEADER_HEIGHT, COLOR_HEADER);
  drawCenteredText(title, 0, 0, SCREEN_WIDTH, HEADER_HEIGHT, FONT_MEDIUM, COLOR_TEXT);
}

void DisplayManager::drawFooter() {
  tft.fillRect(0, SCREEN_HEIGHT - BUTTON_HEIGHT, SCREEN_WIDTH, BUTTON_HEIGHT, COLOR_BUTTON);
  
  // Admin button
  tft.fillRect(SCREEN_WIDTH - 80, SCREEN_HEIGHT - BUTTON_HEIGHT + 5, 70, 30, COLOR_BUTTON);
  drawCenteredText("Admin", SCREEN_WIDTH - 80, SCREEN_HEIGHT - BUTTON_HEIGHT + 5, 70, 30, FONT_SMALL, COLOR_TEXT);
}

void DisplayManager::drawTime(String timeStr, uint16_t x, uint16_t y) {
  tft.setTextColor(COLOR_TEXT);
  tft.setTextSize(FONT_LARGE);
  
  int16_t textWidth = tft.textWidth(timeStr);
  int16_t textX = x + (SCREEN_WIDTH - textWidth) / 2;
  
  // Clear previous time
  tft.fillRect(x, y, SCREEN_WIDTH, 40, COLOR_BACKGROUND);
  
  tft.setCursor(textX, y);
  tft.print(timeStr);
}

// Stub implementations for other methods
bool DisplayManager::isButtonPressed(uint16_t x, uint16_t y, uint16_t w, uint16_t h) { return false; }
void DisplayManager::showAdminMenu() {}
void DisplayManager::showEmployeeList() {}
void DisplayManager::showEmployeeDetails(Employee& employee) {}
void DisplayManager::showAddEmployee() {}
void DisplayManager::showReports() {}
void DisplayManager::showSettings() {}
void DisplayManager::showWiFiSetup() {}
void DisplayManager::showTimeSettings() {}
void DisplayManager::showSystemInfo() {}
void DisplayManager::showKeyboard(String& input, String prompt) {}
void DisplayManager::showNumericKeypad(String& input, String prompt) {}
void DisplayManager::showConfirmDialog(String message, bool& confirmed) {}
void DisplayManager::showMessageBox(String title, String message, uint16_t color) {}
void DisplayManager::drawStatusBar() {}
void DisplayManager::drawDate(String dateStr, uint16_t x, uint16_t y) {}
void DisplayManager::fadeIn() {}
void DisplayManager::fadeOut() {}
void DisplayManager::slideTransition(bool left) {}
void DisplayManager::showDiagnostics() {}
void DisplayManager::printTouchCalibration() {}
void DisplayManager::testDisplay() {}
void DisplayManager::showSuccess(String message) {}
void DisplayManager::drawButton(uint16_t x, uint16_t y, uint16_t w, uint16_t h, String text, uint16_t bgColor, uint16_t textColor, bool pressed) {}
void DisplayManager::drawProgressBar(uint16_t x, uint16_t y, uint16_t w, uint16_t h, uint8_t progress, uint16_t color) {}
