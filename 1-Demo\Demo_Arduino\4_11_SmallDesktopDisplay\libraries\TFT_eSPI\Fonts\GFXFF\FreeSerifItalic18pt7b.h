const uint8_t FreeSerifItalic18pt7bBitmaps[] PROGMEM = {
  0x01, 0xC0, 0xF0, 0x3C, 0x0F, 0x03, 0x81, 0xE0, 0x70, 0x1C, 0x06, 0x01,
  0x80, 0xC0, 0x30, 0x0C, 0x02, 0x01, 0x80, 0x40, 0x10, 0x00, 0x00, 0x01,
  0x80, 0xF0, 0x3C, 0x06, 0x00, 0x38, 0x77, 0x8F, 0x78, 0xF7, 0x0E, 0x60,
  0xE6, 0x0C, 0xC1, 0x8C, 0x18, 0x81, 0x00, 0x00, 0x60, 0xC0, 0x0C, 0x38,
  0x03, 0x86, 0x00, 0x60, 0xC0, 0x0C, 0x38, 0x03, 0x06, 0x00, 0x60, 0xC0,
  0xFF, 0xFF, 0x1F, 0xFF, 0xE0, 0x61, 0xC0, 0x1C, 0x30, 0x03, 0x06, 0x00,
  0x61, 0xC0, 0x18, 0x30, 0x3F, 0xFF, 0xC7, 0xFF, 0xF8, 0x18, 0x30, 0x03,
  0x0E, 0x00, 0xE1, 0x80, 0x18, 0x30, 0x03, 0x0C, 0x00, 0xC1, 0x80, 0x18,
  0x70, 0x00, 0x00, 0x08, 0x00, 0x30, 0x00, 0x40, 0x0F, 0xC0, 0x61, 0xE1,
  0x86, 0xC6, 0x0D, 0x8C, 0x1A, 0x18, 0x24, 0x38, 0xC0, 0x39, 0x80, 0x7F,
  0x00, 0x7E, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0xDC, 0x03, 0x38,
  0x06, 0x32, 0x0C, 0x64, 0x18, 0xDC, 0x71, 0xB8, 0xC6, 0x39, 0x8C, 0x3F,
  0x30, 0x1F, 0x80, 0x18, 0x00, 0x30, 0x00, 0x60, 0x00, 0x07, 0x80, 0x60,
  0x0F, 0xE0, 0xE0, 0x0F, 0x0F, 0xB0, 0x0E, 0x04, 0x30, 0x07, 0x02, 0x18,
  0x07, 0x01, 0x18, 0x03, 0x00, 0x8C, 0x01, 0x80, 0x8C, 0x00, 0xC0, 0x4C,
  0x00, 0x60, 0x66, 0x1F, 0x30, 0x66, 0x1F, 0xCC, 0x63, 0x1C, 0x67, 0xE3,
  0x1C, 0x19, 0xE1, 0x1C, 0x04, 0x01, 0x8C, 0x02, 0x00, 0x8E, 0x01, 0x00,
  0xC7, 0x00, 0x80, 0xC3, 0x00, 0x80, 0x61, 0x80, 0xC0, 0x60, 0xC0, 0xC0,
  0x20, 0x70, 0xE0, 0x30, 0x1F, 0xC0, 0x10, 0x07, 0xC0, 0x00, 0x1E, 0x00,
  0x00, 0xFC, 0x00, 0x07, 0x18, 0x00, 0x18, 0x60, 0x00, 0xE1, 0x80, 0x03,
  0x8C, 0x00, 0x0E, 0x60, 0x00, 0x3B, 0x00, 0x00, 0xF0, 0x00, 0x07, 0x80,
  0x00, 0x7F, 0x1F, 0xC3, 0x3C, 0x1C, 0x38, 0x70, 0x61, 0xE1, 0xE3, 0x87,
  0x07, 0x8C, 0x3C, 0x0F, 0x60, 0xF0, 0x3D, 0x03, 0xC0, 0x78, 0x0F, 0x01,
  0xE0, 0x3E, 0x07, 0xC0, 0x7C, 0x77, 0x84, 0xFF, 0x8F, 0xE1, 0xF8, 0x0F,
  0x00, 0x3B, 0xDE, 0xE7, 0x33, 0x18, 0x80, 0x00, 0x80, 0x80, 0x80, 0x80,
  0xC0, 0xC0, 0xE0, 0x60, 0x70, 0x38, 0x18, 0x0C, 0x0E, 0x07, 0x03, 0x01,
  0x80, 0xC0, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x01, 0x00, 0x80, 0x40, 0x30,
  0x08, 0x04, 0x02, 0x00, 0x04, 0x01, 0x00, 0x80, 0x60, 0x10, 0x08, 0x04,
  0x03, 0x01, 0x80, 0xC0, 0x60, 0x30, 0x18, 0x0C, 0x0E, 0x07, 0x03, 0x81,
  0x80, 0xC0, 0xE0, 0x60, 0x30, 0x30, 0x18, 0x18, 0x08, 0x08, 0x08, 0x08,
  0x00, 0x06, 0x00, 0x60, 0x06, 0x0C, 0x43, 0xE4, 0xF1, 0x58, 0x0E, 0x00,
  0xF0, 0x74, 0xEE, 0x47, 0xC4, 0x30, 0x60, 0x06, 0x00, 0x60, 0x01, 0x80,
  0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80,
  0x01, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80,
  0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x31, 0xCE,
  0x31, 0x08, 0x98, 0xFF, 0xFF, 0x6F, 0xF6, 0x00, 0x06, 0x00, 0x0E, 0x00,
  0x0C, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x30, 0x00, 0x70, 0x00, 0x60, 0x00,
  0xE0, 0x00, 0xC0, 0x01, 0xC0, 0x03, 0x80, 0x03, 0x00, 0x07, 0x00, 0x06,
  0x00, 0x0E, 0x00, 0x0C, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x30, 0x00, 0x70,
  0x00, 0x60, 0x00, 0xE0, 0x00, 0x00, 0x78, 0x00, 0xC3, 0x00, 0xC1, 0xC0,
  0xC0, 0x60, 0xE0, 0x30, 0xE0, 0x1C, 0x70, 0x0E, 0x70, 0x07, 0x38, 0x03,
  0xBC, 0x01, 0xDC, 0x01, 0xEE, 0x00, 0xFF, 0x00, 0x7F, 0x80, 0x3B, 0x80,
  0x1D, 0xC0, 0x1E, 0xE0, 0x0E, 0x70, 0x0F, 0x38, 0x07, 0x1C, 0x07, 0x06,
  0x03, 0x83, 0x83, 0x80, 0xC3, 0x00, 0x1F, 0x00, 0x00, 0xF0, 0x7F, 0x00,
  0x70, 0x07, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x01, 0xC0, 0x1C, 0x01,
  0xC0, 0x38, 0x03, 0x80, 0x38, 0x03, 0x80, 0x70, 0x07, 0x00, 0x70, 0x0E,
  0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x1E, 0x0F, 0xF8, 0x01, 0xF0, 0x07, 0xFC,
  0x0C, 0x3E, 0x10, 0x1F, 0x20, 0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x0F,
  0x00, 0x1E, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x30, 0x00, 0x70, 0x00, 0xE0,
  0x01, 0xC0, 0x03, 0x80, 0x07, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x38, 0x04,
  0x30, 0x0C, 0x7F, 0xF8, 0xFF, 0xF0, 0x00, 0x7C, 0x00, 0xFF, 0x00, 0xC3,
  0xC0, 0x80, 0xF0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1C, 0x00, 0x1C, 0x00,
  0x38, 0x00, 0xF0, 0x03, 0xFC, 0x00, 0x1F, 0x00, 0x03, 0xC0, 0x01, 0xE0,
  0x00, 0x70, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x06, 0x00, 0x07,
  0x00, 0x03, 0x07, 0x87, 0x03, 0xFF, 0x00, 0xFC, 0x00, 0x00, 0x01, 0x80,
  0x01, 0x80, 0x01, 0xC0, 0x01, 0xE0, 0x01, 0xF0, 0x01, 0xB0, 0x01, 0xB8,
  0x01, 0x9C, 0x01, 0x8C, 0x00, 0x86, 0x00, 0x87, 0x00, 0x83, 0x80, 0x81,
  0x80, 0x81, 0xC0, 0xC0, 0xE0, 0xC0, 0x70, 0xFF, 0xFF, 0x7F, 0xFF, 0x00,
  0x1C, 0x00, 0x0C, 0x00, 0x0E, 0x00, 0x07, 0x00, 0x03, 0x80, 0x01, 0x80,
  0x01, 0xFF, 0x01, 0xFF, 0x02, 0x00, 0x02, 0x00, 0x06, 0x00, 0x07, 0x00,
  0x0F, 0xC0, 0x0F, 0xF0, 0x00, 0xF8, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x1C,
  0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x08, 0x00, 0x18,
  0x00, 0x30, 0x00, 0x30, 0x70, 0xE0, 0xFF, 0x80, 0x7E, 0x00, 0x00, 0x03,
  0x80, 0x1F, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38,
  0x00, 0x3C, 0x00, 0x3D, 0xF0, 0x1F, 0xFE, 0x1F, 0x0F, 0x8E, 0x03, 0xC7,
  0x00, 0xF7, 0x00, 0x7B, 0x80, 0x3D, 0x80, 0x1E, 0xC0, 0x0F, 0x60, 0x0F,
  0xB0, 0x07, 0x98, 0x03, 0xC4, 0x03, 0xC3, 0x03, 0xC0, 0xC3, 0x80, 0x1F,
  0x00, 0x3F, 0xFF, 0x7F, 0xFE, 0x40, 0x0E, 0x80, 0x0C, 0x00, 0x18, 0x00,
  0x18, 0x00, 0x30, 0x00, 0x70, 0x00, 0x60, 0x00, 0xC0, 0x01, 0xC0, 0x01,
  0x80, 0x03, 0x80, 0x03, 0x00, 0x06, 0x00, 0x0E, 0x00, 0x0C, 0x00, 0x1C,
  0x00, 0x18, 0x00, 0x30, 0x00, 0x70, 0x00, 0x60, 0x00, 0xE0, 0x00, 0x00,
  0xF8, 0x03, 0x0E, 0x06, 0x06, 0x0C, 0x03, 0x0C, 0x03, 0x0C, 0x03, 0x0C,
  0x03, 0x0E, 0x06, 0x07, 0x8E, 0x07, 0xD8, 0x03, 0xE0, 0x07, 0xF0, 0x1C,
  0xF8, 0x30, 0x3C, 0x60, 0x1C, 0x60, 0x0E, 0xC0, 0x06, 0xC0, 0x06, 0xC0,
  0x06, 0xC0, 0x06, 0xE0, 0x0C, 0x60, 0x18, 0x38, 0x30, 0x0F, 0xC0, 0x01,
  0xF8, 0x07, 0x8C, 0x0E, 0x06, 0x1E, 0x02, 0x3C, 0x03, 0x3C, 0x03, 0x78,
  0x03, 0x78, 0x03, 0x78, 0x03, 0x78, 0x07, 0x78, 0x07, 0x78, 0x07, 0x3C,
  0x0E, 0x3E, 0x1E, 0x1F, 0xEE, 0x07, 0x9C, 0x00, 0x38, 0x00, 0x78, 0x00,
  0x70, 0x01, 0xE0, 0x03, 0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xE0, 0x00, 0x0C,
  0x3C, 0x78, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x0F, 0x1E, 0x18,
  0x00, 0x07, 0x03, 0xC1, 0xE0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x02, 0x03, 0x81, 0xC0, 0xE0, 0x30, 0x10, 0x10, 0x10, 0x00, 0x00,
  0x00, 0x00, 0xC0, 0x01, 0xF0, 0x01, 0xF8, 0x01, 0xF8, 0x01, 0xF0, 0x01,
  0xF0, 0x03, 0xF0, 0x03, 0xF0, 0x00, 0xF0, 0x00, 0x3E, 0x00, 0x07, 0xE0,
  0x00, 0x7E, 0x00, 0x03, 0xE0, 0x00, 0x3E, 0x00, 0x03, 0xF0, 0x00, 0x3F,
  0x00, 0x03, 0xC0, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF,
  0xFF, 0xC0, 0xC0, 0x00, 0x3C, 0x00, 0x07, 0xE0, 0x00, 0x7E, 0x00, 0x07,
  0xE0, 0x00, 0x3E, 0x00, 0x03, 0xE0, 0x00, 0x3F, 0x00, 0x03, 0xC0, 0x01,
  0xF0, 0x01, 0xF8, 0x01, 0xF8, 0x01, 0xF0, 0x01, 0xF0, 0x03, 0xF0, 0x03,
  0xF0, 0x00, 0xF0, 0x00, 0x20, 0x00, 0x00, 0x0F, 0x81, 0x86, 0x30, 0x33,
  0x03, 0x30, 0x30, 0x03, 0x00, 0x60, 0x0E, 0x01, 0xC0, 0x38, 0x06, 0x00,
  0xC0, 0x08, 0x01, 0x00, 0x10, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06,
  0x00, 0xF0, 0x0F, 0x00, 0x60, 0x00, 0x00, 0x7F, 0x00, 0x03, 0xFF, 0xE0,
  0x07, 0x80, 0xF0, 0x0E, 0x00, 0x38, 0x1C, 0x00, 0x0C, 0x38, 0x0E, 0x06,
  0x70, 0x3F, 0xE2, 0x70, 0x71, 0xE3, 0xF0, 0x60, 0xE1, 0xE0, 0xC0, 0xC1,
  0xE0, 0xC0, 0xC1, 0xE1, 0x81, 0xC1, 0xE1, 0x81, 0xC1, 0xE1, 0x81, 0x82,
  0xE1, 0x83, 0x82, 0x71, 0x83, 0x86, 0x71, 0xC7, 0x8C, 0x38, 0xF9, 0xF8,
  0x3C, 0xF0, 0xF0, 0x1E, 0x00, 0x00, 0x0F, 0x80, 0x30, 0x03, 0xFF, 0xE0,
  0x00, 0x7F, 0x00, 0x00, 0x03, 0x00, 0x00, 0x18, 0x00, 0x01, 0xC0, 0x00,
  0x1E, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x5E, 0x00, 0x04, 0xF0,
  0x00, 0x63, 0x80, 0x02, 0x1C, 0x00, 0x20, 0xE0, 0x01, 0x07, 0x00, 0x10,
  0x3C, 0x01, 0xFF, 0xE0, 0x0F, 0xFF, 0x00, 0xC0, 0x38, 0x04, 0x01, 0xC0,
  0x60, 0x0E, 0x06, 0x00, 0x78, 0x30, 0x03, 0xC3, 0x00, 0x1E, 0x38, 0x00,
  0xFB, 0xF0, 0x1F, 0xE0, 0x07, 0xFF, 0x80, 0x0F, 0xFF, 0x00, 0x78, 0x3C,
  0x03, 0xC0, 0xF0, 0x1E, 0x07, 0x80, 0xE0, 0x3C, 0x07, 0x01, 0xE0, 0x78,
  0x1E, 0x03, 0x83, 0xE0, 0x1F, 0xF8, 0x01, 0xFF, 0xC0, 0x0F, 0x0F, 0x00,
  0x70, 0x3C, 0x03, 0x80, 0xF0, 0x3C, 0x07, 0x81, 0xC0, 0x3C, 0x0E, 0x01,
  0xE0, 0xF0, 0x0F, 0x07, 0x80, 0xF0, 0x38, 0x0F, 0x81, 0xC1, 0xF8, 0x1F,
  0xFF, 0x83, 0xFF, 0xE0, 0x00, 0x00, 0x3F, 0x08, 0x07, 0xFF, 0xC0, 0xF8,
  0x3E, 0x0F, 0x00, 0x70, 0xF0, 0x03, 0x8F, 0x00, 0x08, 0xF0, 0x00, 0x47,
  0x80, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00, 0x01, 0xE0, 0x00,
  0x0F, 0x00, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xF0,
  0x00, 0x03, 0x80, 0x02, 0x1E, 0x00, 0x20, 0x78, 0x02, 0x03, 0xE0, 0x60,
  0x07, 0xFE, 0x00, 0x0F, 0xC0, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0xFF, 0xFC,
  0x00, 0x78, 0x1F, 0x00, 0x3C, 0x03, 0xC0, 0x1E, 0x00, 0xF0, 0x0E, 0x00,
  0x78, 0x07, 0x00, 0x1E, 0x07, 0x80, 0x0F, 0x03, 0x80, 0x07, 0x81, 0xC0,
  0x03, 0xC1, 0xE0, 0x01, 0xE0, 0xF0, 0x00, 0xF0, 0x70, 0x00, 0x78, 0x38,
  0x00, 0x78, 0x3C, 0x00, 0x3C, 0x1E, 0x00, 0x3E, 0x0E, 0x00, 0x1E, 0x0F,
  0x00, 0x1E, 0x07, 0x80, 0x1E, 0x03, 0x80, 0x3E, 0x01, 0xC0, 0x7E, 0x01,
  0xFF, 0xFC, 0x03, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFC, 0x07, 0xFF, 0xF0,
  0x1E, 0x01, 0xC0, 0x78, 0x02, 0x01, 0xE0, 0x08, 0x07, 0x00, 0x00, 0x1C,
  0x08, 0x00, 0xF0, 0x60, 0x03, 0x83, 0x80, 0x0F, 0xFC, 0x00, 0x7F, 0xF0,
  0x01, 0xE0, 0xC0, 0x07, 0x03, 0x00, 0x1C, 0x08, 0x00, 0xF0, 0x20, 0x03,
  0x80, 0x00, 0x0E, 0x00, 0x00, 0x78, 0x00, 0x81, 0xE0, 0x06, 0x07, 0x00,
  0x38, 0x1C, 0x03, 0xC0, 0xFF, 0xFF, 0x0F, 0xFF, 0xFC, 0x00, 0x07, 0xFF,
  0xFC, 0x07, 0xFF, 0xF0, 0x1E, 0x01, 0xC0, 0x78, 0x02, 0x01, 0xE0, 0x08,
  0x07, 0x00, 0x20, 0x1C, 0x00, 0x00, 0xF0, 0x20, 0x03, 0x81, 0x80, 0x0E,
  0x0C, 0x00, 0x7F, 0xF0, 0x01, 0xFF, 0xC0, 0x07, 0x03, 0x00, 0x1C, 0x0C,
  0x00, 0xF0, 0x20, 0x03, 0xC0, 0x00, 0x0E, 0x00, 0x00, 0x78, 0x00, 0x01,
  0xE0, 0x00, 0x07, 0x00, 0x00, 0x1C, 0x00, 0x00, 0xF8, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x00, 0x3F, 0x02, 0x01, 0xFF, 0x88, 0x0F, 0x81, 0xF0, 0x3C,
  0x01, 0xE0, 0xF0, 0x01, 0xC3, 0xC0, 0x01, 0x0F, 0x80, 0x02, 0x1E, 0x00,
  0x00, 0x7C, 0x00, 0x00, 0xF0, 0x00, 0x01, 0xE0, 0x00, 0x07, 0xC0, 0x00,
  0x0F, 0x00, 0x3F, 0xFE, 0x00, 0x1E, 0x3C, 0x00, 0x38, 0x78, 0x00, 0x70,
  0xF0, 0x00, 0xE0, 0xE0, 0x01, 0xC1, 0xE0, 0x07, 0x01, 0xE0, 0x0E, 0x01,
  0xF0, 0x3C, 0x01, 0xFF, 0xF0, 0x00, 0xFF, 0x00, 0x00, 0x07, 0xFC, 0x3F,
  0xE0, 0x3E, 0x00, 0xF0, 0x07, 0x80, 0x1C, 0x00, 0xF0, 0x03, 0x80, 0x1C,
  0x00, 0xF0, 0x03, 0x80, 0x1E, 0x00, 0x70, 0x03, 0x80, 0x1E, 0x00, 0x70,
  0x03, 0x80, 0x1E, 0x00, 0x70, 0x03, 0x80, 0x1F, 0xFF, 0xF0, 0x03, 0xFF,
  0xFE, 0x00, 0x70, 0x03, 0xC0, 0x0E, 0x00, 0x70, 0x03, 0xC0, 0x0E, 0x00,
  0x70, 0x03, 0xC0, 0x0E, 0x00, 0x78, 0x03, 0xC0, 0x0E, 0x00, 0x78, 0x01,
  0xC0, 0x0E, 0x00, 0x78, 0x01, 0xC0, 0x0E, 0x00, 0x78, 0x03, 0xE0, 0x3F,
  0xE1, 0xFF, 0x00, 0x07, 0xFC, 0x07, 0xC0, 0x1E, 0x00, 0x78, 0x01, 0xC0,
  0x07, 0x00, 0x1C, 0x00, 0xF0, 0x03, 0x80, 0x0E, 0x00, 0x78, 0x01, 0xE0,
  0x07, 0x00, 0x1C, 0x00, 0xF0, 0x03, 0x80, 0x0E, 0x00, 0x78, 0x01, 0xE0,
  0x07, 0x00, 0x1C, 0x00, 0xF0, 0x0F, 0xF8, 0x00, 0x00, 0xFF, 0x80, 0x0F,
  0x00, 0x07, 0x80, 0x03, 0x80, 0x01, 0xC0, 0x01, 0xE0, 0x00, 0xF0, 0x00,
  0x70, 0x00, 0x38, 0x00, 0x3C, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x0F, 0x00,
  0x07, 0x80, 0x03, 0x80, 0x01, 0xC0, 0x01, 0xE0, 0x00, 0xE0, 0x00, 0x70,
  0x1E, 0x78, 0x0F, 0x38, 0x07, 0xF8, 0x01, 0xF0, 0x00, 0x07, 0xFC, 0x7F,
  0x80, 0xF8, 0x0F, 0x00, 0x38, 0x07, 0x00, 0x3C, 0x07, 0x00, 0x1C, 0x06,
  0x00, 0x0E, 0x06, 0x00, 0x07, 0x0C, 0x00, 0x07, 0x8C, 0x00, 0x03, 0x9C,
  0x00, 0x01, 0xD8, 0x00, 0x01, 0xFC, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x73,
  0x80, 0x00, 0x39, 0xE0, 0x00, 0x3C, 0x78, 0x00, 0x1C, 0x1C, 0x00, 0x0E,
  0x0F, 0x00, 0x07, 0x03, 0x80, 0x07, 0x81, 0xE0, 0x03, 0x80, 0x70, 0x01,
  0xC0, 0x3C, 0x01, 0xE0, 0x1F, 0x03, 0xFE, 0x3F, 0xE0, 0x07, 0xFC, 0x00,
  0x1F, 0x00, 0x01, 0xE0, 0x00, 0x1E, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00,
  0x01, 0xC0, 0x00, 0x3C, 0x00, 0x03, 0x80, 0x00, 0x38, 0x00, 0x07, 0x80,
  0x00, 0x78, 0x00, 0x07, 0x00, 0x00, 0x70, 0x00, 0x0F, 0x00, 0x00, 0xE0,
  0x00, 0x0E, 0x00, 0x11, 0xE0, 0x03, 0x1E, 0x00, 0x61, 0xC0, 0x06, 0x1C,
  0x01, 0xE3, 0xFF, 0xFC, 0xFF, 0xFF, 0xC0, 0x07, 0xF0, 0x00, 0x7E, 0x03,
  0xE0, 0x01, 0xF0, 0x03, 0xC0, 0x03, 0xE0, 0x07, 0x80, 0x0F, 0x80, 0x1F,
  0x00, 0x37, 0x00, 0x2E, 0x00, 0x5E, 0x00, 0x5C, 0x01, 0xB8, 0x01, 0xB8,
  0x06, 0x70, 0x02, 0x78, 0x09, 0xE0, 0x04, 0x70, 0x33, 0xC0, 0x08, 0xE0,
  0xC7, 0x00, 0x31, 0xC1, 0x0E, 0x00, 0x43, 0x86, 0x3C, 0x00, 0x87, 0x18,
  0x70, 0x03, 0x0E, 0x20, 0xE0, 0x06, 0x1C, 0xC3, 0xC0, 0x08, 0x3B, 0x07,
  0x80, 0x10, 0x7C, 0x0E, 0x00, 0x60, 0x78, 0x1C, 0x00, 0x80, 0xE0, 0x78,
  0x03, 0x01, 0x80, 0xF0, 0x07, 0x03, 0x03, 0xE0, 0x3F, 0x84, 0x1F, 0xF0,
  0x00, 0x07, 0xC0, 0x3F, 0xC0, 0x78, 0x03, 0xE0, 0x0E, 0x00, 0x70, 0x03,
  0xC0, 0x18, 0x01, 0xF0, 0x0E, 0x00, 0x6C, 0x03, 0x00, 0x1B, 0x80, 0xC0,
  0x0C, 0xE0, 0x30, 0x03, 0x18, 0x1C, 0x00, 0xC7, 0x06, 0x00, 0x30, 0xC1,
  0x80, 0x18, 0x38, 0xE0, 0x06, 0x06, 0x30, 0x01, 0x81, 0x8C, 0x00, 0xC0,
  0x73, 0x00, 0x30, 0x0D, 0xC0, 0x0C, 0x03, 0xE0, 0x03, 0x00, 0x78, 0x01,
  0x80, 0x1E, 0x00, 0x60, 0x07, 0x00, 0x38, 0x00, 0xC0, 0x0E, 0x00, 0x30,
  0x0F, 0xE0, 0x04, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0xFF, 0xE0, 0x07, 0xC1,
  0xE0, 0x1E, 0x01, 0xE0, 0x78, 0x01, 0xC1, 0xE0, 0x03, 0xC7, 0x80, 0x07,
  0x9F, 0x00, 0x0F, 0x3C, 0x00, 0x1E, 0xF8, 0x00, 0x3D, 0xE0, 0x00, 0xFF,
  0xC0, 0x01, 0xEF, 0x80, 0x03, 0xDE, 0x00, 0x0F, 0xBC, 0x00, 0x1E, 0x78,
  0x00, 0x7C, 0xF0, 0x00, 0xF1, 0xE0, 0x03, 0xC1, 0xC0, 0x0F, 0x03, 0xC0,
  0x3C, 0x03, 0xC1, 0xF0, 0x03, 0xFF, 0x80, 0x01, 0xFC, 0x00, 0x00, 0x07,
  0xFF, 0xC0, 0x07, 0xFF, 0xC0, 0x0E, 0x0F, 0x80, 0x78, 0x1F, 0x01, 0xC0,
  0x3C, 0x07, 0x00, 0xF0, 0x1C, 0x03, 0xC0, 0xF0, 0x0F, 0x03, 0x80, 0x78,
  0x0E, 0x01, 0xE0, 0x78, 0x1F, 0x01, 0xFF, 0xF8, 0x07, 0x7F, 0x00, 0x1C,
  0x00, 0x00, 0xF0, 0x00, 0x03, 0x80, 0x00, 0x0E, 0x00, 0x00, 0x78, 0x00,
  0x01, 0xE0, 0x00, 0x07, 0x00, 0x00, 0x1C, 0x00, 0x00, 0xF0, 0x00, 0x0F,
  0xF0, 0x00, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0xFF, 0xE0, 0x03, 0xC1, 0xE0,
  0x1E, 0x01, 0xC0, 0x78, 0x03, 0xC1, 0xE0, 0x03, 0x87, 0x80, 0x07, 0x8F,
  0x00, 0x0F, 0x3C, 0x00, 0x1E, 0x78, 0x00, 0x3D, 0xE0, 0x00, 0x7B, 0xC0,
  0x01, 0xFF, 0x80, 0x03, 0xDE, 0x00, 0x07, 0xBC, 0x00, 0x1F, 0x78, 0x00,
  0x3C, 0xF0, 0x00, 0xF1, 0xE0, 0x01, 0xE3, 0xC0, 0x07, 0x83, 0x80, 0x1E,
  0x07, 0x80, 0x78, 0x07, 0x01, 0xC0, 0x03, 0xDE, 0x00, 0x01, 0xC0, 0x00,
  0x06, 0x00, 0x00, 0x18, 0x00, 0x10, 0x7F, 0xC0, 0xC3, 0xFF, 0xFF, 0x08,
  0x07, 0xF0, 0x00, 0x07, 0xFF, 0x80, 0x0F, 0xFF, 0x00, 0x78, 0x3C, 0x03,
  0xC0, 0xF0, 0x1E, 0x07, 0x80, 0xE0, 0x3C, 0x07, 0x01, 0xE0, 0x78, 0x1E,
  0x03, 0x83, 0xF0, 0x1F, 0xFE, 0x01, 0xFF, 0xC0, 0x0F, 0x38, 0x00, 0x71,
  0xE0, 0x03, 0x87, 0x00, 0x3C, 0x38, 0x01, 0xC1, 0xE0, 0x0E, 0x07, 0x00,
  0xF0, 0x3C, 0x07, 0x81, 0xE0, 0x38, 0x07, 0x01, 0xC0, 0x3C, 0x1E, 0x00,
  0xF3, 0xFC, 0x07, 0xC0, 0x00, 0xF8, 0x81, 0xFF, 0xC1, 0xE1, 0xE1, 0xE0,
  0x70, 0xF0, 0x10, 0x78, 0x08, 0x3C, 0x00, 0x1F, 0x00, 0x07, 0x80, 0x01,
  0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0xF8,
  0x80, 0x3C, 0x40, 0x1E, 0x20, 0x0F, 0x38, 0x07, 0x9E, 0x07, 0x8F, 0x87,
  0x84, 0x7F, 0xC2, 0x0F, 0x80, 0x3F, 0xFF, 0xF7, 0xFF, 0xFF, 0x70, 0x78,
  0x76, 0x07, 0x02, 0xC0, 0x70, 0x28, 0x0F, 0x02, 0x00, 0xF0, 0x00, 0x0E,
  0x00, 0x01, 0xE0, 0x00, 0x1E, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00, 0x03,
  0xC0, 0x00, 0x3C, 0x00, 0x03, 0x80, 0x00, 0x38, 0x00, 0x07, 0x80, 0x00,
  0x70, 0x00, 0x07, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00, 0x01, 0xF0, 0x00,
  0xFF, 0xE0, 0x00, 0x7F, 0xE0, 0xFE, 0x3F, 0x00, 0x78, 0x3C, 0x00, 0x60,
  0xF0, 0x01, 0x81, 0xE0, 0x03, 0x03, 0xC0, 0x06, 0x07, 0x00, 0x08, 0x1E,
  0x00, 0x30, 0x3C, 0x00, 0x60, 0x70, 0x00, 0x81, 0xE0, 0x01, 0x03, 0xC0,
  0x06, 0x07, 0x80, 0x0C, 0x0E, 0x00, 0x10, 0x3C, 0x00, 0x60, 0x78, 0x00,
  0xC0, 0xF0, 0x01, 0x01, 0xE0, 0x06, 0x03, 0xC0, 0x08, 0x03, 0xC0, 0x30,
  0x07, 0xC1, 0xC0, 0x07, 0xFF, 0x00, 0x03, 0xF8, 0x00, 0x00, 0xFF, 0x01,
  0xFB, 0xE0, 0x07, 0x8E, 0x00, 0x18, 0x78, 0x01, 0x83, 0xC0, 0x0C, 0x1E,
  0x00, 0xC0, 0xF0, 0x06, 0x03, 0x80, 0x60, 0x1C, 0x02, 0x00, 0xE0, 0x30,
  0x07, 0x83, 0x00, 0x3C, 0x10, 0x01, 0xE1, 0x80, 0x07, 0x08, 0x00, 0x38,
  0x80, 0x01, 0xC4, 0x00, 0x0E, 0x40, 0x00, 0x7C, 0x00, 0x03, 0xE0, 0x00,
  0x0E, 0x00, 0x00, 0x70, 0x00, 0x03, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFF,
  0x3F, 0xC3, 0xFB, 0xE0, 0x78, 0x07, 0x8E, 0x03, 0xC0, 0x18, 0x78, 0x0E,
  0x01, 0x83, 0xC0, 0x70, 0x0C, 0x1E, 0x03, 0x80, 0x40, 0xF0, 0x3C, 0x06,
  0x03, 0x81, 0xE0, 0x60, 0x1C, 0x17, 0x83, 0x00, 0xE0, 0xBC, 0x30, 0x07,
  0x09, 0xE1, 0x00, 0x38, 0x47, 0x18, 0x01, 0xE4, 0x38, 0x80, 0x0F, 0x21,
  0xCC, 0x00, 0x7A, 0x0E, 0x40, 0x01, 0xD0, 0x76, 0x00, 0x0F, 0x03, 0xA0,
  0x00, 0x78, 0x1F, 0x00, 0x03, 0x80, 0xF0, 0x00, 0x1C, 0x07, 0x00, 0x00,
  0xC0, 0x38, 0x00, 0x06, 0x00, 0x80, 0x00, 0x20, 0x04, 0x00, 0x00, 0x0F,
  0xF8, 0x7F, 0x03, 0xE0, 0x3E, 0x01, 0xC0, 0x18, 0x01, 0xE0, 0x30, 0x01,
  0xE0, 0x60, 0x00, 0xE0, 0xC0, 0x00, 0xF1, 0xC0, 0x00, 0x71, 0x80, 0x00,
  0x7B, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00, 0x00,
  0x3C, 0x00, 0x00, 0x7E, 0x00, 0x00, 0xCE, 0x00, 0x01, 0x8F, 0x00, 0x01,
  0x07, 0x00, 0x03, 0x07, 0x00, 0x06, 0x07, 0x80, 0x0C, 0x03, 0x80, 0x18,
  0x03, 0xC0, 0x78, 0x03, 0xE0, 0xFE, 0x1F, 0xF8, 0xFF, 0x87, 0xE7, 0xC0,
  0x38, 0x70, 0x06, 0x0E, 0x01, 0x81, 0xE0, 0x30, 0x1C, 0x0C, 0x03, 0x83,
  0x00, 0x78, 0xC0, 0x07, 0x30, 0x00, 0xE4, 0x00, 0x1D, 0x80, 0x03, 0xE0,
  0x00, 0x38, 0x00, 0x0F, 0x00, 0x01, 0xC0, 0x00, 0x38, 0x00, 0x07, 0x00,
  0x01, 0xE0, 0x00, 0x38, 0x00, 0x07, 0x00, 0x01, 0xE0, 0x00, 0x7C, 0x00,
  0x3F, 0xF0, 0x00, 0x07, 0xFF, 0xFC, 0x3F, 0xFF, 0xE0, 0xE0, 0x0F, 0x82,
  0x00, 0x3C, 0x18, 0x01, 0xE0, 0x40, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x03,
  0xC0, 0x00, 0x0E, 0x00, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00,
  0x00, 0xF0, 0x00, 0x07, 0x80, 0x00, 0x1C, 0x00, 0x00, 0xF0, 0x00, 0x07,
  0x80, 0x00, 0x3C, 0x00, 0xC1, 0xE0, 0x02, 0x0F, 0x00, 0x18, 0x38, 0x01,
  0xE1, 0xFF, 0xFF, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xF8, 0x0C, 0x00, 0xC0,
  0x06, 0x00, 0x30, 0x01, 0x80, 0x18, 0x00, 0xC0, 0x06, 0x00, 0x30, 0x03,
  0x00, 0x18, 0x00, 0xC0, 0x06, 0x00, 0x60, 0x03, 0x00, 0x18, 0x01, 0xC0,
  0x0C, 0x00, 0x60, 0x03, 0x00, 0x30, 0x01, 0x80, 0x0C, 0x00, 0x60, 0x06,
  0x00, 0x30, 0x01, 0xF8, 0x00, 0xE0, 0x0E, 0x00, 0x60, 0x07, 0x00, 0x30,
  0x03, 0x80, 0x18, 0x01, 0xC0, 0x0C, 0x00, 0xC0, 0x0E, 0x00, 0x60, 0x07,
  0x00, 0x30, 0x03, 0x80, 0x18, 0x01, 0xC0, 0x0C, 0x00, 0xC0, 0x0E, 0x00,
  0x60, 0x07, 0x00, 0x30, 0x03, 0xF0, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60,
  0x0E, 0x00, 0xC0, 0x0C, 0x00, 0xC0, 0x0C, 0x01, 0x80, 0x18, 0x01, 0x80,
  0x18, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x60, 0x06, 0x00,
  0x60, 0x06, 0x00, 0xC0, 0x0C, 0x00, 0xC0, 0x0C, 0x0F, 0xC0, 0x03, 0x80,
  0x07, 0x00, 0x1F, 0x00, 0x36, 0x00, 0xCE, 0x01, 0x8C, 0x06, 0x1C, 0x0C,
  0x18, 0x38, 0x38, 0x60, 0x31, 0xC0, 0x73, 0x00, 0x6E, 0x00, 0xE0, 0xFF,
  0xFF, 0xFF, 0xFF, 0xF0, 0xE3, 0x8F, 0x0E, 0x18, 0x30, 0x01, 0xEC, 0x0E,
  0x58, 0x30, 0x70, 0xE0, 0xC3, 0x81, 0x86, 0x07, 0x1C, 0x0C, 0x38, 0x18,
  0xE0, 0x71, 0xC0, 0xE3, 0x83, 0x87, 0x0B, 0x2F, 0x36, 0xCF, 0xCF, 0x1F,
  0x1C, 0x00, 0x03, 0x00, 0x1F, 0x00, 0x07, 0x00, 0x07, 0x00, 0x06, 0x00,
  0x0E, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x0C, 0x00, 0x1C, 0x7C, 0x1C, 0xFE,
  0x19, 0x8F, 0x1A, 0x07, 0x3C, 0x07, 0x38, 0x07, 0x38, 0x07, 0x70, 0x0E,
  0x70, 0x0E, 0x70, 0x1C, 0x60, 0x18, 0xE0, 0x30, 0xE0, 0x60, 0xE1, 0xC0,
  0x3F, 0x00, 0x01, 0xF0, 0x38, 0xC3, 0x8E, 0x78, 0x73, 0x80, 0x3C, 0x01,
  0xC0, 0x1E, 0x00, 0xF0, 0x07, 0x80, 0x3C, 0x01, 0xE0, 0x47, 0x84, 0x3F,
  0xC0, 0x7C, 0x00, 0x00, 0x01, 0x80, 0x07, 0xC0, 0x00, 0xE0, 0x00, 0x60,
  0x00, 0x30, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x0C, 0x00, 0x06, 0x00, 0xF7,
  0x01, 0xC7, 0x81, 0xC3, 0x81, 0xC1, 0xC1, 0xE0, 0xE0, 0xE0, 0x60, 0xF0,
  0x30, 0x78, 0x38, 0x78, 0x18, 0x3C, 0x0C, 0x1E, 0x0C, 0x0F, 0x0E, 0x27,
  0xCB, 0x21, 0xF9, 0xE0, 0x78, 0xE0, 0x00, 0xF0, 0x1C, 0xC3, 0x86, 0x38,
  0x33, 0xC3, 0x1C, 0x31, 0xE3, 0x1F, 0xE0, 0xF0, 0x07, 0x80, 0x3C, 0x01,
  0xE0, 0x47, 0x84, 0x3F, 0xC0, 0x7C, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x33,
  0x00, 0x06, 0x30, 0x00, 0xC0, 0x00, 0x0C, 0x00, 0x01, 0xC0, 0x00, 0x18,
  0x00, 0x01, 0x80, 0x00, 0x38, 0x00, 0x3F, 0xF8, 0x03, 0xFF, 0x80, 0x03,
  0x00, 0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0x70, 0x00, 0x06, 0x00, 0x00,
  0x60, 0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x0C, 0x00, 0x00, 0xC0, 0x00,
  0x1C, 0x00, 0x01, 0xC0, 0x00, 0x18, 0x00, 0x01, 0x80, 0x00, 0x18, 0x00,
  0x03, 0x00, 0x00, 0x30, 0x00, 0xC6, 0x00, 0x0C, 0xC0, 0x00, 0x78, 0x00,
  0x00, 0x01, 0xF8, 0x07, 0x1F, 0x0E, 0x0F, 0x0C, 0x0E, 0x18, 0x0E, 0x18,
  0x0E, 0x18, 0x1E, 0x18, 0x3C, 0x0C, 0x78, 0x07, 0xE0, 0x08, 0x00, 0x18,
  0x00, 0x1E, 0x00, 0x0F, 0xE0, 0x13, 0xF0, 0x60, 0x78, 0xC0, 0x38, 0xC0,
  0x18, 0xC0, 0x18, 0xC0, 0x30, 0x60, 0x60, 0x3F, 0x80, 0x03, 0x00, 0x1F,
  0x00, 0x07, 0x00, 0x07, 0x00, 0x06, 0x00, 0x06, 0x00, 0x0E, 0x00, 0x0E,
  0x00, 0x0C, 0x00, 0x1C, 0x38, 0x1C, 0x7C, 0x1C, 0xCC, 0x19, 0x0C, 0x3A,
  0x0C, 0x3C, 0x1C, 0x3C, 0x18, 0x38, 0x18, 0x70, 0x38, 0x70, 0x38, 0x70,
  0x30, 0x60, 0x72, 0xE0, 0x76, 0xE0, 0x7C, 0xC0, 0x70, 0x03, 0x03, 0xC1,
  0xE0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x7E, 0x0F, 0x03, 0x81, 0x81,
  0xC0, 0xE0, 0x70, 0x30, 0x38, 0x1C, 0x1C, 0x4C, 0x47, 0xC3, 0xC0, 0x00,
  0x0C, 0x00, 0x3C, 0x00, 0x78, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x18, 0x03, 0xF0, 0x00, 0xE0, 0x01, 0x80, 0x03, 0x00,
  0x0E, 0x00, 0x1C, 0x00, 0x30, 0x00, 0x60, 0x01, 0xC0, 0x03, 0x80, 0x06,
  0x00, 0x0C, 0x00, 0x38, 0x00, 0x70, 0x00, 0xC0, 0x03, 0x80, 0x06, 0x00,
  0x0C, 0x06, 0x30, 0x0C, 0xC0, 0x0F, 0x00, 0x00, 0x03, 0x00, 0x3E, 0x00,
  0x1C, 0x00, 0x38, 0x00, 0x60, 0x01, 0xC0, 0x03, 0x80, 0x07, 0x00, 0x0C,
  0x00, 0x38, 0xFC, 0x70, 0x60, 0xE1, 0x81, 0x86, 0x07, 0x10, 0x0E, 0x40,
  0x1B, 0x80, 0x3F, 0x00, 0xE7, 0x01, 0xCE, 0x03, 0x0C, 0x06, 0x1C, 0x5C,
  0x1D, 0x38, 0x3E, 0x60, 0x38, 0x03, 0x1F, 0x07, 0x07, 0x06, 0x0E, 0x0E,
  0x0E, 0x0C, 0x1C, 0x1C, 0x18, 0x38, 0x38, 0x38, 0x30, 0x70, 0x70, 0x70,
  0x64, 0xE4, 0xE8, 0xF0, 0xE0, 0x00, 0x06, 0x18, 0x1E, 0x3E, 0x3C, 0x3F,
  0x0E, 0x4C, 0x47, 0x0C, 0x8C, 0x8E, 0x1D, 0x0D, 0x0E, 0x1E, 0x1A, 0x0E,
  0x1C, 0x1E, 0x0C, 0x3C, 0x1C, 0x1C, 0x38, 0x38, 0x1C, 0x38, 0x38, 0x1C,
  0x30, 0x38, 0x18, 0x70, 0x30, 0x39, 0x70, 0x70, 0x32, 0x60, 0x70, 0x3C,
  0x60, 0x60, 0x38, 0x06, 0x0E, 0x1F, 0x1F, 0x83, 0x99, 0xC1, 0x98, 0xC1,
  0xD8, 0xE0, 0xE8, 0x70, 0x78, 0x30, 0x38, 0x38, 0x3C, 0x1C, 0x1C, 0x0E,
  0x0E, 0x06, 0x0E, 0x03, 0x17, 0x01, 0xB3, 0x80, 0xF1, 0x80, 0x70, 0x01,
  0xF0, 0x0E, 0x38, 0x38, 0x30, 0xE0, 0x73, 0x80, 0xEE, 0x01, 0xDC, 0x03,
  0xF8, 0x0F, 0xE0, 0x1D, 0xC0, 0x3B, 0x80, 0xE7, 0x03, 0x8E, 0x06, 0x0E,
  0x38, 0x07, 0xC0, 0x00, 0x00, 0xE7, 0xC0, 0x7C, 0xFE, 0x01, 0xD1, 0xF0,
  0x1E, 0x0F, 0x01, 0xC0, 0xF0, 0x38, 0x0F, 0x03, 0x80, 0xF0, 0x38, 0x0E,
  0x03, 0x01, 0xE0, 0x70, 0x1C, 0x07, 0x03, 0xC0, 0x60, 0x78, 0x06, 0x0F,
  0x00, 0xE1, 0xC0, 0x0F, 0xF0, 0x00, 0xC0, 0x00, 0x1C, 0x00, 0x01, 0xC0,
  0x00, 0x1C, 0x00, 0x01, 0x80, 0x00, 0x38, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0xF7, 0x03, 0xCE, 0x0F, 0x06, 0x1E, 0x06, 0x1C, 0x04, 0x3C, 0x04, 0x78,
  0x04, 0x78, 0x0C, 0xF0, 0x08, 0xF0, 0x18, 0xF0, 0x38, 0xF0, 0xF0, 0xF9,
  0x70, 0x7E, 0x70, 0x3C, 0x70, 0x00, 0x60, 0x00, 0xE0, 0x00, 0xE0, 0x00,
  0xC0, 0x01, 0xC0, 0x01, 0xC0, 0x0F, 0xF0, 0x7C, 0x70, 0xE7, 0xC7, 0x4C,
  0x34, 0x01, 0xA0, 0x1E, 0x00, 0xF0, 0x07, 0x00, 0x78, 0x03, 0x80, 0x1C,
  0x00, 0xC0, 0x0E, 0x00, 0x70, 0x03, 0x80, 0x00, 0x07, 0x88, 0x63, 0x86,
  0x0C, 0x30, 0x21, 0xC1, 0x0E, 0x00, 0x38, 0x00, 0xE0, 0x03, 0x80, 0x1C,
  0x10, 0x60, 0x83, 0x06, 0x18, 0x71, 0x82, 0x78, 0x00, 0x02, 0x03, 0x03,
  0x07, 0xF7, 0xF8, 0xE0, 0x60, 0x70, 0x38, 0x1C, 0x0C, 0x0E, 0x07, 0x03,
  0x01, 0x91, 0xC8, 0xF8, 0x78, 0x00, 0x1C, 0x0D, 0xF8, 0x38, 0x60, 0x70,
  0xC1, 0xC3, 0x83, 0x87, 0x07, 0x0C, 0x1E, 0x38, 0x78, 0x70, 0xB0, 0xE2,
  0x61, 0x8D, 0xC7, 0x33, 0x2C, 0xC6, 0x5F, 0x0F, 0x38, 0x1C, 0x00, 0x18,
  0x1B, 0xE0, 0x73, 0x81, 0xC6, 0x03, 0x18, 0x0C, 0x70, 0x21, 0xC1, 0x83,
  0x0C, 0x0C, 0x20, 0x31, 0x00, 0xC8, 0x03, 0x40, 0x0E, 0x00, 0x30, 0x00,
  0x80, 0x00, 0x18, 0x04, 0x1B, 0xE0, 0x30, 0x71, 0x80, 0xC1, 0xC6, 0x07,
  0x01, 0x1C, 0x2C, 0x08, 0x70, 0xB0, 0x20, 0xC4, 0xC1, 0x03, 0x21, 0x84,
  0x0D, 0x86, 0x20, 0x34, 0x19, 0x00, 0xE0, 0x68, 0x03, 0x81, 0xA0, 0x0C,
  0x07, 0x00, 0x30, 0x18, 0x00, 0x80, 0x40, 0x00, 0x03, 0x07, 0x0F, 0x8F,
  0x13, 0x93, 0x01, 0xB0, 0x01, 0xE0, 0x01, 0xC0, 0x00, 0xC0, 0x00, 0xC0,
  0x01, 0xC0, 0x03, 0xE0, 0x02, 0x60, 0x04, 0x62, 0x08, 0x64, 0xF0, 0x7C,
  0xE0, 0x30, 0x06, 0x06, 0x3F, 0x07, 0x07, 0x07, 0x07, 0x03, 0x03, 0x81,
  0x03, 0x82, 0x01, 0x82, 0x01, 0xC4, 0x01, 0xC4, 0x01, 0xC8, 0x00, 0xC8,
  0x00, 0xD0, 0x00, 0xF0, 0x00, 0xE0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0x80,
  0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x78, 0x00, 0x70, 0x00, 0x1F, 0xFC,
  0x7F, 0xE1, 0x01, 0x08, 0x08, 0x00, 0x40, 0x02, 0x00, 0x10, 0x00, 0x80,
  0x06, 0x00, 0x10, 0x00, 0x80, 0x04, 0x00, 0x38, 0x01, 0xF0, 0x0B, 0xE0,
  0x01, 0xC6, 0x03, 0x98, 0x03, 0x80, 0x00, 0x70, 0x0C, 0x01, 0x80, 0x38,
  0x03, 0x80, 0x30, 0x07, 0x00, 0x70, 0x07, 0x00, 0x60, 0x0E, 0x00, 0xE0,
  0x0C, 0x01, 0xC0, 0x1C, 0x07, 0x80, 0x30, 0x04, 0x00, 0x20, 0x03, 0x00,
  0x30, 0x07, 0x00, 0x70, 0x06, 0x00, 0x60, 0x0E, 0x00, 0xE0, 0x0C, 0x00,
  0xC0, 0x07, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0xC0, 0x06,
  0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x70, 0x07, 0x00, 0x70, 0x06,
  0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0C, 0x00, 0x40, 0x04, 0x00, 0xC0, 0x1E,
  0x03, 0x80, 0x38, 0x03, 0x00, 0x70, 0x07, 0x00, 0x70, 0x06, 0x00, 0xE0,
  0x0E, 0x00, 0xC0, 0x1C, 0x01, 0x80, 0x70, 0x00, 0x1E, 0x00, 0x3F, 0xE1,
  0xF8, 0x7F, 0xC0, 0x07, 0x80 };

const GFXglyph FreeSerifItalic18pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,   9,    0,    1 },   // 0x20 ' '
  {     0,  10,  23,  12,    1,  -22 },   // 0x21 '!'
  {    29,  12,   9,  12,    4,  -22 },   // 0x22 '"'
  {    43,  19,  23,  17,    0,  -22 },   // 0x23 '#'
  {    98,  15,  29,  17,    1,  -25 },   // 0x24 '$'
  {   153,  25,  23,  29,    3,  -22 },   // 0x25 '%'
  {   225,  22,  23,  27,    3,  -22 },   // 0x26 '&'
  {   289,   5,   9,   7,    4,  -22 },   // 0x27 '''
  {   295,   9,  29,  12,    1,  -22 },   // 0x28 '('
  {   328,   9,  29,  12,    1,  -22 },   // 0x29 ')'
  {   361,  12,  14,  18,    5,  -22 },   // 0x2A '*'
  {   382,  16,  18,  24,    4,  -17 },   // 0x2B '+'
  {   418,   5,   8,   9,   -1,   -2 },   // 0x2C ','
  {   423,   8,   2,  12,    2,   -8 },   // 0x2D '-'
  {   425,   4,   4,   9,    1,   -3 },   // 0x2E '.'
  {   427,  16,  23,  10,    0,  -22 },   // 0x2F '/'
  {   473,  17,  24,  17,    1,  -23 },   // 0x30 '0'
  {   524,  12,  24,  17,    2,  -23 },   // 0x31 '1'
  {   560,  16,  23,  17,    1,  -22 },   // 0x32 '2'
  {   606,  17,  24,  18,    0,  -23 },   // 0x33 '3'
  {   657,  17,  24,  17,    0,  -23 },   // 0x34 '4'
  {   708,  16,  23,  18,    0,  -22 },   // 0x35 '5'
  {   754,  17,  24,  18,    1,  -23 },   // 0x36 '6'
  {   805,  16,  23,  17,    3,  -22 },   // 0x37 '7'
  {   851,  16,  24,  18,    1,  -23 },   // 0x38 '8'
  {   899,  16,  24,  17,    1,  -23 },   // 0x39 '9'
  {   947,   7,  15,   9,    2,  -14 },   // 0x3A ':'
  {   961,   9,  20,   9,    1,  -14 },   // 0x3B ';'
  {   984,  18,  18,  20,    2,  -17 },   // 0x3C '<'
  {  1025,  18,   9,  23,    3,  -12 },   // 0x3D '='
  {  1046,  18,  18,  20,    2,  -17 },   // 0x3E '>'
  {  1087,  12,  23,  16,    4,  -22 },   // 0x3F '?'
  {  1122,  24,  23,  27,    2,  -22 },   // 0x40 '@'
  {  1191,  21,  23,  23,    0,  -22 },   // 0x41 'A'
  {  1252,  21,  23,  21,    0,  -22 },   // 0x42 'B'
  {  1313,  21,  23,  21,    2,  -22 },   // 0x43 'C'
  {  1374,  25,  23,  25,    0,  -22 },   // 0x44 'D'
  {  1446,  22,  23,  20,    0,  -22 },   // 0x45 'E'
  {  1510,  22,  23,  20,    0,  -22 },   // 0x46 'F'
  {  1574,  23,  23,  24,    2,  -22 },   // 0x47 'G'
  {  1641,  27,  23,  25,    0,  -22 },   // 0x48 'H'
  {  1719,  14,  23,  11,    0,  -22 },   // 0x49 'I'
  {  1760,  17,  23,  15,    0,  -22 },   // 0x4A 'J'
  {  1809,  25,  23,  22,    0,  -22 },   // 0x4B 'K'
  {  1881,  20,  23,  20,    0,  -22 },   // 0x4C 'L'
  {  1939,  31,  23,  29,    0,  -22 },   // 0x4D 'M'
  {  2029,  26,  23,  24,    0,  -22 },   // 0x4E 'N'
  {  2104,  23,  23,  23,    1,  -22 },   // 0x4F 'O'
  {  2171,  22,  23,  20,    0,  -22 },   // 0x50 'P'
  {  2235,  23,  29,  23,    1,  -22 },   // 0x51 'Q'
  {  2319,  21,  23,  22,    0,  -22 },   // 0x52 'R'
  {  2380,  17,  23,  16,    0,  -22 },   // 0x53 'S'
  {  2429,  20,  23,  21,    3,  -22 },   // 0x54 'T'
  {  2487,  23,  23,  25,    4,  -22 },   // 0x55 'U'
  {  2554,  21,  23,  23,    5,  -22 },   // 0x56 'V'
  {  2615,  29,  23,  31,    5,  -22 },   // 0x57 'W'
  {  2699,  24,  23,  23,    0,  -22 },   // 0x58 'X'
  {  2768,  19,  23,  21,    4,  -22 },   // 0x59 'Y'
  {  2823,  22,  23,  20,    0,  -22 },   // 0x5A 'Z'
  {  2887,  13,  28,  14,    1,  -22 },   // 0x5B '['
  {  2933,  12,  23,  17,    4,  -22 },   // 0x5C '\'
  {  2968,  12,  28,  14,    1,  -22 },   // 0x5D ']'
  {  3010,  15,  13,  15,    0,  -22 },   // 0x5E '^'
  {  3035,  18,   2,  17,    0,    3 },   // 0x5F '_'
  {  3040,   6,   6,   9,    5,  -22 },   // 0x60 '`'
  {  3045,  15,  15,  17,    1,  -14 },   // 0x61 'a'
  {  3074,  16,  24,  17,    1,  -23 },   // 0x62 'b'
  {  3122,  13,  15,  14,    1,  -14 },   // 0x63 'c'
  {  3147,  17,  24,  18,    1,  -23 },   // 0x64 'd'
  {  3198,  13,  15,  14,    1,  -14 },   // 0x65 'e'
  {  3223,  20,  31,  15,   -3,  -23 },   // 0x66 'f'
  {  3301,  16,  22,  15,   -1,  -14 },   // 0x67 'g'
  {  3345,  16,  24,  17,    1,  -23 },   // 0x68 'h'
  {  3393,   9,  23,   9,    1,  -22 },   // 0x69 'i'
  {  3419,  15,  30,  10,   -3,  -22 },   // 0x6A 'j'
  {  3476,  15,  24,  16,    1,  -23 },   // 0x6B 'k'
  {  3521,   8,  25,   9,    1,  -23 },   // 0x6C 'l'
  {  3546,  24,  15,  25,    0,  -14 },   // 0x6D 'm'
  {  3591,  17,  15,  17,    0,  -14 },   // 0x6E 'n'
  {  3623,  15,  15,  17,    1,  -14 },   // 0x6F 'o'
  {  3652,  20,  22,  16,   -3,  -14 },   // 0x70 'p'
  {  3707,  16,  22,  17,    1,  -14 },   // 0x71 'q'
  {  3751,  13,  15,  13,    1,  -14 },   // 0x72 'r'
  {  3776,  13,  15,  12,    0,  -14 },   // 0x73 's'
  {  3801,   9,  18,   8,    1,  -17 },   // 0x74 't'
  {  3822,  15,  15,  17,    1,  -14 },   // 0x75 'u'
  {  3851,  14,  15,  16,    2,  -14 },   // 0x76 'v'
  {  3878,  22,  15,  24,    1,  -14 },   // 0x77 'w'
  {  3920,  16,  15,  15,   -1,  -14 },   // 0x78 'x'
  {  3950,  16,  22,  16,    0,  -14 },   // 0x79 'y'
  {  3994,  14,  18,  14,    0,  -14 },   // 0x7A 'z'
  {  4026,  12,  30,  14,    2,  -23 },   // 0x7B '{'
  {  4071,   2,  23,  10,    4,  -22 },   // 0x7C '|'
  {  4077,  12,  31,  14,    0,  -24 },   // 0x7D '}'
  {  4124,  17,   4,  19,    1,  -10 } }; // 0x7E '~'

const GFXfont FreeSerifItalic18pt7b PROGMEM = {
  (uint8_t  *)FreeSerifItalic18pt7bBitmaps,
  (GFXglyph *)FreeSerifItalic18pt7bGlyphs,
  0x20, 0x7E, 42 };

// Approx. 4805 bytes
