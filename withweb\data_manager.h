/*
 * Data Manager Header - Minimal Version
 */

#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include <Arduino.h>
#include <SD.h>
#include <SPIFFS.h>
#include <FS.h>
#include <ArduinoJson.h>
#include <vector>
#include "config.h"

class DataManager {
private:
  bool sdInitialized;
  bool spiffsInitialized;
  
public:
  DataManager();
  ~DataManager();
  
  bool begin();
  void reset();
  bool isSDAvailable() const { return sdInitialized; }
  bool isSPIFFSAvailable() const { return spiffsInitialized; }
  
  bool writeFile(String path, String content);
  bool writeFile(String path, const uint8_t* data, size_t length);
  String readFile(String path);
  bool deleteFile(String path);
  bool fileExists(String path);
  size_t getFileSize(String path);
  
  bool writeJSON(String path, JsonDocument& doc);
  bool readJSON(String path, JsonDocument& doc);
  
  bool appendCSV(String path, String row);
  
  bool logAttendance(const AttendanceRecord& record);
  
  bool saveConfig(const SystemConfig& config);
  bool loadConfig(SystemConfig& config);
  
  uint64_t getTotalSpace();
  uint64_t getUsedSpace();
  uint64_t getFreeSpace();
  
  String formatBytes(uint64_t bytes);
  String getCurrentDateString();
  String getCurrentTimeString();
};

#endif // DATA_MANAGER_H
