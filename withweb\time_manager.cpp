/*
 * Time Manager Implementation - Minimal Version
 */

#include "time_manager.h"

TimeManager::TimeManager() : 
  timeClient(nullptr),
  ntpServer(DEFAULT_NTP_SERVER),
  timezone(DEFAULT_TIMEZONE),
  timezoneOffset(0),
  ntpEnabled(false),
  timeSet(false),
  lastNTPUpdate(0) {
}

TimeManager::~TimeManager() {
  if (timeClient) {
    delete timeClient;
  }
}

bool TimeManager::begin() {
  return begin(DEFAULT_NTP_SERVER, DEFAULT_TIMEZONE);
}

bool TimeManager::begin(String ntpServerAddr, String timezoneStr) {
  Serial.println("Initializing time manager...");
  
  ntpServer = ntpServerAddr;
  timezone = timezoneStr;
  
  // Calculate timezone offset (simplified)
  if (timezoneStr.indexOf("EST") >= 0 || timezoneStr.indexOf("America/New_York") >= 0) {
    timezoneOffset = -5 * 3600;
  } else if (timezoneStr.indexOf("PST") >= 0 || timezoneStr.indexOf("America/Los_Angeles") >= 0) {
    timezoneOffset = -8 * 3600;
  } else {
    timezoneOffset = 0; // UTC
  }
  
  // Initialize NTP client if WiFi is available
  if (WiFi.status() == WL_CONNECTED) {
    if (enableNTP()) {
      updateFromNTP();
    }
  } else {
    Serial.println("WiFi not connected - NTP disabled");
  }
  
  Serial.println("Time manager initialized");
  return true;
}

void TimeManager::reset() {
  if (timeClient) {
    delete timeClient;
    timeClient = nullptr;
  }
  
  ntpEnabled = false;
  timeSet = false;
  lastNTPUpdate = 0;
}

bool TimeManager::enableNTP() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("Cannot enable NTP - WiFi not connected");
    return false;
  }
  
  if (timeClient) {
    delete timeClient;
  }
  
  timeClient = new NTPClient(ntpUDP, ntpServer.c_str(), timezoneOffset, NTP_UPDATE_INTERVAL);
  timeClient->begin();
  
  ntpEnabled = true;
  Serial.println("NTP enabled with server: " + ntpServer);
  
  return true;
}

void TimeManager::disableNTP() {
  if (timeClient) {
    timeClient->end();
    delete timeClient;
    timeClient = nullptr;
  }
  
  ntpEnabled = false;
  Serial.println("NTP disabled");
}

bool TimeManager::updateFromNTP() {
  if (!ntpEnabled || !timeClient) {
    return false;
  }
  
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("Cannot update NTP - WiFi not connected");
    return false;
  }
  
  Serial.println("Updating time from NTP...");
  
  if (timeClient->update()) {
    unsigned long epochTime = timeClient->getEpochTime();
    
    struct timeval tv;
    tv.tv_sec = epochTime;
    tv.tv_usec = 0;
    settimeofday(&tv, NULL);
    
    lastNTPUpdate = millis();
    timeSet = true;
    
    Serial.println("NTP update successful");
    Serial.println("Current time: " + getCurrentDateTime());
    
    return true;
  } else {
    Serial.println("NTP update failed");
    return false;
  }
}

String TimeManager::getCurrentTime() {
  if (!timeSet) {
    return "--:--:--";
  }
  
  time_t now;
  time(&now);
  
  struct tm* timeinfo = localtime(&now);
  
  char buffer[16];
  strftime(buffer, sizeof(buffer), "%H:%M:%S", timeinfo);
  
  return String(buffer);
}

String TimeManager::getCurrentDate() {
  if (!timeSet) {
    return "----/--/--";
  }
  
  time_t now;
  time(&now);
  
  struct tm* timeinfo = localtime(&now);
  
  char buffer[16];
  strftime(buffer, sizeof(buffer), "%Y/%m/%d", timeinfo);
  
  return String(buffer);
}

String TimeManager::getCurrentDateTime() {
  if (!timeSet) {
    return "----/--/-- --:--:--";
  }
  
  return getCurrentDate() + " " + getCurrentTime();
}

String TimeManager::getCurrentTimestamp() {
  if (!timeSet) {
    return "1970-01-01T00:00:00Z";
  }
  
  time_t now;
  time(&now);
  
  struct tm* timeinfo = gmtime(&now); // UTC time for ISO 8601
  
  char buffer[32];
  strftime(buffer, sizeof(buffer), "%Y-%m-%dT%H:%M:%SZ", timeinfo);
  
  return String(buffer);
}

time_t TimeManager::getCurrentEpoch() {
  time_t now;
  time(&now);
  return now;
}

void TimeManager::setTimezone(String tz) {
  timezone = tz;
  
  // Simplified timezone offset calculation
  if (tz.indexOf("EST") >= 0 || tz.indexOf("America/New_York") >= 0) {
    timezoneOffset = -5 * 3600;
  } else if (tz.indexOf("PST") >= 0 || tz.indexOf("America/Los_Angeles") >= 0) {
    timezoneOffset = -8 * 3600;
  } else {
    timezoneOffset = 0; // UTC
  }
  
  if (timeClient) {
    timeClient->setTimeOffset(timezoneOffset);
  }
  
  Serial.println("Timezone set to: " + tz);
}

void TimeManager::setNTPServer(String server) {
  ntpServer = server;
  
  if (ntpEnabled) {
    disableNTP();
    enableNTP();
  }
  
  Serial.println("NTP server set to: " + server);
}

bool TimeManager::setTime(int hour, int minute, int second) {
  if (hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59) {
    return false;
  }
  
  time_t now;
  time(&now);
  
  struct tm* timeinfo = localtime(&now);
  timeinfo->tm_hour = hour;
  timeinfo->tm_min = minute;
  timeinfo->tm_sec = second;
  
  time_t newTime = mktime(timeinfo);
  
  struct timeval tv;
  tv.tv_sec = newTime;
  tv.tv_usec = 0;
  settimeofday(&tv, NULL);
  
  timeSet = true;
  Serial.println("Time manually set to: " + getCurrentTime());
  
  return true;
}

bool TimeManager::setDate(int year, int month, int day) {
  if (year < 2020 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31) {
    return false;
  }
  
  time_t now;
  time(&now);
  
  struct tm* timeinfo = localtime(&now);
  timeinfo->tm_year = year - 1900; // tm_year is years since 1900
  timeinfo->tm_mon = month - 1;    // tm_mon is 0-based
  timeinfo->tm_mday = day;
  
  time_t newTime = mktime(timeinfo);
  
  struct timeval tv;
  tv.tv_sec = newTime;
  tv.tv_usec = 0;
  settimeofday(&tv, NULL);
  
  timeSet = true;
  Serial.println("Date manually set to: " + getCurrentDate());
  
  return true;
}

bool TimeManager::setDateTime(int year, int month, int day, int hour, int minute, int second) {
  if (!setDate(year, month, day)) {
    return false;
  }
  
  return setTime(hour, minute, second);
}

bool TimeManager::isTimeValid() {
  if (!timeSet) return false;
  
  time_t now;
  time(&now);
  
  // Check if time is reasonable (after 2020 and before 2100)
  return (now > 1577836800 && now < 4102444800); // 2020-01-01 to 2100-01-01
}

bool TimeManager::needsNTPUpdate() {
  if (!ntpEnabled) return false;
  
  unsigned long currentTime = millis();
  return (currentTime - lastNTPUpdate > NTP_UPDATE_INTERVAL);
}

void TimeManager::update() {
  // Update time client if available
  if (timeClient && ntpEnabled) {
    timeClient->update();
  }
  
  // Check if NTP update is needed
  if (needsNTPUpdate()) {
    updateFromNTP();
  }
}

void TimeManager::forceNTPUpdate() {
  if (ntpEnabled) {
    updateFromNTP();
  }
}

unsigned long TimeManager::getUptime() {
  return millis() / 1000;
}

String TimeManager::getUptimeString() {
  unsigned long uptime = getUptime();
  
  unsigned long days = uptime / 86400;
  uptime %= 86400;
  unsigned long hours = uptime / 3600;
  uptime %= 3600;
  unsigned long minutes = uptime / 60;
  unsigned long seconds = uptime % 60;
  
  String result = "";
  if (days > 0) result += String(days) + "d ";
  if (hours > 0) result += String(hours) + "h ";
  if (minutes > 0) result += String(minutes) + "m ";
  result += String(seconds) + "s";
  
  return result;
}

void TimeManager::printTimeInfo() {
  Serial.println("=== Time Manager Status ===");
  Serial.println("Time Set: " + String(timeSet ? "Yes" : "No"));
  Serial.println("NTP Enabled: " + String(ntpEnabled ? "Yes" : "No"));
  Serial.println("NTP Server: " + ntpServer);
  Serial.println("Timezone: " + timezone);
  Serial.println("Current Time: " + getCurrentDateTime());
  Serial.println("Uptime: " + getUptimeString());
  Serial.println("===========================");
}
