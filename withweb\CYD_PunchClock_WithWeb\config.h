/*
 * Configuration file for RFID Punch Clock System
 * ESP32-2432S028R (CYD) + MFRC522
 * 
 * Pin definitions, constants, and system configuration
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// ===== HARDWARE PIN DEFINITIONS =====

// TFT Display (ILI9341) - ESP32-2432S028R specific
#define TFT_MISO    19
#define TFT_MOSI    23
#define TFT_SCLK    18
#define TFT_CS      15
#define TFT_DC      2
#define TFT_RST     -1  // Not connected
#define TFT_BL      21  // Backlight control

// Touch Controller (XPT2046)
#define TOUCH_MISO  39
#define TOUCH_MOSI  32
#define TOUCH_SCLK  25
#define TOUCH_CS    33
#define TOUCH_IRQ   36

// SD Card (shares SPI with TFT)
#define SD_MISO     19  // Shared with TFT
#define SD_MOSI     23  // Shared with TFT
#define SD_SCLK     18  // Shared with TFT
#define SD_CS       5

// Built-in Components
#define RGB_LED_PIN 4
#define SPEAKER_PIN 26
#define LDR_PIN     34

// MFRC522 RFID Module (shares SPI with TFT/SD)
#define RFID_SDA    22  // Chip Select
#define RFID_RST    16  // Reset
#define RFID_MISO   19  // Shared with TFT/SD
#define RFID_MOSI   23  // Shared with TFT/SD
#define RFID_SCK    18  // Shared with TFT/SD

// ===== DISPLAY CONFIGURATION =====

// Screen dimensions
#define SCREEN_WIDTH  240
#define SCREEN_HEIGHT 320

// Screen layout areas
#define HEADER_HEIGHT     40
#define TIME_HEIGHT       80
#define STATUS_HEIGHT     100
#define ACTIVITY_HEIGHT   60
#define BUTTON_HEIGHT     40

// Colors (RGB565 format)
#define COLOR_BACKGROUND  0x0000  // Black
#define COLOR_HEADER      0x001F  // Blue
#define COLOR_TEXT        0xFFFF  // White
#define COLOR_SUCCESS     0x07E0  // Green
#define COLOR_ERROR       0xF800  // Red
#define COLOR_WARNING     0xFFE0  // Yellow
#define COLOR_INFO        0x07FF  // Cyan
#define COLOR_BUTTON      0x4208  // Dark Gray
#define COLOR_BUTTON_PRESSED 0x8410  // Light Gray

// Font sizes
#define FONT_SMALL        2
#define FONT_MEDIUM       4
#define FONT_LARGE        6

// Touch settings
#define TOUCH_THRESHOLD   600
#define TOUCH_DEBOUNCE    200  // milliseconds

// ===== RFID CONFIGURATION =====

// RFID timing
#define RFID_SCAN_INTERVAL    100   // milliseconds
#define RFID_DEBOUNCE_TIME    2000  // milliseconds
#define RFID_TIMEOUT          5000  // milliseconds

// ===== AUDIO CONFIGURATION =====

// Sound frequencies (Hz)
#define SOUND_SUCCESS     1000
#define SOUND_ERROR       500
#define SOUND_PUNCH_IN    800
#define SOUND_PUNCH_OUT   600
#define SOUND_CLICK       1200
#define SOUND_STARTUP     1500

// Sound durations (ms)
#define SOUND_DURATION_SHORT  200
#define SOUND_DURATION_MEDIUM 300
#define SOUND_DURATION_LONG   500

// ===== LED CONFIGURATION =====

// LED status colors
enum LEDStatus {
  LED_OFF,
  LED_READY,
  LED_SCANNING,
  LED_SUCCESS,
  LED_ERROR,
  LED_ADMIN,
  LED_PROCESSING
};

// LED brightness (0-255)
#define LED_BRIGHTNESS_LOW    50
#define LED_BRIGHTNESS_MEDIUM 128
#define LED_BRIGHTNESS_HIGH   255

// ===== SYSTEM CONFIGURATION =====

// Timing constants
#define SYSTEM_UPDATE_INTERVAL    50    // milliseconds
#define TIME_UPDATE_INTERVAL      1000  // milliseconds
#define BRIGHTNESS_UPDATE_INTERVAL 5000 // milliseconds
#define ACTIVITY_TIMEOUT          30000 // milliseconds (30 seconds)
#define SLEEP_TIMEOUT             300000 // milliseconds (5 minutes)

// Memory management
#define JSON_BUFFER_SIZE          2048
#define MAX_EMPLOYEES             500
#define MAX_ATTENDANCE_RECORDS    10000

// File paths
#define CONFIG_FILE               "/config.json"
#define EMPLOYEES_FILE            "/employees.json"
#define ATTENDANCE_FILE           "/attendance.csv"
#define CALIBRATION_FILE          "/calibration.dat"
#define LOG_FILE                  "/system.log"

// Network configuration
#define WIFI_CONNECT_TIMEOUT      20000  // milliseconds
#define WIFI_RETRY_INTERVAL       30000  // milliseconds
#define NTP_UPDATE_INTERVAL       3600000 // milliseconds (1 hour)
#define WEB_SERVER_PORT           80

// Default settings
#define DEFAULT_ADMIN_PIN         "1234"
#define DEFAULT_TIMEZONE          "America/New_York"
#define DEFAULT_NTP_SERVER        "pool.ntp.org"
#define DEFAULT_DEVICE_NAME       "PunchClock-001"

// ===== DATA STRUCTURES =====

// Touch point structure
struct TouchPoint {
  uint16_t x;
  uint16_t y;
  bool pressed;
  unsigned long timestamp;
};

// Employee structure
struct Employee {
  int id;
  String name;
  String rfidUID;
  String department;
  String shiftStart;
  String shiftEnd;
  bool active;
  String lastAction;
  String lastTimestamp;
  String pin;
  int accessLevel;
};

// Attendance record structure
struct AttendanceRecord {
  int employeeId;
  String timestamp;
  String action;
  String location;
  bool synced;
};

// System configuration structure
struct SystemConfig {
  String deviceName;
  String wifiSSID;
  String wifiPassword;
  String ntpServer;
  String timezone;
  String adminPin;
  bool autoBrightness;
  bool soundEnabled;
  int displayTimeout;
  int sleepTimeout;
  bool webServerEnabled;
  int webServerPort;
};

// ===== UTILITY MACROS =====

// Debug printing
#ifdef DEBUG
  #define DEBUG_PRINT(x)    Serial.print(x)
  #define DEBUG_PRINTLN(x)  Serial.println(x)
  #define DEBUG_PRINTF(x, y) Serial.printf(x, y)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(x, y)
#endif

// Memory monitoring
#define PRINT_FREE_HEAP() Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap())

// Error handling
#define CHECK_INIT(component, message) \
  if (!component) { \
    Serial.println("Failed to initialize: " message); \
    return false; \
  }

// Time utilities
#define SECONDS_TO_MS(s) ((s) * 1000)
#define MINUTES_TO_MS(m) ((m) * 60 * 1000)
#define HOURS_TO_MS(h)   ((h) * 60 * 60 * 1000)

// ===== VERSION INFORMATION =====

#define FIRMWARE_VERSION      "1.0.1"
#define HARDWARE_VERSION      "ESP32-2432S028R"
#define BUILD_DATE            __DATE__
#define BUILD_TIME            __TIME__

// ===== FEATURE FLAGS =====

// Enable/disable features for testing
#define FEATURE_WIFI_ENABLED      true
#define FEATURE_WEB_SERVER        true
#define FEATURE_NTP_SYNC          true
#define FEATURE_SD_CARD           true
#define FEATURE_AUDIO             true
#define FEATURE_LED_STATUS        true
#define FEATURE_AUTO_BRIGHTNESS   true
#define FEATURE_SLEEP_MODE        true
#define FEATURE_BACKUP            true

// Development flags
#define ENABLE_SERIAL_DEBUG       true
#define ENABLE_MEMORY_MONITORING  false
#define ENABLE_PERFORMANCE_TIMING false

// ===== VALIDATION MACROS =====

// Input validation
#define IS_VALID_PIN(pin) ((pin).length() == 4 && (pin).toInt() > 0)
#define IS_VALID_RFID(uid) ((uid).length() >= 8 && (uid).indexOf(':') > 0)
#define IS_VALID_NAME(name) ((name).length() > 0 && (name).length() <= 50)

// Range validation
#define CLAMP(value, min, max) ((value) < (min) ? (min) : ((value) > (max) ? (max) : (value)))
#define IN_RANGE(value, min, max) ((value) >= (min) && (value) <= (max))

// ===== SYSTEM LIMITS =====

#define MAX_WIFI_SSID_LENGTH      32
#define MAX_WIFI_PASSWORD_LENGTH  64
#define MAX_EMPLOYEE_NAME_LENGTH  50
#define MAX_DEPARTMENT_LENGTH     30
#define MAX_PIN_LENGTH            4
#define MAX_RFID_UID_LENGTH       20
#define MAX_FILENAME_LENGTH       32
#define MAX_LOG_ENTRIES           1000

#endif // CONFIG_H
