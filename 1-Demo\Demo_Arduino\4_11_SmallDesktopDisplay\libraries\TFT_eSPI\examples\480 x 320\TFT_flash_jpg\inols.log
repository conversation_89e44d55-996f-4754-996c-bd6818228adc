






Started logging.

<<<
Content-Length: 3207

{"jsonrpc":"2.0","id":0,"method":"initialize","params":{"processId":828,"clientInfo":{"name":"vscode","version":"1.50.0"},"rootPath":"c:\\Users\\<USER>\\Documents\\Arduino\\libraries\\TFT_eSPI\\examples\\480 x 320\\TFT_flash_jpg","rootUri":"file:///c%3A/Users/<USER>/Documents/Arduino/libraries/TFT_eSPI/examples/480%20x%20320/TFT_flash_jpg","capabilities":{"workspace":{"applyEdit":true,"workspaceEdit":{"documentChanges":true,"resourceOperations":["create","rename","delete"],"failureHandling":"textOnlyTransactional"},"didChangeConfiguration":{"dynamicRegistration":true},"didChangeWatchedFiles":{"dynamicRegistration":true},"symbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]}},"executeCommand":{"dynamicRegistration":true},"configuration":true,"workspaceFolders":true},"textDocument":{"publishDiagnostics":{"relatedInformation":true,"versionSupport":false,"tagSupport":{"valueSet":[1,2]}},"synchronization":{"dynamicRegistration":true,"willSave":true,"willSaveWaitUntil":true,"didSave":true},"completion":{"dynamicRegistration":true,"contextSupport":true,"completionItem":{"snippetSupport":true,"commitCharactersSupport":true,"documentationFormat":["markdown","plaintext"],"deprecatedSupport":true,"preselectSupport":true,"tagSupport":{"valueSet":[1]}},"completionItemKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]}},"hover":{"dynamicRegistration":true,"contentFormat":["markdown","plaintext"]},"signatureHelp":{"dynamicRegistration":true,"signatureInformation":{"documentationFormat":["markdown","plaintext"],"parameterInformation":{"labelOffsetSupport":true}},"contextSupport":true},"definition":{"dynamicRegistration":true,"linkSupport":true},"references":{"dynamicRegistration":true},"documentHighlight":{"dynamicRegistration":true},"documentSymbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"hierarchicalDocumentSymbolSupport":true},"codeAction":{"dynamicRegistration":true,"isPreferredSupport":true,"codeActionLiteralSupport":{"codeActionKind":{"valueSet":["","quickfix","refactor","refactor.extract","refactor.inline","refactor.rewrite","source","source.organizeImports"]}}},"codeLens":{"dynamicRegistration":true},"formatting":{"dynamicRegistration":true},"rangeFormatting":{"dynamicRegistration":true},"onTypeFormatting":{"dynamicRegistration":true},"rename":{"dynamicRegistration":true,"prepareSupport":true},"documentLink":{"dynamicRegistration":true,"tooltipSupport":true},"typeDefinition":{"dynamicRegistration":true,"linkSupport":true},"implementation":{"dynamicRegistration":true,"linkSupport":true},"colorProvider":{"dynamicRegistration":true},"foldingRange":{"dynamicRegistration":true,"rangeLimit":5000,"lineFoldingOnly":true},"declaration":{"dynamicRegistration":true,"linkSupport":true},"selectionRange":{"dynamicRegistration":true}},"window":{"workDoneProgress":true}},"initializationOptions":{},"trace":"off","workspaceFolders":[{"uri":"file:///c%3A/Users/<USER>/Documents/Arduino/libraries/TFT_eSPI/examples/480%20x%20320/TFT_flash_jpg","name":"TFT_flash_jpg"}]}}
>>>
Content-Length: 598

{"id":0,"result":{"capabilities":{"textDocumentSync":2,"hoverProvider":true,"completionProvider":{"triggerCharacters":[".","\u003e",":"]},"signatureHelpProvider":{"triggerCharacters":["(",","]},"definitionProvider":true,"documentHighlightProvider":true,"documentSymbolProvider":true,"workspaceSymbolProvider":true,"codeActionProvider":true,"documentFormattingProvider":true,"documentRangeFormattingProvider":true,"documentOnTypeFormattingProvider":{"firstTriggerCharacter":"\n"},"renameProvider":false,"executeCommandProvider":{"commands":["clangd.applyFix","clangd.applyTweak"]}}},"jsonrpc":"2.0"}
<<<
Content-Length: 52

{"jsonrpc":"2.0","method":"initialized","params":{}}Content-Length: 9489

{"jsonrpc":"2.0","method":"textDocument/didOpen","params":{"textDocument":{"uri":"file:///c%3A/Users/<USER>/Documents/Arduino/libraries/TFT_eSPI/examples/480%20x%20320/TFT_flash_jpg/TFT_flash_jpg.ino","languageId":"cpp","version":1,"text":"// Sketch to display images on a 480 x 320 ILI9486 Raspberry Pi 3.5\" TFT (Waveshare design)\n// which has a 16 bit serial interface based on 74HC04, 74HC4040 and 2 x 74HC4094 logic chips\n\n// Renders images stored in an array in program (FLASH)JPEG images are stored in header files\n// (see jpeg1.h etc)\n\n// The sketch does not need the SD or sdFat libraries since it does not access an SD Card.\n\n// As well as the TFT_eSPI library:\n// https://github.com/Bodmer/TFT_eSPI\n// the sketch need the JPEG Decoder library. This can be loaded via the Library Manager.\n// or can be downloaded here:\n// https://github.com/Bodmer/JPEGDecoder\n\n//----------------------------------------------------------------------------------------------------\n\n#include <SPI.h>\n#include <TFT_eSPI.h>\n\nTFT_eSPI tft = TFT_eSPI();\n\n\n// JPEG decoder library\n#include <JPEGDecoder.h>\n\n// Return the minimum of two values a and b\n#define minimum(a,b)     (((a) < (b)) ? (a) : (b))\n\n// Include the sketch header file that contains the image stored as an array of bytes\n// More than one image array could be stored in each header file.\n#include \"jpeg1.h\"\n#include \"jpeg2.h\"\n#include \"jpeg3.h\"\n#include \"jpeg4.h\"\n\n// Count how many times the image is drawn for test purposes\nuint32_t icount = 0;\n//----------------------------------------------------------------------------------------------------\n\n\n//####################################################################################################\n// Setup\n//####################################################################################################\nvoid setup() {\n  Serial.begin(115200);\n  tft.begin();\n}\n\n//####################################################################################################\n// Main loop\n//####################################################################################################\nvoid loop() {\n\n  tft.setRotation(2);  // portrait\n  tft.fillScreen(random(0xFFFF));\n\n  // The image is 300 x 300 pixels so we do some sums to position image in the middle of the screen!\n  // Doing this by reading the image width and height from the jpeg info is left as an exercise!\n  int x = (tft.width()  - 300) / 2 - 1;\n  int y = (tft.height() - 300) / 2 - 1;\n\n  drawArrayJpeg(EagleEye, sizeof(EagleEye), x, y); // Draw a jpeg image stored in memory at x,y\n  delay(2000);\n\n\n  tft.setRotation(2);  // portrait\n  tft.fillScreen(random(0xFFFF));\n  drawArrayJpeg(Baboon40, sizeof(Baboon40), 0, 0); // Draw a jpeg image stored in memory\n  delay(2000);\n\n\n  tft.setRotation(2);  // portrait\n  tft.fillScreen(random(0xFFFF));\n  drawArrayJpeg(lena20k, sizeof(lena20k), 0, 0); // Draw a jpeg image stored in memory\n  delay(2000);\n\n  tft.setRotation(1);  // landscape\n  tft.fillScreen(random(0xFFFF));\n\n  // This image will be deliberately cropped as it is 480 x 320 thes extends off the screen when plotted\n  // at coordinate 100,100\n  drawArrayJpeg(Mouse480, sizeof(Mouse480), 100, 100); // Draw a jpeg image stored in memory, test cropping\n  //drawArrayJpeg(Mouse480, sizeof(Mouse480), 0, 0); // Draw a jpeg image stored in memory\n  delay(2000);\n}\n\n//####################################################################################################\n// Draw a JPEG on the TFT pulled from a program memory array\n//####################################################################################################\nvoid drawArrayJpeg(const uint8_t arrayname[], uint32_t array_size, int xpos, int ypos) {\n\n  int x = xpos;\n  int y = ypos;\n\n  JpegDec.decodeArray(arrayname, array_size);\n  \n  jpegInfo(); // Print information from the JPEG file (could comment this line out)\n  \n  renderJPEG(x, y);\n  \n  Serial.println(\"#########################\");\n}\n\n//####################################################################################################\n// Draw a JPEG on the TFT, images will be cropped on the right/bottom sides if they do not fit\n//####################################################################################################\n// This function assumes xpos,ypos is a valid screen coordinate. For convenience images that do not\n// fit totally on the screen are cropped to the nearest MCU size and may leave right/bottom borders.\nvoid renderJPEG(int xpos, int ypos) {\n\n  // retrieve infomration about the image\n  uint16_t *pImg;\n  uint16_t mcu_w = JpegDec.MCUWidth;\n  uint16_t mcu_h = JpegDec.MCUHeight;\n  uint32_t max_x = JpegDec.width;\n  uint32_t max_y = JpegDec.height;\n\n  // Jpeg images are draw as a set of image block (tiles) called Minimum Coding Units (MCUs)\n  // Typically these MCUs are 16x16 pixel blocks\n  // Determine the width and height of the right and bottom edge image blocks\n  uint32_t min_w = minimum(mcu_w, max_x % mcu_w);\n  uint32_t min_h = minimum(mcu_h, max_y % mcu_h);\n\n  // save the current image block size\n  uint32_t win_w = mcu_w;\n  uint32_t win_h = mcu_h;\n\n  // record the current time so we can measure how long it takes to draw an image\n  uint32_t drawTime = millis();\n\n  // save the coordinate of the right and bottom edges to assist image cropping\n  // to the screen size\n  max_x += xpos;\n  max_y += ypos;\n\n  // read each MCU block until there are no more\n  while (JpegDec.read()) {\n\t  \n    // save a pointer to the image block\n    pImg = JpegDec.pImage ;\n\n    // calculate where the image block should be drawn on the screen\n    int mcu_x = JpegDec.MCUx * mcu_w + xpos;  // Calculate coordinates of top left corner of current MCU\n    int mcu_y = JpegDec.MCUy * mcu_h + ypos;\n\n    // check if the image block size needs to be changed for the right edge\n    if (mcu_x + mcu_w <= max_x) win_w = mcu_w;\n    else win_w = min_w;\n\n    // check if the image block size needs to be changed for the bottom edge\n    if (mcu_y + mcu_h <= max_y) win_h = mcu_h;\n    else win_h = min_h;\n\n    // copy pixels into a contiguous block\n    if (win_w != mcu_w)\n    {\n      uint16_t *cImg;\n      int p = 0;\n      cImg = pImg + win_w;\n      for (int h = 1; h < win_h; h++)\n      {\n        p += mcu_w;\n        for (int w = 0; w < win_w; w++)\n        {\n          *cImg = *(pImg + w + p);\n          cImg++;\n        }\n      }\n    }\n\n    // calculate how many pixels must be drawn\n    uint32_t mcu_pixels = win_w * win_h;\n\n    tft.startWrite();\n\n    // draw image MCU block only if it will fit on the screen\n    if (( mcu_x + win_w ) <= tft.width() && ( mcu_y + win_h ) <= tft.height())\n    {\n\n      // Now set a MCU bounding window on the TFT to push pixels into (x, y, x + width - 1, y + height - 1)\n      tft.setAddrWindow(mcu_x, mcu_y, win_w, win_h);\n\n      // Write all MCU pixels to the TFT window\n      while (mcu_pixels--) {\n        // Push each pixel to the TFT MCU area\n        tft.pushColor(*pImg++);\n      }\n\n    }\n    else if ( (mcu_y + win_h) >= tft.height()) JpegDec.abort(); // Image has run off bottom of screen so abort decoding\n\n    tft.endWrite();\n  }\n\n  // calculate how long it took to draw the image\n  drawTime = millis() - drawTime;\n\n  // print the results to the serial port\n  Serial.print(F(  \"Total render time was    : \")); Serial.print(drawTime); Serial.println(F(\" ms\"));\n  Serial.println(F(\"\"));\n}\n\n//####################################################################################################\n// Print image information to the serial port (optional)\n//####################################################################################################\nvoid jpegInfo() {\n  Serial.println(F(\"===============\"));\n  Serial.println(F(\"JPEG image info\"));\n  Serial.println(F(\"===============\"));\n  Serial.print(F(  \"Width      :\")); Serial.println(JpegDec.width);\n  Serial.print(F(  \"Height     :\")); Serial.println(JpegDec.height);\n  Serial.print(F(  \"Components :\")); Serial.println(JpegDec.comps);\n  Serial.print(F(  \"MCU / row  :\")); Serial.println(JpegDec.MCUSPerRow);\n  Serial.print(F(  \"MCU / col  :\")); Serial.println(JpegDec.MCUSPerCol);\n  Serial.print(F(  \"Scan type  :\")); Serial.println(JpegDec.scanType);\n  Serial.print(F(  \"MCU width  :\")); Serial.println(JpegDec.MCUWidth);\n  Serial.print(F(  \"MCU height :\")); Serial.println(JpegDec.MCUHeight);\n  Serial.println(F(\"===============\"));\n}\n\n//####################################################################################################\n// Show the execution time (optional)\n//####################################################################################################\n// WARNING: for UNO/AVR legacy reasons printing text to the screen with the Mega might not work for\n// sketch sizes greater than ~70KBytes because 16 bit address pointers are used in some libraries.\n\n// The Due will work fine with the HX8357_Due library.\n\nvoid showTime(uint32_t msTime) {\n  //tft.setCursor(0, 0);\n  //tft.setTextFont(1);\n  //tft.setTextSize(2);\n  //tft.setTextColor(TFT_WHITE, TFT_BLACK);\n  //tft.print(F(\" JPEG drawn in \"));\n  //tft.print(msTime);\n  //tft.println(F(\" ms \"));\n  Serial.print(F(\" JPEG drawn in \"));\n  Serial.print(msTime);\n  Serial.println(F(\" ms \"));\n}\n\n"}}}Content-Length: 123847

{"jsonrpc":"2.0","method":"textDocument/didOpen","params":{"textDocument":{"uri":"file:///c%3A/Users/<USER>/Documents/Arduino/libraries/TFT_eSPI/examples/480%20x%20320/TFT_flash_jpg/jpeg1.h","languageId":"cpp","version":1,"text":"// We need this header file to use FLASH as storage with PROGMEM directive\n\n// Here is the 320 x 480 jpeg image data\nconst uint8_t Baboon40[] PROGMEM = {\n0xFF,0xD8,0xFF,0xE0,0x00,0x10,0x4A,0x46,0x49,0x46,0x00,0x01,0x01,0x00,0x00,0x01,0x00,0x01,0x00,0x00,0xFF,0xDB,0x00,0x43,0x00,0x14,0x0E,0x0F,0x12,0x0F,0x0D,0x14,\n0x12,0x10,0x12,0x17,0x15,0x14,0x18,0x1E,0x32,0x21,0x1E,0x1C,0x1C,0x1E,0x3D,0x2C,0x2E,0x24,0x32,0x49,0x40,0x4C,0x4B,0x47,0x40,0x46,0x45,0x50,0x5A,0x73,0x62,0x50,\n0x55,0x6D,0x56,0x45,0x46,0x64,0x88,0x65,0x6D,0x77,0x7B,0x81,0x82,0x81,0x4E,0x60,0x8D,0x97,0x8C,0x7D,0x96,0x73,0x7E,0x81,0x7C,0xFF,0xDB,0x00,0x43,0x01,0x15,0x17,\n0x17,0x1E,0x1A,0x1E,0x3B,0x21,0x21,0x3B,0x7C,0x53,0x46,0x53,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,\n0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0x7C,0xFF,0xC0,\n0x00,0x11,0x08,0x01,0xE0,0x01,0x40,0x03,0x01,0x22,0x00,0x02,0x11,0x01,0x03,0x11,0x01,0xFF,0xC4,0x00,0x1A,0x00,0x00,0x03,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,\n0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x03,0x04,0x01,0x05,0x00,0x06,0xFF,0xC4,0x00,0x3E,0x10,0x00,0x02,0x02,0x01,0x03,0x02,0x04,0x03,0x06,0x06,0x01,0x03,0x04,0x01,\n0x05,0x00,0x01,0x02,0x03,0x11,0x00,0x12,0x21,0x31,0x04,0x41,0x13,0x22,0x51,0x61,0x71,0x81,0x91,0x05,0x32,0x42,0xA1,0xB1,0xC1,0x14,0x23,0x52,0xD1,0xE1,0xF0,0x62,\n0x24,0x33,0xF1,0x15,0x43,0x72,0x82,0x34,0x06,0x53,0x63,0x73,0x92,0xFF,0xC4,0x00,0x1A,0x01,0x00,0x03,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,\n0x00,0x00,0x01,0x02,0x03,0x00,0x04,0x05,0x06,0xFF,0xC4,0x00,0x2A,0x11,0x00,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x03,0x00,0x00,0x00,0x00,0x00,0x01,\n0x02,0x11,0x03,0x21,0x12,0x31,0x04,0x41,0x32,0x51,0x22,0x61,0x13,0x71,0x05,0x23,0x14,0x33,0x42,0x81,0xF0,0xFF,0xDA,0x00,0x0C,0x03,0x01,0x00,0x02,0x11,0x03,0x11,\n0x00,0x3F,0x00,0x60,0xEA,0x1C,0xBB,0x47,0x2C,0x04,0x3A,0xF9,0x91,0xC1,0xEE,0x7B,0x7C,0x37,0xC9,0xA0,0x77,0x52,0xD2,0xEA,0x78,0x8A,0x29,0x64,0xEE,0x19,0x0E,0xFF,\n0x00,0x96,0x59,0xD5,0x3A,0x29,0x54,0x8E,0x4D,0x4E,0xC8,0x40,0x4D,0xEF,0xDA,0xBD,0xF2,0x63,0x3A,0xAA,0x20,0x94,0x16,0x8D,0xC9,0x01,0x80,0xDB,0x7A,0x37,0xEC,0x73,\n0x9F,0xFA,0x14,0x2E,0x9F,0xAA,0x0F,0x3A,0xA1,0x07,0x4C,0xEA,0x4E,0x8E,0x77,0xAE,0xC7,0xEA,0x37,0xC6,0xB9,0x58,0x5A,0x29,0x04,0x84,0x94,0xA4,0x00,0x9A,0xDB,0xD4,\n0xFE,0x9F,0xF8,0xC4,0x43,0x00,0x12,0