/*
 * RFID Handler Header
 * Manages MFRC522 RFID reader operations
 */

#ifndef RFID_HANDLER_H
#define RFID_HANDLER_H

#include <MFRC522.h>
#include <SPI.h>
#include "config.h"

class RFIDHandler {
private:
  MFRC522 mfrc522;
  
  // State management
  bool initialized;
  bool cardPresent;
  String lastCardUID;
  unsigned long lastReadTime;
  unsigned long lastCardTime;
  
  // Anti-collision and debouncing
  unsigned long debounceTime;
  bool cardDetected;
  
  // Statistics
  unsigned long totalReads;
  unsigned long successfulReads;
  unsigned long failedReads;
  
  // Private methods
  String formatUID(byte* uid, byte uidSize);
  bool validateCard(byte* uid, byte uidSize);
  void resetReader();
  bool performSelfTest();
  
public:
  RFIDHandler();
  ~RFIDHandler();
  
  // Initialization and setup
  bool begin();
  void reset();
  bool isInitialized() const { return initialized; }
  
  // Card detection and reading
  bool isCardPresent();
  String readCardUID();
  bool readCardData(byte* buffer, byte bufferSize);
  
  // Card operations
  bool authenticateCard(String uid);
  bool writeCardData(byte* data, byte dataSize);
  bool formatCard();
  
  // Reader management
  void enableReader();
  void disableReader();
  void powerDown();
  void powerUp();
  
  // Diagnostics and testing
  bool testReader();
  void printReaderInfo();
  void printCardInfo();
  String getReaderVersion();
  
  // Statistics
  unsigned long getTotalReads() const { return totalReads; }
  unsigned long getSuccessfulReads() const { return successfulReads; }
  unsigned long getFailedReads() const { return failedReads; }
  float getSuccessRate() const;
  void resetStatistics();
  
  // Configuration
  void setDebounceTime(unsigned long time) { debounceTime = time; }
  unsigned long getDebounceTime() const { return debounceTime; }
  
  // Error handling
  String getLastError() const;
  bool hasError() const;
  void clearError();
};

#endif // RFID_HANDLER_H
