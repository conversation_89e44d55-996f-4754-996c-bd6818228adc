// ArduinoJson - arduinojson.org
// Copyright Benoit Blanchon 2014-2020
// MIT License
//
// This example shows how to generate a JSON document with ArduinoJson.

#include <iostream>
#include "ArduinoJson.h"

int main() {
  // Allocate the JSON document
  //
  // Inside the brackets, 200 is the RAM allocated to this document.
  // Don't forget to change this value to match your requirement.
  // Use arduinojson.org/v6/assistant to compute the capacity.
  StaticJsonDocument<200> doc;

  // StaticJsonObject allocates memory on the stack, it can be
  // replaced by DynamicJsonDocument which allocates in the heap.
  //
  // DynamicJsonDocument  doc(200);

  // StaticJsonObject allocates memory on the stack, it can be
  // replaced by DynamicJsonDocument which allocates in the heap.
  //
  // DynamicJsonDocument  doc(200);

  // Add values in the document
  //
  doc["sensor"] = "gps";
  doc["time"] = **********;

  // Add an array.
  //
  JsonArray data = doc.createNestedArray("data");
  data.add(48.756080);
  data.add(2.302038);

  // Generate the minified JSON and send it to STDOUT
  //
  serializeJson(doc, std::cout);
  // The above line prints:
  // {"sensor":"gps","time":**********,"data":[48.756080,2.302038]}

  // Start a new line
  std::cout << std::endl;

  // Generate the prettified JSON and send it to STDOUT
  //
  serializeJsonPretty(doc, std::cout);
  // The above line prints:
  // {
  //   "sensor": "gps",
  //   "time": **********,
  //   "data": [
  //     48.756080,
  //     2.302038
  //   ]
  // }
}
