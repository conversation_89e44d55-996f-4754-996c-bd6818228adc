/*
 * LED Manager Header
 * Handles RGB LED status indication
 */

#ifndef LED_MANAGER_H
#define LED_MANAGER_H

#include <FastLED.h>
#include "config.h"

class LEDManager {
private:
  CRGB leds[1];
  bool initialized;
  LEDStatus currentStatus;
  uint8_t brightness;
  
  // Animation state
  bool animationEnabled;
  unsigned long lastUpdate;
  uint8_t animationStep;
  uint16_t animationSpeed;
  
  // Color definitions
  CRGB statusColors[7];
  
  // Private methods
  void setColor(CRGB color);
  void updateAnimation();
  CRGB blendColors(CRGB color1, CRGB color2, uint8_t blend);
  
public:
  LEDManager();
  ~LEDManager();
  
  // Initialization
  bool begin();
  void reset();
  bool isInitialized() const { return initialized; }
  
  // Status control
  void setStatus(LEDStatus status);
  LEDStatus getStatus() const { return currentStatus; }
  
  // Brightness control
  void setBrightness(uint8_t bright);
  uint8_t getBrightness() const { return brightness; }
  
  // Color control
  void setColor(uint8_t red, uint8_t green, uint8_t blue);
  void setColor(uint32_t color);
  void turnOff();
  void turnOn();
  
  // Animation control
  void enableAnimation(bool enable = true) { animationEnabled = enable; }
  void disableAnimation() { animationEnabled = false; }
  void setAnimationSpeed(uint16_t speed) { animationSpeed = speed; }
  
  // Predefined effects
  void breathe(CRGB color, uint16_t speed = 1000);
  void pulse(CRGB color, uint8_t pulses = 3);
  void flash(CRGB color, uint16_t duration = 500);
  void rainbow(uint16_t speed = 100);
  void fade(CRGB fromColor, CRGB toColor, uint16_t duration = 1000);
  
  // Status-specific effects
  void showReady();
  void showScanning();
  void showSuccess();
  void showError();
  void showProcessing();
  void showAdmin();
  
  // Update function (call in main loop)
  void update();
  
  // Testing
  void testLED();
  void colorCycle();
};

#endif // LED_MANAGER_H
