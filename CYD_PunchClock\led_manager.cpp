/*
 * LED Manager Implementation
 * Handles RGB LED status indication
 */

#include "led_manager.h"

LEDManager::LEDManager() : 
  initialized(false),
  currentStatus(LED_OFF),
  brightness(LED_BRIGHTNESS_MEDIUM),
  animationEnabled(false),
  lastUpdate(0),
  animationStep(0),
  animationSpeed(1000) {
  
  // Initialize status colors
  statusColors[LED_OFF] = CRGB::Black;
  statusColors[LED_READY] = CRGB::Green;
  statusColors[LED_SCANNING] = CRGB::Blue;
  statusColors[LED_SUCCESS] = CRGB::White;
  statusColors[LED_ERROR] = CRGB::Red;
  statusColors[LED_ADMIN] = CRGB::Purple;
  statusColors[LED_PROCESSING] = CRGB::Orange;
}

LEDManager::~LEDManager() {
  if (initialized) {
    turnOff();
  }
}

bool LEDManager::begin() {
  Serial.println("Initializing LED manager...");
  
  // Initialize FastLED
  FastLED.addLeds<WS2812, RGB_LED_PIN, GRB>(leds, 1);
  FastLED.setBrightness(brightness);
  FastLED.clear();
  FastLED.show();
  
  initialized = true;
  
  // Set initial status
  setStatus(LED_READY);
  
  Serial.println("LED manager initialized");
  return true;
}

void LEDManager::reset() {
  turnOff();
  currentStatus = LED_OFF;
  animationEnabled = false;
  animationStep = 0;
}

void LEDManager::setStatus(LEDStatus status) {
  if (!initialized) return;
  
  currentStatus = status;
  
  switch (status) {
    case LED_OFF:
      turnOff();
      break;
    case LED_READY:
      showReady();
      break;
    case LED_SCANNING:
      showScanning();
      break;
    case LED_SUCCESS:
      showSuccess();
      break;
    case LED_ERROR:
      showError();
      break;
    case LED_ADMIN:
      showAdmin();
      break;
    case LED_PROCESSING:
      showProcessing();
      break;
  }
}

void LEDManager::setBrightness(uint8_t bright) {
  brightness = CLAMP(bright, 0, 255);
  
  if (initialized) {
    FastLED.setBrightness(brightness);
    FastLED.show();
  }
}

void LEDManager::setColor(uint8_t red, uint8_t green, uint8_t blue) {
  if (!initialized) return;
  
  leds[0] = CRGB(red, green, blue);
  FastLED.show();
}

void LEDManager::setColor(uint32_t color) {
  if (!initialized) return;
  
  uint8_t red = (color >> 16) & 0xFF;
  uint8_t green = (color >> 8) & 0xFF;
  uint8_t blue = color & 0xFF;
  
  setColor(red, green, blue);
}

void LEDManager::turnOff() {
  if (!initialized) return;
  
  leds[0] = CRGB::Black;
  FastLED.show();
  animationEnabled = false;
}

void LEDManager::turnOn() {
  if (!initialized) return;
  
  setStatus(currentStatus);
}

void LEDManager::breathe(CRGB color, uint16_t speed) {
  if (!initialized) return;
  
  animationEnabled = true;
  animationSpeed = speed;
  setColor(color);
}

void LEDManager::pulse(CRGB color, uint8_t pulses) {
  if (!initialized) return;
  
  for (uint8_t i = 0; i < pulses; i++) {
    setColor(color);
    delay(100);
    turnOff();
    delay(100);
  }
}

void LEDManager::flash(CRGB color, uint16_t duration) {
  if (!initialized) return;
  
  setColor(color);
  delay(duration);
  turnOff();
}

void LEDManager::fade(CRGB fromColor, CRGB toColor, uint16_t duration) {
  if (!initialized) return;
  
  uint16_t steps = duration / 10; // 10ms per step
  
  for (uint16_t i = 0; i <= steps; i++) {
    uint8_t blend = map(i, 0, steps, 0, 255);
    CRGB currentColor = blendColors(fromColor, toColor, blend);
    setColor(currentColor);
    delay(10);
  }
}

void LEDManager::showReady() {
  if (!initialized) return;
  
  setColor(statusColors[LED_READY]);
  animationEnabled = false;
}

void LEDManager::showScanning() {
  if (!initialized) return;
  
  setColor(statusColors[LED_SCANNING]);
  animationEnabled = true;
  animationSpeed = 500; // Pulse every 500ms
}

void LEDManager::showSuccess() {
  if (!initialized) return;
  
  // Flash white 3 times
  pulse(statusColors[LED_SUCCESS], 3);
  
  // Return to ready state
  setStatus(LED_READY);
}

void LEDManager::showError() {
  if (!initialized) return;
  
  setColor(statusColors[LED_ERROR]);
  animationEnabled = true;
  animationSpeed = 250; // Fast blink for error
}

void LEDManager::showProcessing() {
  if (!initialized) return;
  
  setColor(statusColors[LED_PROCESSING]);
  animationEnabled = true;
  animationSpeed = 300; // Medium pulse
}

void LEDManager::showAdmin() {
  if (!initialized) return;
  
  setColor(statusColors[LED_ADMIN]);
  animationEnabled = true;
  animationSpeed = 1000; // Slow breathe
}

void LEDManager::update() {
  if (!initialized || !animationEnabled) return;
  
  unsigned long currentTime = millis();
  
  if (currentTime - lastUpdate >= animationSpeed / 100) {
    updateAnimation();
    lastUpdate = currentTime;
  }
}

void LEDManager::testLED() {
  if (!initialized) return;
  
  Serial.println("Testing LED...");
  
  // Test all status colors
  const char* statusNames[] = {"OFF", "READY", "SCANNING", "SUCCESS", "ERROR", "ADMIN", "PROCESSING"};
  
  for (int i = 0; i < 7; i++) {
    Serial.println("LED Status: " + String(statusNames[i]));
    setStatus((LEDStatus)i);
    delay(1000);
  }
  
  // Return to ready
  setStatus(LED_READY);
  
  Serial.println("LED test complete");
}

void LEDManager::colorCycle() {
  if (!initialized) return;
  
  Serial.println("LED color cycle...");
  
  // Cycle through rainbow colors
  for (int hue = 0; hue < 360; hue += 10) {
    CRGB color = CHSV(hue, 255, 255);
    setColor(color);
    delay(50);
  }
  
  // Return to ready
  setStatus(LED_READY);
  
  Serial.println("Color cycle complete");
}

// Private methods

void LEDManager::setColor(CRGB color) {
  if (!initialized) return;
  
  leds[0] = color;
  FastLED.show();
}

void LEDManager::updateAnimation() {
  if (!initialized) return;
  
  switch (currentStatus) {
    case LED_SCANNING:
    case LED_PROCESSING:
    case LED_ADMIN:
      // Breathing effect
      {
        uint8_t brightness = beatsin8(60, 50, 255); // 60 BPM breathing
        CRGB color = statusColors[currentStatus];
        color.nscale8(brightness);
        setColor(color);
      }
      break;
      
    case LED_ERROR:
      // Fast blinking
      {
        animationStep = (animationStep + 1) % 2;
        if (animationStep == 0) {
          setColor(statusColors[LED_ERROR]);
        } else {
          turnOff();
        }
      }
      break;
      
    default:
      // No animation for other states
      animationEnabled = false;
      break;
  }
}

CRGB LEDManager::blendColors(CRGB color1, CRGB color2, uint8_t blend) {
  CRGB result;
  
  result.r = map(blend, 0, 255, color1.r, color2.r);
  result.g = map(blend, 0, 255, color1.g, color2.g);
  result.b = map(blend, 0, 255, color1.b, color2.b);
  
  return result;
}
