/*
 * Audio Manager Header
 * Handles speaker and sound effects
 */

#ifndef AUDIO_MANAGER_H
#define AUDIO_MANAGER_H

#include <Arduino.h>
#include "config.h"

class AudioManager {
private:
  bool initialized;
  bool enabled;
  uint8_t volume;
  
  // Sound queue for non-blocking playback
  struct SoundEvent {
    int frequency;
    int duration;
    unsigned long startTime;
    bool playing;
  };
  
  SoundEvent currentSound;
  std::vector<SoundEvent> soundQueue;
  
  // Private methods
  void playTone(int frequency, int duration);
  void stopTone();
  void processQueue();
  
public:
  AudioManager();
  ~AudioManager();
  
  // Initialization
  bool begin();
  void reset();
  bool isInitialized() const { return initialized; }
  
  // Enable/disable audio
  void enable() { enabled = true; }
  void disable() { enabled = false; }
  bool isEnabled() const { return enabled; }
  
  // Volume control
  void setVolume(uint8_t vol);
  uint8_t getVolume() const { return volume; }
  
  // Basic sound effects
  void playStartupSound();
  void playSuccessSound();
  void playErrorSound();
  void playWarningSound();
  void playClickSound();
  void playPunchSound(bool punchIn);
  void playBeep(int frequency = SOUND_SUCCESS, int duration = SOUND_DURATION_SHORT);
  
  // Advanced sound effects
  void playMelody(int* frequencies, int* durations, int length);
  void playChime();
  void playAlarm();
  void playNotification();
  
  // Queue management
  void queueSound(int frequency, int duration);
  void clearQueue();
  bool isPlaying() const;
  void update(); // Call in main loop for non-blocking playback
  
  // Configuration
  void setEnabled(bool enable) { enabled = enable; }
  
  // Testing
  void testSpeaker();
  void playTestTones();
};

#endif // AUDIO_MANAGER_H
