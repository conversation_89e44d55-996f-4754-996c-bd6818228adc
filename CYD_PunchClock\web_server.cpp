/*
 * Web Server Manager Implementation
 * Handles web interface and API endpoints
 */

#include "web_server.h"

WebServerManager::WebServerManager() : 
  server(nullptr),
  employeeDB(nullptr),
  dataManager(nullptr),
  initialized(false),
  serverRunning(false),
  serverPort(WEB_SERVER_PORT),
  adminUsername("admin"),
  adminPassword("admin"),
  authEnabled(false),
  totalRequests(0),
  apiRequests(0),
  webRequests(0) {
}

WebServerManager::~WebServerManager() {
  stop();
}

bool WebServerManager::begin() {
  return begin(WEB_SERVER_PORT);
}

bool WebServerManager::begin(int port) {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("Cannot start web server - WiFi not connected");
    return false;
  }
  
  Serial.println("Initializing web server...");
  
  serverPort = port;
  server = new AsyncWebServer(serverPort);
  
  setupRoutes();
  
  server->begin();
  serverRunning = true;
  initialized = true;
  
  Serial.println("Web server started on port " + String(serverPort));
  Serial.println("Access at: http://" + WiFi.localIP().toString() + ":" + String(serverPort));
  
  return true;
}

bool WebServerManager::begin(EmployeeDB* empDB, DataManager* dataMgr) {
  employeeDB = empDB;
  dataManager = dataMgr;
  return begin();
}

void WebServerManager::stop() {
  if (server) {
    server->end();
    delete server;
    server = nullptr;
  }
  
  serverRunning = false;
  initialized = false;
  
  Serial.println("Web server stopped");
}

void WebServerManager::handleClient() {
  // AsyncWebServer handles clients automatically
  // This method is kept for compatibility
}

void WebServerManager::enableAuth(String username, String password) {
  adminUsername = username;
  adminPassword = password;
  authEnabled = true;
  
  Serial.println("Web server authentication enabled");
}

void WebServerManager::disableAuth() {
  authEnabled = false;
  Serial.println("Web server authentication disabled");
}

void WebServerManager::resetStatistics() {
  totalRequests = 0;
  apiRequests = 0;
  webRequests = 0;
}

String WebServerManager::getServerInfo() {
  DynamicJsonDocument doc(512);
  
  doc["running"] = serverRunning;
  doc["port"] = serverPort;
  doc["authEnabled"] = authEnabled;
  doc["totalRequests"] = totalRequests;
  doc["apiRequests"] = apiRequests;
  doc["webRequests"] = webRequests;
  doc["ipAddress"] = WiFi.localIP().toString();
  
  String result;
  serializeJson(doc, result);
  return result;
}

void WebServerManager::printServerStatus() {
  Serial.println("=== Web Server Status ===");
  Serial.println("Running: " + String(serverRunning ? "Yes" : "No"));
  Serial.println("Port: " + String(serverPort));
  Serial.println("IP Address: " + WiFi.localIP().toString());
  Serial.println("Auth Enabled: " + String(authEnabled ? "Yes" : "No"));
  Serial.println("Total Requests: " + String(totalRequests));
  Serial.println("API Requests: " + String(apiRequests));
  Serial.println("Web Requests: " + String(webRequests));
  Serial.println("========================");
}

// Private methods

void WebServerManager::setupRoutes() {
  setupAPIRoutes();
  setupWebRoutes();
  
  // Handle CORS
  server->onNotFound([](AsyncWebServerRequest* request) {
    if (request->method() == HTTP_OPTIONS) {
      request->send(200);
    } else {
      request->send(404, "text/plain", "Not Found");
    }
  });
}

void WebServerManager::setupAPIRoutes() {
  // Employee management API
  server->on("/api/employees", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleGetEmployees(request);
  });
  
  server->on("/api/employees", HTTP_POST, [this](AsyncWebServerRequest* request) {
    handleAddEmployee(request);
  });
  
  server->on("/api/employees", HTTP_PUT, [this](AsyncWebServerRequest* request) {
    handleUpdateEmployee(request);
  });
  
  server->on("/api/employees", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
    handleDeleteEmployee(request);
  });
  
  // Attendance API
  server->on("/api/attendance", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleGetAttendance(request);
  });
  
  // Reports API
  server->on("/api/reports", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleGetReports(request);
  });
  
  // System API
  server->on("/api/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleSystemStatus(request);
  });
  
  server->on("/api/config", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleSystemConfig(request);
  });
}

void WebServerManager::setupWebRoutes() {
  // Main web pages
  server->on("/", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleRoot(request);
  });
  
  server->on("/login", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleLogin(request);
  });
  
  server->on("/dashboard", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleDashboard(request);
  });
  
  server->on("/employees", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleEmployeeManagement(request);
  });
  
  server->on("/reports", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleReports(request);
  });
  
  server->on("/settings", HTTP_GET, [this](AsyncWebServerRequest* request) {
    handleSettings(request);
  });
}

bool WebServerManager::authenticate(AsyncWebServerRequest* request) {
  if (!authEnabled) return true;
  
  // Simple basic authentication check
  if (request->hasHeader("Authorization")) {
    String auth = request->getHeader("Authorization")->value();
    // In a real implementation, you'd properly decode and validate the auth header
    return true; // Simplified for now
  }
  
  return false;
}

void WebServerManager::handleGetEmployees(AsyncWebServerRequest* request) {
  totalRequests++;
  apiRequests++;
  
  if (!authenticate(request)) {
    sendErrorResponse(request, "Unauthorized", 401);
    return;
  }
  
  if (!employeeDB) {
    sendErrorResponse(request, "Employee database not available", 500);
    return;
  }
  
  DynamicJsonDocument doc(JSON_BUFFER_SIZE * 2);
  JsonArray empArray = doc.createNestedArray("employees");
  
  std::vector<Employee> employees = employeeDB->getAllEmployees();
  for (const auto& emp : employees) {
    JsonObject empObj = empArray.createNestedObject();
    empObj["id"] = emp.id;
    empObj["name"] = emp.name;
    empObj["department"] = emp.department;
    empObj["active"] = emp.active;
    empObj["lastAction"] = emp.lastAction;
    empObj["lastTimestamp"] = emp.lastTimestamp;
    // Don't include sensitive data like RFID UID or PIN in API response
  }
  
  doc["total"] = employees.size();
  doc["active"] = employeeDB->getActiveEmployeeCount();
  
  String response;
  serializeJson(doc, response);
  sendJSONResponse(request, response);
}

void WebServerManager::handleSystemStatus(AsyncWebServerRequest* request) {
  totalRequests++;
  apiRequests++;
  
  String response = generateSystemStatusJSON();
  sendJSONResponse(request, response);
}

void WebServerManager::handleRoot(AsyncWebServerRequest* request) {
  totalRequests++;
  webRequests++;
  
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <title>Punch Clock System</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .nav { display: flex; justify-content: space-around; margin-bottom: 30px; }
        .nav a { text-decoration: none; color: #007bff; padding: 10px 20px; border: 1px solid #007bff; border-radius: 5px; }
        .nav a:hover { background-color: #007bff; color: white; }
        .status { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .footer { text-align: center; color: #666; margin-top: 30px; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕐 Employee Punch Clock System</h1>
            <p>ESP32-2432S028R Management Interface</p>
        </div>
        
        <div class="nav">
            <a href="/dashboard">📊 Dashboard</a>
            <a href="/employees">👥 Employees</a>
            <a href="/reports">📈 Reports</a>
            <a href="/settings">⚙️ Settings</a>
        </div>
        
        <div class="status">
            <h3>System Status</h3>
            <p><strong>Device:</strong> ESP32-2432S028R</p>
            <p><strong>Firmware:</strong> v)" + String(FIRMWARE_VERSION) + R"(</p>
            <p><strong>WiFi:</strong> Connected</p>
            <p><strong>IP Address:</strong> )" + WiFi.localIP().toString() + R"(</p>
            <p><strong>Uptime:</strong> <span id="uptime">Loading...</span></p>
        </div>
        
        <div class="footer">
            <p>Punch Clock System - Built with ESP32</p>
        </div>
    </div>
    
    <script>
        function updateUptime() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('uptime').textContent = data.uptime || 'Unknown';
                })
                .catch(error => console.error('Error:', error));
        }
        
        updateUptime();
        setInterval(updateUptime, 30000); // Update every 30 seconds
    </script>
</body>
</html>
)";
  
  request->send(200, "text/html", html);
}

void WebServerManager::handleDashboard(AsyncWebServerRequest* request) {
  totalRequests++;
  webRequests++;
  
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <title>Dashboard - Punch Clock</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f0f0; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .recent-activity { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .activity-item { padding: 10px; border-bottom: 1px solid #eee; }
        .activity-item:last-child { border-bottom: none; }
        .nav-back { margin-bottom: 20px; }
        .nav-back a { text-decoration: none; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-back">
            <a href="/">← Back to Home</a>
        </div>
        
        <div class="header">
            <h1>📊 System Dashboard</h1>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalEmployees">-</div>
                <div class="stat-label">Total Employees</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeEmployees">-</div>
                <div class="stat-label">Active Employees</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayPunches">-</div>
                <div class="stat-label">Today's Punches</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="systemUptime">-</div>
                <div class="stat-label">System Uptime</div>
            </div>
        </div>
        
        <div class="recent-activity">
            <h3>Recent Activity</h3>
            <div id="activityList">
                <div class="activity-item">Loading recent activity...</div>
            </div>
        </div>
    </div>
    
    <script>
        function updateDashboard() {
            // Update employee stats
            fetch('/api/employees')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalEmployees').textContent = data.total || 0;
                    document.getElementById('activeEmployees').textContent = data.active || 0;
                })
                .catch(error => console.error('Error fetching employees:', error));
            
            // Update system status
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('systemUptime').textContent = data.uptime || 'Unknown';
                })
                .catch(error => console.error('Error fetching status:', error));
        }
        
        updateDashboard();
        setInterval(updateDashboard, 30000); // Update every 30 seconds
    </script>
</body>
</html>
)";
  
  request->send(200, "text/html", html);
}

String WebServerManager::generateSystemStatusJSON() {
  DynamicJsonDocument doc(1024);
  
  doc["firmware"] = FIRMWARE_VERSION;
  doc["hardware"] = HARDWARE_VERSION;
  doc["uptime"] = millis() / 1000;
  doc["freeHeap"] = ESP.getFreeHeap();
  doc["wifiConnected"] = (WiFi.status() == WL_CONNECTED);
  doc["ipAddress"] = WiFi.localIP().toString();
  doc["totalRequests"] = totalRequests;
  
  if (employeeDB) {
    doc["totalEmployees"] = employeeDB->getTotalEmployees();
    doc["activeEmployeeCount"] = employeeDB->getActiveEmployeeCount();
  }
  
  String result;
  serializeJson(doc, result);
  return result;
}

void WebServerManager::sendJSONResponse(AsyncWebServerRequest* request, String json, int code) {
  request->send(code, "application/json", json);
}

void WebServerManager::sendErrorResponse(AsyncWebServerRequest* request, String message, int code) {
  DynamicJsonDocument doc(256);
  doc["error"] = message;
  doc["code"] = code;
  
  String response;
  serializeJson(doc, response);
  
  request->send(code, "application/json", response);
}

// Placeholder implementations for other handlers
void WebServerManager::handleAddEmployee(AsyncWebServerRequest* request) {
  sendErrorResponse(request, "Not implemented yet", 501);
}

void WebServerManager::handleUpdateEmployee(AsyncWebServerRequest* request) {
  sendErrorResponse(request, "Not implemented yet", 501);
}

void WebServerManager::handleDeleteEmployee(AsyncWebServerRequest* request) {
  sendErrorResponse(request, "Not implemented yet", 501);
}

void WebServerManager::handleGetAttendance(AsyncWebServerRequest* request) {
  sendErrorResponse(request, "Not implemented yet", 501);
}

void WebServerManager::handleGetReports(AsyncWebServerRequest* request) {
  sendErrorResponse(request, "Not implemented yet", 501);
}

void WebServerManager::handleSystemConfig(AsyncWebServerRequest* request) {
  sendErrorResponse(request, "Not implemented yet", 501);
}

void WebServerManager::handleLogin(AsyncWebServerRequest* request) {
  request->send(200, "text/html", "<h1>Login Page - Not implemented yet</h1>");
}

void WebServerManager::handleEmployeeManagement(AsyncWebServerRequest* request) {
  request->send(200, "text/html", "<h1>Employee Management - Not implemented yet</h1>");
}

void WebServerManager::handleReports(AsyncWebServerRequest* request) {
  request->send(200, "text/html", "<h1>Reports - Not implemented yet</h1>");
}

void WebServerManager::handleSettings(AsyncWebServerRequest* request) {
  request->send(200, "text/html", "<h1>Settings - Not implemented yet</h1>");
}
