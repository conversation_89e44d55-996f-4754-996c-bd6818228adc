/*
 * Simple Web Server Implementation
 * Uses built-in ESP32 WebServer instead of ESPAsyncWebServer
 */

#include "simple_web_server.h"

SimpleWebServer::SimpleWebServer() : 
  server(nullptr),
  employeeDB(nullptr),
  dataManager(nullptr),
  initialized(false),
  serverRunning(false),
  serverPort(WEB_SERVER_PORT),
  totalRequests(0),
  apiRequests(0),
  webRequests(0) {
}

SimpleWebServer::~SimpleWebServer() {
  stop();
}

bool SimpleWebServer::begin() {
  return begin(WEB_SERVER_PORT);
}

bool SimpleWebServer::begin(int port) {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("Cannot start web server - WiFi not connected");
    return false;
  }
  
  Serial.println("Initializing simple web server...");
  
  serverPort = port;
  server = new WebServer(serverPort);
  
  setupRoutes();
  
  server->begin();
  serverRunning = true;
  initialized = true;
  
  Serial.println("Simple web server started on port " + String(serverPort));
  Serial.println("Access at: http://" + WiFi.localIP().toString() + ":" + String(serverPort));
  
  return true;
}

bool SimpleWebServer::begin(EmployeeDB* empDB, DataManager* dataMgr) {
  employeeDB = empDB;
  dataManager = dataMgr;
  return begin();
}

void SimpleWebServer::stop() {
  if (server) {
    server->stop();
    delete server;
    server = nullptr;
  }
  
  serverRunning = false;
  initialized = false;
  
  Serial.println("Simple web server stopped");
}

void SimpleWebServer::handleClient() {
  if (server && serverRunning) {
    server->handleClient();
  }
}

void SimpleWebServer::resetStatistics() {
  totalRequests = 0;
  apiRequests = 0;
  webRequests = 0;
}

String SimpleWebServer::getServerInfo() {
  DynamicJsonDocument doc(512);
  
  doc["running"] = serverRunning;
  doc["port"] = serverPort;
  doc["totalRequests"] = totalRequests;
  doc["apiRequests"] = apiRequests;
  doc["webRequests"] = webRequests;
  doc["ipAddress"] = WiFi.localIP().toString();
  
  String result;
  serializeJson(doc, result);
  return result;
}

void SimpleWebServer::printServerStatus() {
  Serial.println("=== Simple Web Server Status ===");
  Serial.println("Running: " + String(serverRunning ? "Yes" : "No"));
  Serial.println("Port: " + String(serverPort));
  Serial.println("IP Address: " + WiFi.localIP().toString());
  Serial.println("Total Requests: " + String(totalRequests));
  Serial.println("===============================");
}

// Private methods

void SimpleWebServer::setupRoutes() {
  // Main page
  server->on("/", [this]() {
    handleRoot();
  });
  
  // API endpoints
  server->on("/api/status", [this]() {
    handleStatus();
  });
  
  server->on("/api/employees", [this]() {
    handleEmployees();
  });
  
  // Handle not found
  server->onNotFound([this]() {
    handleNotFound();
  });
}

void SimpleWebServer::handleRoot() {
  totalRequests++;
  webRequests++;
  
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <title>RFID Punch Clock</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .status { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .api-links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 20px; }
        .api-link { background: #007bff; color: white; padding: 10px; text-align: center; border-radius: 5px; text-decoration: none; }
        .api-link:hover { background: #0056b3; }
        .footer { text-align: center; color: #666; margin-top: 30px; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕐 RFID Punch Clock System</h1>
            <p>ESP32-2432S028R Management Interface</p>
        </div>
        
        <div class="status">
            <h3>System Status</h3>
            <p><strong>Device:</strong> ESP32-2432S028R (CYD)</p>
            <p><strong>Firmware:</strong> v)" + String(FIRMWARE_VERSION) + R"(</p>
            <p><strong>WiFi:</strong> Connected</p>
            <p><strong>IP Address:</strong> )" + WiFi.localIP().toString() + R"(</p>
            <p><strong>Free Memory:</strong> )" + String(ESP.getFreeHeap()) + R"( bytes</p>
        </div>
        
        <div class="api-links">
            <a href="/api/status" class="api-link">📊 System Status</a>
            <a href="/api/employees" class="api-link">👥 Employee List</a>
        </div>
        
        <div class="footer">
            <p>Simple Web Interface - Compatible with all ESP32 versions</p>
        </div>
    </div>
</body>
</html>
)";
  
  server->send(200, "text/html", html);
}

void SimpleWebServer::handleStatus() {
  totalRequests++;
  apiRequests++;
  
  String response = generateStatusJSON();
  sendJSONResponse(response);
}

void SimpleWebServer::handleEmployees() {
  totalRequests++;
  apiRequests++;
  
  String response = generateEmployeesJSON();
  sendJSONResponse(response);
}

void SimpleWebServer::handleNotFound() {
  totalRequests++;
  
  sendErrorResponse("Not Found", 404);
}

String SimpleWebServer::generateStatusJSON() {
  DynamicJsonDocument doc(1024);
  
  doc["firmware"] = FIRMWARE_VERSION;
  doc["hardware"] = HARDWARE_VERSION;
  doc["uptime"] = millis() / 1000;
  doc["freeHeap"] = ESP.getFreeHeap();
  doc["wifiConnected"] = (WiFi.status() == WL_CONNECTED);
  doc["ipAddress"] = WiFi.localIP().toString();
  doc["totalRequests"] = totalRequests;
  
  if (employeeDB) {
    doc["totalEmployees"] = employeeDB->getTotalEmployees();
    doc["activeEmployees"] = employeeDB->getActiveEmployeeCount();
  }
  
  String result;
  serializeJson(doc, result);
  return result;
}

String SimpleWebServer::generateEmployeesJSON() {
  DynamicJsonDocument doc(JSON_BUFFER_SIZE * 2);
  JsonArray empArray = doc.createNestedArray("employees");
  
  if (employeeDB) {
    std::vector<Employee> employees = employeeDB->getAllEmployees();
    for (const auto& emp : employees) {
      JsonObject empObj = empArray.createNestedObject();
      empObj["id"] = emp.id;
      empObj["name"] = emp.name;
      empObj["department"] = emp.department;
      empObj["active"] = emp.active;
      empObj["lastAction"] = emp.lastAction;
      empObj["lastTimestamp"] = emp.lastTimestamp;
      // Don't include sensitive data like RFID UID or PIN
    }
    
    doc["total"] = employees.size();
    doc["active"] = employeeDB->getActiveEmployeeCount();
  } else {
    doc["total"] = 0;
    doc["active"] = 0;
    doc["error"] = "Employee database not available";
  }
  
  String result;
  serializeJson(doc, result);
  return result;
}

void SimpleWebServer::sendJSONResponse(String json, int code) {
  server->send(code, "application/json", json);
}

void SimpleWebServer::sendErrorResponse(String message, int code) {
  DynamicJsonDocument doc(256);
  doc["error"] = message;
  doc["code"] = code;
  
  String response;
  serializeJson(doc, response);
  
  server->send(code, "application/json", response);
}
