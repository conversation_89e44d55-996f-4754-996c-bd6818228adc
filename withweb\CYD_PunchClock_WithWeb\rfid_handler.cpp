/*
 * RFID Handler Implementation - Minimal Version
 */

#include "rfid_handler.h"

RFIDHandler::RFIDHandler() : 
  mfrc522(RFID_SDA, RFID_RST),
  initialized(false),
  lastCardUID(""),
  lastReadTime(0) {
}

RFIDHandler::~RFIDHandler() {
  if (initialized) {
    mfrc522.PCD_SoftPowerDown();
  }
}

bool RFIDHandler::begin() {
  Serial.println("Initializing RFID reader...");
  
  // Initialize SPI
  SPI.begin();
  
  // Initialize MFRC522
  mfrc522.PCD_Init();
  
  // Check RFID version
  byte version = mfrc522.PCD_ReadRegister(MFRC522::VersionReg);
  
  if (version == 0x00 || version == 0xFF) {
    Serial.println("RFID: FAIL - No response");
    return false;
  }
  
  // Set antenna gain to maximum
  mfrc522.PCD_SetAntennaGain(mfrc522.RxGain_max);
  
  initialized = true;
  Serial.println("RFID reader initialized successfully");
  
  return true;
}

void RFIDHandler::reset() {
  if (!initialized) return;
  
  mfrc522.PCD_Reset();
  delay(50);
  mfrc522.PCD_Init();
  
  lastCardUID = "";
  
  Serial.println("RFID reader reset");
}

bool RFIDHandler::isCardPresent() {
  if (!initialized) return false;
  
  // Look for new cards
  if (!mfrc522.PICC_IsNewCardPresent()) {
    return false;
  }
  
  // Select one of the cards
  if (!mfrc522.PICC_ReadCardSerial()) {
    return false;
  }
  
  return true;
}

String RFIDHandler::readCardUID() {
  if (!initialized) {
    return "";
  }
  
  String uid = "";
  
  for (byte i = 0; i < mfrc522.uid.size; i++) {
    if (i > 0) uid += ":";
    if (mfrc522.uid.uidByte[i] < 0x10) uid += "0";
    uid += String(mfrc522.uid.uidByte[i], HEX);
  }
  
  uid.toUpperCase();
  lastCardUID = uid;
  lastReadTime = millis();
  
  // Halt the card to prevent multiple reads
  mfrc522.PICC_HaltA();
  mfrc522.PCD_StopCrypto1();
  
  Serial.println("Card UID read: " + uid);
  return uid;
}

void RFIDHandler::enableReader() {
  if (!initialized) return;
  
  mfrc522.PCD_SoftPowerUp();
  Serial.println("RFID reader enabled");
}

void RFIDHandler::disableReader() {
  if (!initialized) return;
  
  mfrc522.PCD_SoftPowerDown();
  Serial.println("RFID reader disabled");
}

bool RFIDHandler::testReader() {
  if (!initialized) return false;
  
  byte version = mfrc522.PCD_ReadRegister(MFRC522::VersionReg);
  return (version != 0x00 && version != 0xFF);
}

void RFIDHandler::printReaderInfo() {
  if (!initialized) return;
  
  Serial.println("=== RFID Reader Information ===");
  Serial.print("Firmware Version: 0x");
  Serial.println(mfrc522.PCD_ReadRegister(MFRC522::VersionReg), HEX);
  
  String version = getReaderVersion();
  Serial.println("Reader Version: " + version);
  
  Serial.println("===============================");
}

String RFIDHandler::getReaderVersion() {
  if (!initialized) return "Unknown";
  
  byte version = mfrc522.PCD_ReadRegister(MFRC522::VersionReg);
  
  switch (version) {
    case 0x88: return "Clone";
    case 0x90: return "v0.0";
    case 0x91: return "v1.0";
    case 0x92: return "v2.0";
    default: return "Unknown (0x" + String(version, HEX) + ")";
  }
}
