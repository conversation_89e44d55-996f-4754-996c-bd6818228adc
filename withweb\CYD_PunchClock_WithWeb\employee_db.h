/*
 * Employee Database Header - Minimal Version
 */

#ifndef EMPLOYEE_DB_H
#define EMPLOYEE_DB_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <SD.h>
#include <FS.h>
#include "config.h"
#include "data_manager.h"

class EmployeeDB {
private:
  std::vector<Employee> employees;
  DataManager* dataManager;
  bool initialized;
  bool dataLoaded;
  
  int totalEmployees;
  int activeEmployees;
  unsigned long lastSave;
  
  bool loadFromFile();
  bool saveToFile();
  int getNextEmployeeId();
  bool validateEmployee(const Employee& emp);
  
public:
  EmployeeDB();
  ~EmployeeDB();
  
  bool begin();
  bool begin(DataManager* dm);
  void reset();
  bool isInitialized() const { return initialized; }
  
  bool addEmployee(const Employee& employee);
  bool updateEmployee(const Employee& employee);
  bool deleteEmployee(int employeeId);
  bool activateEmployee(int employeeId);
  bool deactivateEmployee(int employeeId);
  
  Employee* findById(int employeeId);
  Employee* findByRFID(const String& rfidUID);
  Employee* findByName(const String& name);
  
  std::vector<Employee> getAllEmployees();
  std::vector<Employee> getActiveEmployees();
  int getEmployeeCount() const { return totalEmployees; }
  int getActiveEmployeeCount() const { return activeEmployees; }
  
  bool isRFIDRegistered(const String& rfidUID);
  bool registerRFID(int employeeId, const String& rfidUID);
  bool unregisterRFID(int employeeId);
  
  bool save();
  bool load();
  
  void updateStatistics();
  int getTotalEmployees() const { return totalEmployees; }
  int getActiveEmployees() const { return activeEmployees; }
  unsigned long getLastSaveTime() const { return lastSave; }
  
  void printEmployeeList();
};

#endif // EMPLOYEE_DB_H
