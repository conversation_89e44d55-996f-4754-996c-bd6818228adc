/*
 * LED Manager Implementation - Minimal Version
 */

#include "led_manager.h"

LEDManager::LEDManager() : 
  initialized(false),
  currentStatus(LED_OFF),
  brightness(LED_BRIGHTNESS_MEDIUM) {
}

LEDManager::~LEDManager() {
  if (initialized) {
    turnOff();
  }
}

bool LEDManager::begin() {
  Serial.println("Initializing LED manager...");
  
  // Initialize FastLED
  FastLED.addLeds<WS2812, RGB_LED_PIN, GRB>(leds, 1);
  FastLED.setBrightness(brightness);
  FastLED.clear();
  FastLED.show();
  
  initialized = true;
  
  // Set initial status
  setStatus(LED_READY);
  
  Serial.println("LED manager initialized");
  return true;
}

void LEDManager::reset() {
  turnOff();
  currentStatus = LED_OFF;
}

void LEDManager::setStatus(LEDStatus status) {
  if (!initialized) return;
  
  currentStatus = status;
  
  switch (status) {
    case LED_OFF:
      leds[0] = CRGB::Black;
      break;
    case LED_READY:
      leds[0] = CRGB::Green;
      break;
    case LED_SCANNING:
      leds[0] = CRGB::Blue;
      break;
    case LED_SUCCESS:
      leds[0] = CRGB::White;
      break;
    case LED_ERROR:
      leds[0] = CRGB::Red;
      break;
    case LED_ADMIN:
      leds[0] = CRGB::Purple;
      break;
    case LED_PROCESSING:
      leds[0] = CRGB::Orange;
      break;
  }
  
  FastLED.show();
}

void LEDManager::setBrightness(uint8_t bright) {
  brightness = CLAMP(bright, 0, 255);
  
  if (initialized) {
    FastLED.setBrightness(brightness);
    FastLED.show();
  }
}

void LEDManager::setColor(uint8_t red, uint8_t green, uint8_t blue) {
  if (!initialized) return;
  
  leds[0] = CRGB(red, green, blue);
  FastLED.show();
}

void LEDManager::setColor(uint32_t color) {
  if (!initialized) return;
  
  uint8_t red = (color >> 16) & 0xFF;
  uint8_t green = (color >> 8) & 0xFF;
  uint8_t blue = color & 0xFF;
  
  setColor(red, green, blue);
}

void LEDManager::turnOff() {
  if (!initialized) return;
  
  leds[0] = CRGB::Black;
  FastLED.show();
}

void LEDManager::turnOn() {
  if (!initialized) return;
  
  setStatus(currentStatus);
}

void LEDManager::update() {
  // Simple update - no animations in minimal version
}

void LEDManager::testLED() {
  if (!initialized) return;
  
  Serial.println("Testing LED...");
  
  // Test colors
  leds[0] = CRGB::Red;
  FastLED.show();
  delay(500);
  
  leds[0] = CRGB::Green;
  FastLED.show();
  delay(500);
  
  leds[0] = CRGB::Blue;
  FastLED.show();
  delay(500);
  
  leds[0] = CRGB::White;
  FastLED.show();
  delay(500);
  
  leds[0] = CRGB::Black;
  FastLED.show();
  
  Serial.println("LED test complete");
}
