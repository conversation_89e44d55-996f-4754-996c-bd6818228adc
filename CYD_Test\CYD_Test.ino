/*
 * Hardware and Library Test for ESP32-2432S028R (CYD)
 * Tests all components and libraries before running main firmware
 * 
 * This is a separate test sketch - do not put in the same folder as main firmware
 */

// Test basic includes
#include <Arduino.h>
#include <SPI.h>
#include <WiFi.h>

// Test TFT and Touch
#include <TFT_eSPI.h>
#include <XPT2046_Touchscreen.h>

// Test RFID
#include <MFRC522.h>

// Test Storage
#include <SD.h>
#include <SPIFFS.h>
#include <FS.h>

// Test JSON
#include <ArduinoJson.h>

// Test Network and Time
#include <NTPClient.h>
#include <WiFiUdp.h>

// Test LED
#include <FastLED.h>

// Pin definitions for ESP32-2432S028R
#define TFT_BL      21
#define TOUCH_CS    33
#define TOUCH_IRQ   36
#define SD_CS       5
#define RGB_LED_PIN 4
#define SPEAKER_PIN 26
#define LDR_PIN     34
#define RFID_SDA    22
#define RFID_RST    16

// Test objects
TFT_eSPI tft = TFT_eSPI();
XPT2046_Touchscreen touch(TOUCH_CS, TOUCH_IRQ);
MFRC522 mfrc522(RFID_SDA, RFID_RST);
CRGB leds[1];

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("\n=== ESP32-2432S028R Library Test ===");
  
  // Test pin definitions
  pinMode(TFT_BL, OUTPUT);
  pinMode(SPEAKER_PIN, OUTPUT);
  pinMode(RGB_LED_PIN, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  
  Serial.println("✅ Pin definitions work!");
  
  // Test display
  tft.init();
  tft.setRotation(0);
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("Library Test");
  
  Serial.println("✅ TFT display works!");
  
  // Test touch
  touch.begin();
  touch.setRotation(0);
  
  Serial.println("✅ Touch controller initialized!");
  
  // Test RFID
  SPI.begin();
  mfrc522.PCD_Init();
  byte version = mfrc522.PCD_ReadRegister(MFRC522::VersionReg);
  
  if (version != 0x00 && version != 0xFF) {
    Serial.println("✅ RFID reader detected!");
    Serial.printf("   RFID Version: 0x%02X\n", version);
  } else {
    Serial.println("❌ RFID reader not detected - check wiring");
  }
  
  // Test SD card
  if (SD.begin(SD_CS)) {
    uint64_t cardSize = SD.cardSize() / (1024 * 1024);
    Serial.println("✅ SD card detected!");
    Serial.printf("   SD Card Size: %lluMB\n", cardSize);
  } else {
    Serial.println("❌ SD card not detected - check card insertion");
  }
  
  // Test SPIFFS
  if (SPIFFS.begin(true)) {
    Serial.println("✅ SPIFFS initialized!");
  } else {
    Serial.println("❌ SPIFFS initialization failed");
  }
  
  // Test JSON
  DynamicJsonDocument doc(1024);
  doc["test"] = "value";
  doc["number"] = 42;
  doc["boolean"] = true;
  
  String jsonString;
  serializeJson(doc, jsonString);
  
  Serial.println("✅ JSON operations work!");
  Serial.println("   JSON: " + jsonString);
  
  // Test LED
  FastLED.addLeds<WS2812, RGB_LED_PIN, GRB>(leds, 1);
  FastLED.setBrightness(128);
  leds[0] = CRGB::Red;
  FastLED.show();
  delay(500);
  leds[0] = CRGB::Green;
  FastLED.show();
  delay(500);
  leds[0] = CRGB::Blue;
  FastLED.show();
  delay(500);
  leds[0] = CRGB::Black;
  FastLED.show();
  
  Serial.println("✅ RGB LED works!");
  
  // Test speaker
  tone(SPEAKER_PIN, 1000, 200);
  delay(300);
  tone(SPEAKER_PIN, 1500, 200);
  delay(300);
  noTone(SPEAKER_PIN);
  
  Serial.println("✅ Speaker works!");
  
  // Test light sensor
  int lightValue = analogRead(LDR_PIN);
  Serial.println("✅ Light sensor works!");
  Serial.printf("   Light Level: %d\n", lightValue);
  
  // Test WiFi scan
  WiFi.mode(WIFI_STA);
  WiFi.disconnect();
  delay(100);
  
  int networks = WiFi.scanNetworks();
  if (networks > 0) {
    Serial.println("✅ WiFi works!");
    Serial.printf("   Networks found: %d\n", networks);
  } else {
    Serial.println("❌ WiFi scan failed");
  }
  
  // Display results on screen
  tft.fillScreen(TFT_BLACK);
  tft.setTextSize(1);
  tft.setCursor(10, 10);
  tft.setTextColor(TFT_GREEN);
  tft.println("Library Test Results:");
  tft.println("");
  tft.setTextColor(TFT_WHITE);
  tft.println("Display: OK");
  tft.println("Touch: OK");
  tft.printf("RFID: %s\n", (version != 0x00 && version != 0xFF) ? "OK" : "FAIL");
  tft.printf("SD Card: %s\n", SD.begin(SD_CS) ? "OK" : "FAIL");
  tft.println("SPIFFS: OK");
  tft.println("JSON: OK");
  tft.println("LED: OK");
  tft.println("Speaker: OK");
  tft.println("Light Sensor: OK");
  tft.printf("WiFi: %s\n", (networks > 0) ? "OK" : "FAIL");
  
  Serial.println("\n=== Test Complete ===");
  Serial.println("If all tests pass, you can proceed with the main firmware!");
  Serial.println("Touch the screen to test touch functionality...");
}

void loop() {
  // Test touch input
  if (touch.touched()) {
    TS_Point p = touch.getPoint();
    if (p.z > 600) {
      Serial.printf("Touch detected: X=%d, Y=%d, Z=%d\n", p.x, p.y, p.z);
      
      // Flash LED on touch
      leds[0] = CRGB::White;
      FastLED.show();
      delay(100);
      leds[0] = CRGB::Black;
      FastLED.show();
      
      // Beep on touch
      tone(SPEAKER_PIN, 800, 100);
      
      delay(500); // Debounce
    }
  }
  
  // Test RFID reading
  if (mfrc522.PICC_IsNewCardPresent() && mfrc522.PICC_ReadCardSerial()) {
    Serial.print("RFID Card detected: ");
    for (byte i = 0; i < mfrc522.uid.size; i++) {
      if (i > 0) Serial.print(":");
      if (mfrc522.uid.uidByte[i] < 0x10) Serial.print("0");
      Serial.print(mfrc522.uid.uidByte[i], HEX);
    }
    Serial.println();
    
    // Flash green for RFID detection
    leds[0] = CRGB::Green;
    FastLED.show();
    tone(SPEAKER_PIN, 1200, 300);
    delay(1000);
    leds[0] = CRGB::Black;
    FastLED.show();
    
    mfrc522.PICC_HaltA();
    mfrc522.PCD_StopCrypto1();
  }
  
  delay(100);
}
