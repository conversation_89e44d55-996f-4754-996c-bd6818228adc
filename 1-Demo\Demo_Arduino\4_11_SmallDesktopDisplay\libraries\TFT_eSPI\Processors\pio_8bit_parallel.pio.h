// -------------------------------------------------- //
// This file is autogenerated by pioasm; do not edit! //
// -------------------------------------------------- //

#pragma once

#if !PICO_NO_HARDWARE
#include "hardware/pio.h"
#endif

// ------ //
// tft_io //
// ------ //

#define tft_io_wrap_target 9
#define tft_io_wrap 27

#define tft_io_offset_block_fill 0u
#define tft_io_offset_start_tx 9u
#define tft_io_offset_start_8 14u
#define tft_io_offset_set_addr_window 17u

static const uint16_t tft_io_program_instructions[] = {
    0x98a0, //  0: pull   block           side 1     
    0xa027, //  1: mov    x, osr                     
    0x80a0, //  2: pull   block                      
    0xa047, //  3: mov    y, osr                     
    0xb8e1, //  4: mov    osr, x          side 1     
    0x7118, //  5: out    pins, 24        side 0 [1] 
    0xb942, //  6: nop                    side 1 [1] 
    0x7108, //  7: out    pins, 8         side 0 [1] 
    0x1884, //  8: jmp    y--, 4          side 1     
            //     .wrap_target
    0x98a0, //  9: pull   block           side 1     
    0x7118, // 10: out    pins, 24        side 0 [1] 
    0xb942, // 11: nop                    side 1 [1] 
    0x7108, // 12: out    pins, 8         side 0 [1] 
    0x1809, // 13: jmp    9               side 1     
    0x98a0, // 14: pull   block           side 1     
    0x7100, // 15: out    pins, 32        side 0 [1] 
    0x1809, // 16: jmp    9               side 1     
    0xf822, // 17: set    x, 2            side 1     
    0xe000, // 18: set    pins, 0                    
    0x80a0, // 19: pull   block                      
    0x7000, // 20: out    pins, 32        side 0     
    0x003a, // 21: jmp    !x, 26                     
    0x98a0, // 22: pull   block           side 1     
    0xe001, // 23: set    pins, 1                    
    0x7108, // 24: out    pins, 8         side 0 [1] 
    0x19f8, // 25: jmp    !osre, 24       side 1 [1] 
    0x1852, // 26: jmp    x--, 18         side 1     
    0xe001, // 27: set    pins, 1                    
            //     .wrap
};

#if !PICO_NO_HARDWARE
static const struct pio_program tft_io_program = {
    .instructions = tft_io_program_instructions,
    .length = 28,
    .origin = -1,
};

static inline pio_sm_config tft_io_program_get_default_config(uint offset) {
    pio_sm_config c = pio_get_default_sm_config();
    sm_config_set_wrap(&c, offset + tft_io_wrap_target, offset + tft_io_wrap);
    sm_config_set_sideset(&c, 2, true, false);
    return c;
}
#endif
