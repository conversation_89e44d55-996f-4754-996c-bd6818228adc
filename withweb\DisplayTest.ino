/*
 * Simple Display Test for ESP32-2432S028R (CYD)
 * Use this to test if your display is working
 */

#include <TFT_eSPI.h>

TFT_eSPI tft = TFT_eSPI();

void setup() {
  Serial.begin(115200);
  Serial.println("Starting display test...");
  
  // Initialize display
  tft.init();
  tft.setRotation(0); // Portrait mode
  
  // Turn on backlight
  pinMode(21, OUTPUT);
  digitalWrite(21, HIGH);
  
  // Clear screen with black
  tft.fillScreen(TFT_BLACK);
  
  // Test basic drawing
  Serial.println("Drawing test pattern...");
  
  // Draw colored rectangles
  tft.fillRect(0, 0, 240, 80, TFT_RED);
  tft.fillRect(0, 80, 240, 80, TFT_GREEN);
  tft.fillRect(0, 160, 240, 80, TFT_BLUE);
  tft.fillRect(0, 240, 240, 80, TFT_YELLOW);
  
  // Draw text
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("Display Test");
  
  tft.setCursor(10, 90);
  tft.println("ESP32-2432S028R");
  
  tft.setCursor(10, 170);
  tft.println("CYD Board");
  
  tft.setCursor(10, 250);
  tft.println("Working!");
  
  Serial.println("Display test complete!");
  Serial.println("You should see colored bars and text on the display");
}

void loop() {
  // Flash the backlight every 2 seconds to show it's working
  static unsigned long lastBlink = 0;
  static bool backlightOn = true;
  
  if (millis() - lastBlink > 2000) {
    backlightOn = !backlightOn;
    digitalWrite(21, backlightOn ? HIGH : LOW);
    lastBlink = millis();
    
    Serial.println(backlightOn ? "Backlight ON" : "Backlight OFF");
  }
}
