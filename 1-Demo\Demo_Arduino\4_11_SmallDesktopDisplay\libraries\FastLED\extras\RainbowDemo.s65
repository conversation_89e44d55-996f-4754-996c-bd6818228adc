; "Rainbow with glitter" demo
; for "FastLED6502"
;
; Runs on an Apple ][, ][+, //e, or //gs
;
; Supports APA102, Adafruit DotStar,
; LPD8806, and WS2801 LED strips.
;
; LED strip connects to game port pins,
; see FastLED6502.s65 for details.
;
; <PERSON>, February 2015

#define NUM_LEDS 	100
#define BRIGHTNESS 	64
#define CHIPSET  	APA102
#define DATA_PIN 	14 
#define CLOCK_PIN 	5
 
	* = $6000

#include "FastLED6502.s65"
#include "AppleII.s65"

gHue	  .byt 0
gHueDelta .byt 17
gHueSpeed .byt 7


Setup	
	lda #0
	sta gHue
	
Loop
	lda gHue
	clc
	adc gHueSpeed
	sta gHue
	ldx gHue
	ldy gHueDelta
; Fill RGB array with HSV rainbow
	jsr FastLED_FillRainbow_XY
; Use master brightness control
	lda #BRIGHTNESS
	sta FastLED_Brightness	
CheckOpenApple
	bit OpenApple
	bpl CheckSolidApple
; Add glitter if requested
	jsr AddGlitter		
CheckSolidApple
	bit SolidApple
	bpl DoDisplay
; Pulse brightness if requested
	jsr PulseBrightness
DoDisplay
; This is where the magic happens
	jsr FastLED_Show	
	jmp Loop


AddGlitter
	ldy #3
MaybeAdd1Glitter
	jsr FastLED_Random8
	cmp FastLED_NumPixels
	bcs SkipThis1Glitter
	tax
	lda #$FF
	sta ledsR,x
	sta ledsG,x
	sta ledsB,x
SkipThis1Glitter
	dey
	bne MaybeAdd1Glitter
	rts
	
	
PulseBrightness
	lda #13
	jsr FastLED_Beat8
	clc
	adc #12
	bcc PulseBright1
	lda #$FF
PulseBright1
	sta FastLED_Brightness
	rts
	