# Installation Guide
## RFID Employee Punch Clock System

This guide will walk you through the complete installation process for the RFID Punch Clock System on the ESP32-2432S028R (CYD) board.

## 📋 Prerequisites

### Hardware Required
- ESP32-2432S028R (CYD) board
- MFRC522 RFID reader module
- MicroSD card (Class 10, 8-32GB, formatted as FAT32)
- RFID cards/tags (13.56MHz, ISO14443A compatible)
- Jumper wires for connections
- USB-C cable for programming
- Computer with Arduino IDE

### Software Required
- Arduino IDE 2.0+ or 1.8.19+
- ESP32 board package
- Required libraries (listed below)

## 🔧 Step 1: Arduino IDE Setup

### 1.1 Install Arduino IDE
1. Download Arduino IDE from [arduino.cc](https://www.arduino.cc/en/software)
2. Install and launch Arduino IDE

### 1.2 Add ESP32 Board Support
1. Open Arduino IDE
2. Go to **File → Preferences**
3. Add this URL to "Additional Board Manager URLs":
   ```
   https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json
   ```
4. Go to **Tools → Board → Boards Manager**
5. Search for "ESP32" and install "esp32 by Espressif Systems"
6. Select **Tools → Board → ESP32 Arduino → ESP32 Dev Module**

### 1.3 Configure Board Settings
Set the following board configuration:
```
Board: ESP32 Dev Module
Upload Speed: 921600
CPU Frequency: 240MHz (WiFi/BT)
Flash Frequency: 80MHz
Flash Mode: QIO
Flash Size: 4MB (32Mb)
Partition Scheme: Default 4MB with spiffs (1.2MB APP/1.5MB SPIFFS)
Core Debug Level: None
PSRAM: Disabled
```

## 📚 Step 2: Install Required Libraries

### 2.1 Install via Library Manager
Go to **Tools → Manage Libraries** and install:

1. **TFT_eSPI** by Bodmer (v2.5.0+)
2. **XPT2046_Touchscreen** by Paul Stoffregen (v1.4.0+)
3. **MFRC522** by GithubCommunity (v1.4.10+)
4. **ArduinoJson** by Benoit Blanchon (v6.21.0+)
5. **ESPAsyncWebServer** by lacamera (v1.2.3+)
6. **NTPClient** by Fabrice Weinberg (v3.2.1+)
7. **FastLED** by Daniel Garcia (v3.5.0+)

### 2.2 Manual Library Installation (if needed)
If any library is not available via Library Manager:

1. Download the library ZIP file
2. Go to **Sketch → Include Library → Add .ZIP Library**
3. Select the downloaded ZIP file

## ⚙️ Step 3: Configure TFT_eSPI Library

### 3.1 Locate User_Setup.h
Find the TFT_eSPI library folder:
- **Windows**: `Documents\Arduino\libraries\TFT_eSPI\`
- **macOS**: `~/Documents/Arduino/libraries/TFT_eSPI/`
- **Linux**: `~/Arduino/libraries/TFT_eSPI/`

### 3.2 Edit User_Setup.h
Open `User_Setup.h` and replace the content with:

```cpp
#define USER_SETUP_ID 206

// Driver selection
#define ILI9341_DRIVER

// Display size
#define TFT_WIDTH  240
#define TFT_HEIGHT 320

// ESP32-2432S028R pin definitions
#define TFT_MISO 19
#define TFT_MOSI 23
#define TFT_SCLK 18
#define TFT_CS   15
#define TFT_DC    2
#define TFT_RST  -1  // Not connected
#define TFT_BL   21  // Backlight

// Touch screen
#define TOUCH_CS 33

// Font loading
#define LOAD_GLCD   // Font 1. Original Adafruit 8 pixel font needs ~1820 bytes in FLASH
#define LOAD_FONT2  // Font 2. Small 16 pixel high font, needs ~3534 bytes in FLASH, 96 characters
#define LOAD_FONT4  // Font 4. Medium 26 pixel high font, needs ~5848 bytes in FLASH, 96 characters
#define LOAD_FONT6  // Font 6. Large 48 pixel font, needs ~2666 bytes in FLASH, only characters 1234567890:-.apm
#define LOAD_FONT7  // Font 7. 7 segment 48 pixel font, needs ~2438 bytes in FLASH, only characters 1234567890:-.
#define LOAD_FONT8  // Font 8. Large 75 pixel font needs ~3256 bytes in FLASH, only characters 1234567890:-.
#define LOAD_GFXFF  // FreeFonts. Include access to the 48 Adafruit_GFX free fonts FF1 to FF48 and custom fonts

#define SMOOTH_FONT

// SPI frequency
#define SPI_FREQUENCY  40000000
#define SPI_READ_FREQUENCY  20000000
#define SPI_TOUCH_FREQUENCY  2500000
```

### 3.3 Save and Close
Save the file and restart Arduino IDE.

## 🔌 Step 4: Hardware Assembly

### 4.1 MFRC522 Wiring
Connect the MFRC522 module to the ESP32-2432S028R:

| MFRC522 Pin | ESP32-2432S028R Pin | Wire Color (suggested) |
|-------------|---------------------|------------------------|
| VCC         | 3.3V               | Red                    |
| GND         | GND                | Black                  |
| SDA         | GPIO 22            | Yellow                 |
| SCK         | GPIO 18            | Green                  |
| MOSI        | GPIO 23            | Blue                   |
| MISO        | GPIO 19            | Purple                 |
| RST         | GPIO 16            | Orange                 |
| IRQ         | Not connected      | -                      |

### 4.2 Connection Verification
1. Double-check all connections
2. Ensure VCC is connected to 3.3V (NOT 5V!)
3. Verify no loose connections
4. Check for short circuits

### 4.3 SD Card Preparation
1. Format MicroSD card as FAT32
2. Insert into the CYD board's SD slot
3. Ensure card is properly seated

## 💾 Step 5: Upload Firmware

### 5.1 Download Project Files
1. Download or clone the project files
2. Extract to a folder named `CYD_PunchClock`
3. Ensure all `.cpp` and `.h` files are present

### 5.2 Open Project
1. Open `CYD_PunchClock.ino` in Arduino IDE
2. Verify all tabs are loaded (should see multiple files)

### 5.3 Compile and Upload
1. Connect ESP32-2432S028R via USB-C
2. Select the correct COM port in **Tools → Port**
3. Click **Upload** button (→)
4. Wait for compilation and upload to complete

### 5.4 Monitor Serial Output
1. Open **Tools → Serial Monitor**
2. Set baud rate to **115200**
3. Watch for initialization messages

## 🎯 Step 6: Initial Setup

### 6.1 First Boot
1. Power on the device
2. Watch for welcome screen
3. Follow touch calibration prompts
4. Tap corners as instructed

### 6.2 WiFi Configuration
1. Navigate to Settings → WiFi
2. Select your network
3. Enter WiFi password
4. Wait for connection confirmation

### 6.3 Time Setup
1. Go to Settings → Time
2. Select your timezone
3. Enable NTP synchronization
4. Verify correct time display

### 6.4 Admin Setup
1. Set admin PIN (default: 1234)
2. Configure device name
3. Enable/disable features as needed

## 👥 Step 7: Employee Setup

### 7.1 Add First Employee
1. Access Admin Panel (long press admin button + PIN)
2. Select "Add Employee"
3. Enter employee details:
   - Name
   - Department
   - Shift times
   - Access level

### 7.2 Register RFID Card
1. In employee details, select "Register RFID"
2. Hold RFID card near reader
3. Confirm card registration
4. Test punch in/out functionality

### 7.3 Test System
1. Hold registered RFID card near reader
2. Verify audio feedback
3. Check LED status indication
4. Confirm attendance logging

## 🌐 Step 8: Web Interface Setup

### 8.1 Access Web Interface
1. Note device IP address from display
2. Open web browser
3. Navigate to `http://[device-ip]/`
4. Bookmark for easy access

### 8.2 Web Features Test
1. View dashboard
2. Check employee list
3. Test report generation
4. Verify system status

## 🔧 Step 9: Troubleshooting

### 9.1 Common Issues

#### Upload Fails
- Check USB cable connection
- Try different USB port
- Press and hold BOOT button during upload
- Verify correct board selection

#### Display Not Working
- Check TFT_eSPI configuration
- Verify pin connections
- Test with simple TFT example

#### RFID Not Reading
- Verify 3.3V power (not 5V!)
- Check SPI wiring
- Test with MFRC522 example code
- Ensure RFID cards are compatible (13.56MHz)

#### Touch Not Responding
- Recalibrate touch screen
- Check touch controller wiring
- Clean screen surface

#### WiFi Connection Issues
- Verify 2.4GHz network (5GHz not supported)
- Check credentials
- Ensure strong signal
- Try mobile hotspot for testing

### 9.2 Diagnostic Tools

#### Serial Monitor Commands
Enable debug mode in `config.h`:
```cpp
#define DEBUG 1
```

#### Component Testing
Access diagnostic menu via admin panel:
- RFID self-test
- Display test patterns
- Touch calibration
- Audio test
- LED color cycle

## ✅ Step 10: Verification Checklist

### Hardware Verification
- [ ] All connections secure
- [ ] RFID module powered by 3.3V
- [ ] SD card inserted and recognized
- [ ] Display shows content clearly
- [ ] Touch responds accurately
- [ ] Audio plays correctly
- [ ] LED changes colors

### Software Verification
- [ ] Firmware uploads successfully
- [ ] Serial monitor shows initialization
- [ ] WiFi connects to network
- [ ] Time synchronizes with NTP
- [ ] RFID cards read successfully
- [ ] Attendance logs to SD card
- [ ] Web interface accessible

### Functional Verification
- [ ] Employee punch in/out works
- [ ] Audio feedback plays
- [ ] LED status indicates correctly
- [ ] Admin panel accessible
- [ ] Reports generate properly
- [ ] Data persists after reboot

## 🎉 Congratulations!

Your RFID Employee Punch Clock System is now installed and ready for use!

## 📞 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Review serial monitor output
3. Verify hardware connections
4. Consult the main README.md
5. Create an issue on GitHub

## 📚 Next Steps

- Read the User Manual for daily operations
- Explore advanced configuration options
- Set up regular data backups
- Plan employee training sessions
- Consider additional features from the roadmap
