version: 6.17.2.{build}
environment:
  matrix:
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
    CMAKE_GENERATOR: Visual Studio 16 2019
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
    CMAKE_GENERATOR: Visual Studio 15 2017
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
    CMAKE_GENERATOR: Visual Studio 14 2015
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2013
    CMAKE_GENERATOR: Visual Studio 12 2013
  - CMAKE_GENERATOR: Visual Studio 11 2012
  - CMAKE_GENERATOR: Visual Studio 10 2010
  - CMAKE_GENERATOR: MinGW Makefiles
configuration: Debug
before_build:
- set PATH=C:\MinGW\bin;%PATH:C:\Program Files\Git\usr\bin;=% # Workaround for CMake not wanting sh.exe on PATH for MinGW
- cmake -DCMAKE_BUILD_TYPE=%CONFIGURATION% -G "%CMAKE_GENERATOR%" .
build_script:
- cmake --build . --config %CONFIGURATION%
test_script:
- ctest -C %CONFIGURATION% --output-on-failure .
