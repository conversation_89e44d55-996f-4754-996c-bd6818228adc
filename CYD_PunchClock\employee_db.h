/*
 * Employee Database Header
 * Manages employee data and operations
 */

#ifndef EMPLOYEE_DB_H
#define EMPLOYEE_DB_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <SD.h>
#include <FS.h>
#include "config.h"
#include "data_manager.h"

class EmployeeDB {
private:
  std::vector<Employee> employees;
  DataManager* dataManager;
  bool initialized;
  bool dataLoaded;
  
  // Statistics
  int totalEmployees;
  int activeEmployees;
  unsigned long lastSave;
  
  // Private methods
  bool loadFromFile();
  bool saveToFile();
  int getNextEmployeeId();
  bool validateEmployee(const Employee& emp);
  void sortEmployees();
  
public:
  EmployeeDB();
  ~EmployeeDB();
  
  // Initialization
  bool begin();
  bool begin(DataManager* dm);
  void reset();
  bool isInitialized() const { return initialized; }
  
  // Employee management
  bool addEmployee(const Employee& employee);
  bool updateEmployee(const Employee& employee);
  bool deleteEmployee(int employeeId);
  bool activateEmployee(int employeeId);
  bool deactivateEmployee(int employeeId);
  
  // Employee lookup
  Employee* findById(int employeeId);
  Employee* findByRFID(const String& rfidUID);
  Employee* findByName(const String& name);
  std::vector<Employee*> findByDepartment(const String& department);
  
  // Employee queries
  std::vector<Employee> getAllEmployees();
  std::vector<Employee> getActiveEmployees();
  std::vector<Employee> getInactiveEmployees();
  int getEmployeeCount() const { return totalEmployees; }
  int getActiveEmployeeCount() const { return activeEmployees; }
  
  // RFID management
  bool isRFIDRegistered(const String& rfidUID);
  bool registerRFID(int employeeId, const String& rfidUID);
  bool unregisterRFID(int employeeId);
  String getRFIDByEmployeeId(int employeeId);
  
  // Department management
  std::vector<String> getAllDepartments();
  int getDepartmentEmployeeCount(const String& department);
  
  // Validation
  bool validateRFID(const String& rfidUID);
  bool validateEmployeeName(const String& name);
  bool validateDepartment(const String& department);
  bool isEmployeeIdUnique(int id);
  bool isRFIDUnique(const String& rfidUID);
  
  // Data operations
  bool save();
  bool load();
  bool backup();
  bool restore(const String& backupFile);
  bool exportCSV(const String& filename);
  bool importCSV(const String& filename);
  
  // Statistics
  void updateStatistics();
  int getTotalEmployees() const { return totalEmployees; }
  int getActiveEmployees() const { return activeEmployees; }
  unsigned long getLastSaveTime() const { return lastSave; }
  
  // Utility functions
  String generateEmployeeReport();
  void printEmployeeList();
  void printEmployeeDetails(int employeeId);
  
  // Search and filter
  std::vector<Employee*> searchEmployees(const String& query);
  std::vector<Employee*> filterByStatus(bool active);
  std::vector<Employee*> filterByAccessLevel(int minLevel);
  
  // Bulk operations
  bool addMultipleEmployees(const std::vector<Employee>& employeeList);
  bool updateMultipleEmployees(const std::vector<Employee>& employeeList);
  bool deleteMultipleEmployees(const std::vector<int>& employeeIds);
  
  // Error handling
  String getLastError() const;
  bool hasError() const;
  void clearError();
};

#endif // EMPLOYEE_DB_H
