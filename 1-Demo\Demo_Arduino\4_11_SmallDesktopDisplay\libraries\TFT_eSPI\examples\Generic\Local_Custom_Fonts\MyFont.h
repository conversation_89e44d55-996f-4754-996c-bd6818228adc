// If you follow the tutorial here:
// https://www.youtube.com/watch?v=L8MmTISmwZ8
// do not forget the names must be edited to remove the Windows file path.

// I had to edit lines 8, 3135, 3361-3363 in this file


const uint8_t myFont32pt8bBitmaps[] PROGMEM = {
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0xBF, 0xCF, 0xE7, 0xF3,
  0xF9, 0xFC, 0xFE, 0x7F, 0x3F, 0x9F, 0x8F, 0xC7, 0xE1, 0xF0, 0xF8, 0x7C,
  0x3E, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF8, 0xFF, 0xC3, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xC3, 0xFF,
  0xFF, 0xC3, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xC3, 0xFF,
  0xFF, 0xC3, 0xFF, 0x7F, 0xC3, 0xFF, 0x7F, 0xC1, 0xFE, 0x7F, 0x81, 0xFE,
  0x7F, 0x81, 0xFE, 0x7F, 0x81, 0xFE, 0x3F, 0x81, 0xFE, 0x3F, 0x80, 0xFE,
  0x3F, 0x80, 0xFC, 0x00, 0x1F, 0xC0, 0xFE, 0x00, 0x1F, 0xC0, 0x7F, 0x00,
  0x0F, 0xE0, 0x3F, 0x80, 0x07, 0xF0, 0x1F, 0xC0, 0x03, 0xF8, 0x1F, 0xE0,
  0x01, 0xFC, 0x0F, 0xE0, 0x01, 0xFC, 0x07, 0xF0, 0x00, 0xFE, 0x03, 0xF8,
  0x00, 0x7F, 0x01, 0xFC, 0x00, 0x3F, 0x81, 0xFE, 0x00, 0x1F, 0x80, 0xFE,
  0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x7F, 0x01, 0xFC, 0x00, 0x3F, 0x81,
  0xFC, 0x00, 0x3F, 0x80, 0xFE, 0x00, 0x1F, 0xC0, 0x7F, 0x00, 0x0F, 0xE0,
  0x3F, 0x80, 0x07, 0xF0, 0x1F, 0xC0, 0x03, 0xF8, 0x1F, 0xC0, 0x03, 0xF8,
  0x0F, 0xE0, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3,
  0xF0, 0x1F, 0xC0, 0x03, 0xF8, 0x0F, 0xE0, 0x01, 0xFC, 0x07, 0xF0, 0x00,
  0xFE, 0x03, 0xF8, 0x00, 0x7F, 0x03, 0xF8, 0x00, 0x7F, 0x01, 0xFC, 0x00,
  0x3F, 0x80, 0xFE, 0x00, 0x1F, 0xC0, 0x7F, 0x00, 0x0F, 0xE0, 0x3F, 0x80,
  0x07, 0xF0, 0x3F, 0x80, 0x07, 0xF0, 0x1F, 0xC0, 0x00, 0x00, 0x07, 0xC0,
  0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x0F, 0xFC, 0x00,
  0x00, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xF8, 0x03,
  0xFF, 0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xC3, 0xFF,
  0x7F, 0xFF, 0x0F, 0xF9, 0xF3, 0xFC, 0x3F, 0xC7, 0xCF, 0xF9, 0xFE, 0x1F,
  0x1F, 0xE7, 0xF8, 0x7C, 0x78, 0x1F, 0xE1, 0xF0, 0x00, 0x7F, 0x87, 0xC0,
  0x00, 0xFF, 0x1F, 0x00, 0x03, 0xFC, 0x7C, 0x00, 0x0F, 0xFD, 0xF0, 0x00,
  0x3F, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x03,
  0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0x00, 0x0F,
  0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x1F,
  0xFF, 0xE0, 0x00, 0x7D, 0xFF, 0xC0, 0x01, 0xF1, 0xFF, 0x00, 0x07, 0xC3,
  0xFC, 0x00, 0x1F, 0x0F, 0xF0, 0xF0, 0x7C, 0x1F, 0xFF, 0xE1, 0xF0, 0x7F,
  0xFF, 0x87, 0xC1, 0xFD, 0xFE, 0x1F, 0x0F, 0xF7, 0xFC, 0x7C, 0x3F, 0xDF,
  0xF9, 0xF1, 0xFF, 0x3F, 0xF7, 0xCF, 0xF8, 0xFF, 0xFF, 0xFF, 0xE1, 0xFF,
  0xFF, 0xFF, 0x07, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF,
  0xFE, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x1F, 0xF0,
  0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x01, 0xF0, 0x00,
  0x00, 0x07, 0xC0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x00,
  0x03, 0xF8, 0x00, 0x00, 0x3F, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x1F, 0x80,
  0x01, 0xFF, 0xF8, 0x00, 0x07, 0xE0, 0x00, 0xFF, 0xFE, 0x00, 0x03, 0xF0,
  0x00, 0x7F, 0xFF, 0xC0, 0x00, 0xFC, 0x00, 0x1F, 0xC7, 0xF8, 0x00, 0x7E,
  0x00, 0x0F, 0xE0, 0xFE, 0x00, 0x1F, 0x80, 0x03, 0xF8, 0x3F, 0x80, 0x0F,
  0xC0, 0x00, 0xFE, 0x07, 0xE0, 0x03, 0xF0, 0x00, 0x3F, 0x81, 0xFC, 0x01,
  0xF8, 0x00, 0x0F, 0xE0, 0x7F, 0x00, 0x7E, 0x00, 0x03, 0xF0, 0x1F, 0xC0,
  0x3F, 0x00, 0x00, 0xFC, 0x07, 0xF0, 0x0F, 0xC0, 0x00, 0x3F, 0x01, 0xFC,
  0x07, 0xE0, 0x00, 0x0F, 0xE0, 0x7F, 0x01, 0xF8, 0x00, 0x03, 0xF8, 0x1F,
  0x80, 0xFC, 0x00, 0x00, 0xFE, 0x0F, 0xE0, 0x3F, 0x00, 0x00, 0x3F, 0x83,
  0xF8, 0x1F, 0x80, 0x00, 0x07, 0xF1, 0xFE, 0x07, 0xE0, 0x00, 0x01, 0xFF,
  0xFF, 0x03, 0xF0, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0xFC, 0x00, 0x00, 0x07,
  0xFF, 0xE0, 0x7E, 0x00, 0x00, 0x00, 0xFF, 0xE0, 0x1F, 0x80, 0x00, 0x00,
  0x0F, 0xE0, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x03, 0xF0, 0x07, 0xF0,
  0x00, 0x00, 0x01, 0xF8, 0x07, 0xFF, 0x00, 0x00, 0x00, 0x7E, 0x07, 0xFF,
  0xE0, 0x00, 0x00, 0x3F, 0x03, 0xFF, 0xFC, 0x00, 0x00, 0x0F, 0xC0, 0xFF,
  0xFF, 0x00, 0x00, 0x07, 0xE0, 0x7F, 0x8F, 0xE0, 0x00, 0x01, 0xF8, 0x1F,
  0xC3, 0xF8, 0x00, 0x00, 0xFC, 0x07, 0xE0, 0x7F, 0x00, 0x00, 0x3F, 0x03,
  0xF8, 0x1F, 0xC0, 0x00, 0x1F, 0x80, 0xFE, 0x07, 0xF0, 0x00, 0x07, 0xE0,
  0x3F, 0x81, 0xFC, 0x00, 0x03, 0xF0, 0x0F, 0xE0, 0x7F, 0x00, 0x00, 0xFC,
  0x03, 0xF8, 0x1F, 0xC0, 0x00, 0x7E, 0x00, 0xFE, 0x07, 0xF0, 0x00, 0x1F,
  0x80, 0x3F, 0x81, 0xFC, 0x00, 0x0F, 0xC0, 0x0F, 0xE0, 0x7F, 0x00, 0x03,
  0xF0, 0x01, 0xF8, 0x1F, 0xC0, 0x01, 0xF8, 0x00, 0x7F, 0x0F, 0xE0, 0x00,
  0x7E, 0x00, 0x1F, 0xC3, 0xF8, 0x00, 0x3F, 0x00, 0x03, 0xFF, 0xFE, 0x00,
  0x0F, 0xC0, 0x00, 0xFF, 0xFF, 0x00, 0x07, 0xE0, 0x00, 0x1F, 0xFF, 0x80,
  0x01, 0xF8, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0xFC, 0x00, 0x00, 0x1F, 0xC0,
  0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x1F,
  0xFF, 0xF8, 0x00, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x00, 0x1F, 0xFF, 0xFF,
  0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xC0, 0x00, 0x0F, 0xFF, 0xFF, 0xF0, 0x00,
  0x07, 0xFE, 0x0F, 0xF8, 0x00, 0x07, 0xFE, 0x03, 0xFC, 0x00, 0x03, 0xFE,
  0x00, 0xFE, 0x00, 0x01, 0xFF, 0x00, 0x7F, 0x00, 0x00, 0x7F, 0x80, 0x3F,
  0x80, 0x00, 0x3F, 0xE0, 0x3F, 0xC0, 0x00, 0x1F, 0xF0, 0x1F, 0xE0, 0x00,
  0x0F, 0xFC, 0x3F, 0xE0, 0x00, 0x03, 0xFF, 0x3F, 0xF0, 0x00, 0x00, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x00, 0x1F, 0xFF, 0xF0,
  0x00, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xE0, 0x00, 0x00,
  0x0F, 0xFF, 0xE0, 0x00, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x00, 0x0F, 0xFF,
  0xFE, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0x82, 0x00, 0x0F, 0xFE, 0xFF, 0xE1,
  0xF8, 0x0F, 0xFE, 0x3F, 0xF0, 0xFF, 0x0F, 0xFC, 0x1F, 0xFC, 0xFF, 0x87,
  0xFC, 0x07, 0xFF, 0x7F, 0x87, 0xFE, 0x01, 0xFF, 0xFF, 0xC3, 0xFE, 0x00,
  0x7F, 0xFF, 0xE1, 0xFF, 0x00, 0x1F, 0xFF, 0xE0, 0xFF, 0x80, 0x07, 0xFF,
  0xF0, 0x7F, 0xC0, 0x03, 0xFF, 0xF0, 0x3F, 0xE0, 0x00, 0xFF, 0xF8, 0x1F,
  0xF0, 0x00, 0x3F, 0xFC, 0x0F, 0xFC, 0x00, 0x3F, 0xFF, 0x07, 0xFF, 0x00,
  0x3F, 0xFF, 0xE1, 0xFF, 0xC0, 0x7F, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0x87,
  0xFF, 0xFF, 0xFC, 0xFF, 0xC1, 0xFF, 0xFF, 0xFC, 0x3F, 0xC0, 0x3F, 0xFF,
  0xF8, 0x0F, 0xC0, 0x0F, 0xFF, 0xF0, 0x01, 0xC0, 0x00, 0x7F, 0xC0, 0x00,
  0x60, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xEF, 0xF7, 0xF3, 0xF9, 0xFC, 0xFE, 0x00, 0x3F, 0x00, 0x7E, 0x00,
  0xFE, 0x00, 0xFC, 0x01, 0xFC, 0x01, 0xFC, 0x03, 0xF8, 0x03, 0xF8, 0x07,
  0xF0, 0x07, 0xF0, 0x0F, 0xF0, 0x0F, 0xE0, 0x1F, 0xE0, 0x1F, 0xE0, 0x1F,
  0xC0, 0x3F, 0xC0, 0x3F, 0xC0, 0x3F, 0xC0, 0x7F, 0xC0, 0x7F, 0x80, 0x7F,
  0x80, 0x7F, 0x80, 0x7F, 0x80, 0x7F, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0x7F, 0x80, 0x7F, 0x80, 0x7F, 0x80, 0x7F, 0x80, 0x7F, 0x80, 0x7F,
  0x80, 0x3F, 0xC0, 0x3F, 0xC0, 0x3F, 0xC0, 0x3F, 0xC0, 0x1F, 0xC0, 0x1F,
  0xE0, 0x1F, 0xE0, 0x0F, 0xE0, 0x0F, 0xE0, 0x07, 0xF0, 0x07, 0xF0, 0x03,
  0xF8, 0x03, 0xF8, 0x01, 0xF8, 0x01, 0xFC, 0x00, 0xFC, 0x00, 0xFE, 0x00,
  0x7E, 0x00, 0x3F, 0xFC, 0x00, 0x7E, 0x00, 0x7F, 0x00, 0x3F, 0x00, 0x3F,
  0x80, 0x3F, 0x80, 0x1F, 0xC0, 0x1F, 0xC0, 0x0F, 0xE0, 0x0F, 0xE0, 0x0F,
  0xF0, 0x07, 0xF0, 0x07, 0xF8, 0x07, 0xF8, 0x03, 0xF8, 0x03, 0xFC, 0x03,
  0xFC, 0x03, 0xFC, 0x03, 0xFC, 0x01, 0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x01,
  0xFE, 0x01, 0xFE, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFE, 0x01,
  0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x03, 0xFC, 0x03,
  0xFC, 0x03, 0xFC, 0x03, 0xFC, 0x03, 0xF8, 0x07, 0xF8, 0x07, 0xF8, 0x07,
  0xF0, 0x0F, 0xF0, 0x0F, 0xE0, 0x0F, 0xE0, 0x1F, 0xC0, 0x1F, 0xC0, 0x1F,
  0x80, 0x3F, 0x80, 0x3F, 0x00, 0x7F, 0x00, 0x7E, 0x00, 0xFC, 0x00, 0x00,
  0xFC, 0x00, 0x03, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x3E, 0x00, 0x00, 0xF8,
  0x01, 0x81, 0xE0, 0x67, 0xC7, 0x87, 0x9F, 0xDE, 0x7E, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0x00, 0x3F, 0x00, 0x01, 0xFE, 0x00, 0x0F,
  0xFC, 0x00, 0x7F, 0xF8, 0x03, 0xF3, 0xF0, 0x1F, 0x8F, 0xE0, 0xFE, 0x1F,
  0xC1, 0xF0, 0x3C, 0x01, 0xC0, 0xE0, 0x02, 0x01, 0x00, 0x00, 0x1F, 0xE0,
  0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF, 0x00,
  0x00, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00,
  0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF0, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x07,
  0xF8, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3F,
  0xC0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x01, 0xFE,
  0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x87,
  0xC3, 0xE1, 0xF0, 0xF0, 0xF9, 0xF9, 0xFC, 0xFC, 0x38, 0x18, 0x00, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0x00, 0x1F, 0x80, 0x07, 0xE0, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x3F,
  0x00, 0x0F, 0xC0, 0x07, 0xF0, 0x01, 0xFC, 0x00, 0x7E, 0x00, 0x1F, 0x80,
  0x0F, 0xE0, 0x03, 0xF8, 0x00, 0xFC, 0x00, 0x3F, 0x00, 0x1F, 0xC0, 0x07,
  0xF0, 0x01, 0xF8, 0x00, 0x7E, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x03, 0xF0,
  0x00, 0xFC, 0x00, 0x7F, 0x00, 0x1F, 0xC0, 0x07, 0xE0, 0x01, 0xF8, 0x00,
  0xFE, 0x00, 0x3F, 0x80, 0x0F, 0xC0, 0x03, 0xF0, 0x01, 0xFC, 0x00, 0x7F,
  0x00, 0x1F, 0x80, 0x07, 0xE0, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x3F, 0x00,
  0x0F, 0xC0, 0x03, 0xF0, 0x01, 0xFC, 0x00, 0x7E, 0x00, 0x1F, 0x80, 0x07,
  0xE0, 0x03, 0xF8, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x07,
  0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0xFF, 0xFF,
  0xF0, 0x0F, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0x07, 0xFF, 0xFF, 0xF8,
  0x3F, 0xF0, 0xFF, 0xE3, 0xFF, 0x01, 0xFF, 0x1F, 0xF0, 0x0F, 0xF8, 0xFF,
  0x80, 0x3F, 0xE7, 0xFC, 0x01, 0xFF, 0x7F, 0xC0, 0x0F, 0xFB, 0xFE, 0x00,
  0x7F, 0xDF, 0xF0, 0x01, 0xFE, 0xFF, 0x80, 0x0F, 0xFF, 0xFC, 0x00, 0x7F,
  0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0xFF, 0xFF,
  0xC0, 0x07, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0x80,
  0x0F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0x00, 0x1F,
  0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xC0, 0x07, 0xFF, 0xFE, 0x00, 0x3F, 0xDF,
  0xF0, 0x03, 0xFE, 0xFF, 0x80, 0x1F, 0xF3, 0xFE, 0x00, 0xFF, 0x9F, 0xF0,
  0x07, 0xFC, 0xFF, 0x80, 0x7F, 0xC7, 0xFE, 0x03, 0xFE, 0x1F, 0xF8, 0x7F,
  0xF0, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF, 0xFF, 0x80,
  0x7F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xC0, 0x07, 0xFF, 0xF8, 0x00, 0x0F,
  0xFF, 0x80, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x07, 0xF0, 0x00, 0xFF, 0x00,
  0x0F, 0xF0, 0x01, 0xFF, 0x00, 0x3F, 0xF0, 0x07, 0xFF, 0x00, 0xFF, 0xF0,
  0x1F, 0xFF, 0x07, 0xFF, 0xF1, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0x9F, 0xFF, 0xF1, 0xFF, 0xFE, 0x1F,
  0xFF, 0x81, 0xFF, 0xE0, 0x1F, 0xF8, 0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x01,
  0xFF, 0x00, 0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x01, 0xFF, 0x00,
  0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x1F, 0xF0,
  0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x01, 0xFF,
  0x00, 0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x1F,
  0xF0, 0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x00,
  0x1F, 0xF0, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xF8, 0x01, 0xFF,
  0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF,
  0xFF, 0x0F, 0xFF, 0xFF, 0xFE, 0x3F, 0xF8, 0x3F, 0xF9, 0xFF, 0x80, 0x3F,
  0xF7, 0xFC, 0x00, 0xFF, 0xDF, 0xF0, 0x01, 0xFF, 0x7F, 0xC0, 0x07, 0xFD,
  0xFE, 0x00, 0x1F, 0xF0, 0xF8, 0x00, 0x7F, 0xC0, 0x00, 0x01, 0xFE, 0x00,
  0x00, 0x07, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x0F,
  0xFC, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x1F, 0xF8,
  0x00, 0x00, 0xFF, 0xC0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x3F, 0xF0, 0x00,
  0x01, 0xFF, 0x80, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x03,
  0xFF, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x07, 0xFE,
  0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xF7, 0xFF, 0xFF,
  0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC,
  0x00, 0x1F, 0xE0, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xE0, 0x01,
  0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0x01, 0xFF,
  0xFF, 0xFE, 0x07, 0xFF, 0xFF, 0xF8, 0x3F, 0xF0, 0x7F, 0xF0, 0xFF, 0x80,
  0xFF, 0xC7, 0xFC, 0x01, 0xFF, 0x1F, 0xF0, 0x03, 0xFC, 0x7F, 0x80, 0x0F,
  0xF0, 0x0E, 0x00, 0x3F, 0xC0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x07, 0xFC,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x03, 0xFF, 0x80, 0x00, 0xFF, 0xFC, 0x00,
  0x03, 0xFF, 0xE0, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00,
  0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x40,
  0xFF, 0xC0, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x07,
  0xFC, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x7F, 0xC1, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFD, 0xFE, 0x00, 0x1F, 0xF7, 0xFC, 0x00, 0x7F, 0xDF,
  0xF0, 0x03, 0xFF, 0x7F, 0xE0, 0x1F, 0xFC, 0xFF, 0xE0, 0xFF, 0xE3, 0xFF,
  0xFF, 0xFF, 0x87, 0xFF, 0xFF, 0xFC, 0x0F, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF,
  0xFF, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0xFF, 0xFC,
  0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x0F,
  0xF0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x0F,
  0xFE, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x80, 0x00, 0x07,
  0xFF, 0xC0, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x03,
  0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x01,
  0xFE, 0xFF, 0x00, 0x01, 0xFE, 0x7F, 0x80, 0x00, 0xFF, 0x3F, 0xC0, 0x00,
  0xFF, 0x1F, 0xE0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x07, 0xF8, 0x00,
  0x7F, 0x83, 0xFC, 0x00, 0x7F, 0x81, 0xFE, 0x00, 0x7F, 0x80, 0xFF, 0x00,
  0x7F, 0xC0, 0x7F, 0x80, 0x3F, 0xC0, 0x3F, 0xC0, 0x3F, 0xC0, 0x1F, 0xE0,
  0x3F, 0xC0, 0x0F, 0xF0, 0x1F, 0xE0, 0x07, 0xF8, 0x1F, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x01,
  0xFE, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00,
  0x3F, 0xC0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x07, 0xF8, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x1F,
  0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0x01, 0xFF, 0xFF, 0xFC, 0x0F, 0xFF,
  0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF,
  0xFC, 0x0F, 0xFF, 0xFF, 0xF0, 0x3F, 0xC0, 0x00, 0x01, 0xFF, 0x00, 0x00,
  0x07, 0xF8, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x01,
  0xFE, 0x00, 0x00, 0x07, 0xF9, 0xFC, 0x00, 0x3F, 0xFF, 0xFE, 0x00, 0xFF,
  0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xFC, 0x0F, 0xFF, 0xFF, 0xF8, 0x3F, 0xFF,
  0xFF, 0xE1, 0xFF, 0xFF, 0xFF, 0xC7, 0xFF, 0xFF, 0xFF, 0x9F, 0xFC, 0x1F,
  0xFE, 0x7F, 0xC0, 0x1F, 0xF8, 0x3C, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x7F,
  0xC0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x1F, 0xF0,
  0xF0, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE,
  0x00, 0x1F, 0xF7, 0xFC, 0x00, 0xFF, 0x9F, 0xF8, 0x07, 0xFE, 0x7F, 0xF0,
  0x7F, 0xF8, 0xFF, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xFE, 0x07, 0xFF, 0xFF,
  0xF8, 0x0F, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xF0,
  0x00, 0x3F, 0xFF, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF0, 0x00,
  0x01, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xF0, 0x00, 0xFF, 0xFF, 0xE0, 0x07,
  0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0x81, 0xFF, 0xFF, 0xFE, 0x07, 0xFF,
  0xFF, 0xFC, 0x3F, 0xF8, 0x3F, 0xF0, 0xFF, 0xC0, 0x7F, 0xC7, 0xFE, 0x00,
  0xFF, 0x9F, 0xF0, 0x03, 0xFE, 0x7F, 0xC0, 0x07, 0x81, 0xFF, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03,
  0xFE, 0x1F, 0xC0, 0x0F, 0xF9, 0xFF, 0xE0, 0x3F, 0xEF, 0xFF, 0xC0, 0xFF,
  0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF,
  0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xF3, 0xFF, 0xE0, 0xFF, 0xEF, 0xFE, 0x01,
  0xFF, 0xBF, 0xF0, 0x03, 0xFE, 0xFF, 0xC0, 0x07, 0xFF, 0xFE, 0x00, 0x1F,
  0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0x7F, 0x80, 0x07, 0xFD,
  0xFF, 0x00, 0x1F, 0xF7, 0xFC, 0x00, 0x7F, 0xDF, 0xF8, 0x01, 0xFE, 0x3F,
  0xF0, 0x0F, 0xF8, 0xFF, 0xE0, 0xFF, 0xE1, 0xFF, 0xFF, 0xFF, 0x07, 0xFF,
  0xFF, 0xFC, 0x0F, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0x00, 0x3F, 0xFF,
  0xF8, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x7F, 0x80,
  0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x07, 0xF8,
  0x00, 0x00, 0x7F, 0x80, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x03, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F,
  0xE0, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x7F, 0x80,
  0x00, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x01, 0xFE, 0x00, 0x00,
  0x1F, 0xE0, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x7F,
  0x80, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFF, 0x00,
  0x00, 0x0F, 0xF0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x03, 0xFC, 0x00, 0x00,
  0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x7F,
  0x80, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0xFF, 0x00,
  0x00, 0x07, 0xF8, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x0F, 0xFF, 0xC0,
  0x01, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xF8, 0x1F,
  0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0x8F, 0xFE, 0x0F, 0xFC, 0x7F, 0xC0,
  0x3F, 0xE3, 0xFE, 0x00, 0xFF, 0x9F, 0xE0, 0x07, 0xFC, 0xFF, 0x00, 0x1F,
  0xE7, 0xF8, 0x00, 0xFF, 0x3F, 0xC0, 0x07, 0xF9, 0xFE, 0x00, 0x7F, 0x8F,
  0xF8, 0x03, 0xFC, 0x3F, 0xC0, 0x3F, 0xE1, 0xFF, 0x83, 0xFE, 0x07, 0xFF,
  0xFF, 0xE0, 0x1F, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFE,
  0x00, 0x1F, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0xC1,
  0xFF, 0x83, 0xFF, 0x1F, 0xF0, 0x0F, 0xFC, 0xFF, 0x00, 0x3F, 0xEF, 0xF8,
  0x00, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xE0, 0x00,
  0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xC0, 0x03, 0xFF,
  0xFF, 0x00, 0x1F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xE0, 0x0F, 0xF9, 0xFF,
  0xC1, 0xFF, 0xCF, 0xFF, 0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF,
  0xFE, 0x03, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xC0,
  0x00, 0x1F, 0xF0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x03, 0xFF, 0xE0, 0x00,
  0x3F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0x80, 0x7F,
  0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xF8, 0x7F, 0xF0,
  0x7F, 0xF1, 0xFF, 0x00, 0xFF, 0xCF, 0xF8, 0x01, 0xFF, 0x3F, 0xE0, 0x03,
  0xFE, 0xFF, 0x80, 0x0F, 0xFB, 0xFE, 0x00, 0x3F, 0xEF, 0xF0, 0x00, 0xFF,
  0xBF, 0xE0, 0x03, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xFE, 0x00, 0x3F, 0xFF,
  0xF8, 0x01, 0xFF, 0xDF, 0xF0, 0x07, 0xFF, 0x7F, 0xF0, 0x7F, 0xFD, 0xFF,
  0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0xFF, 0xC7, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF,
  0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0x7F, 0xC0, 0x7F, 0xF9,
  0xFF, 0x00, 0x7F, 0x07, 0xFC, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x7F,
  0xC0, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x1E, 0x00, 0x3F, 0xE7,
  0xFC, 0x01, 0xFF, 0x9F, 0xF0, 0x07, 0xFC, 0x7F, 0xE0, 0x3F, 0xF0, 0xFF,
  0xC3, 0xFF, 0x83, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF,
  0xFF, 0xC0, 0x3F, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0xFF, 0xFF,
  0x00, 0x01, 0xFF, 0xF8, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x7F,
  0xDF, 0xF7, 0xFD, 0xFF, 0x7F, 0xDF, 0xF7, 0xFD, 0xFF, 0x7F, 0xC0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xDF, 0xF7, 0xFD, 0xFF, 0x7F, 0xDF,
  0xF7, 0xFD, 0xFF, 0x7F, 0xC1, 0xE0, 0x78, 0x1E, 0x0F, 0x87, 0xC3, 0xF3,
  0xF8, 0x7C, 0x1E, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00,
  0x3C, 0x00, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0xFF,
  0xE0, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0x80, 0x03, 0xFF, 0xFF,
  0x00, 0x1F, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFE, 0x00,
  0x7F, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x3F,
  0xFF, 0x80, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x01, 0xFF,
  0xC0, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x0F, 0xFF,
  0xFC, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0x80, 0x00, 0xFF,
  0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xF8, 0x00, 0x1F,
  0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x01,
  0xFF, 0xC0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00,
  0x1E, 0x00, 0x00, 0x00, 0x04, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x80, 0x00, 0x00, 0x01, 0xC0,
  0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x0F, 0xFC,
  0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x7F, 0xFF,
  0xC0, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x0F, 0xFF,
  0xFC, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF,
  0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x0F,
  0xFE, 0x00, 0x00, 0x1F, 0xFC, 0x00, 0x00, 0xFF, 0xF8, 0x00, 0x0F, 0xFF,
  0xF0, 0x00, 0x7F, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xF8,
  0x01, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xE0, 0x03,
  0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0x1F,
  0xF8, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0xE0,
  0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
  0x3F, 0xFF, 0x80, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0xFF, 0xFF, 0xFE, 0x00,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xF8,
  0x7F, 0xF8, 0x1F, 0xFC, 0x7F, 0xF0, 0x03, 0xFF, 0x3F, 0xE0, 0x00, 0xFF,
  0x9F, 0xF0, 0x00, 0x3F, 0xDF, 0xF0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x0F,
  0xF8, 0x78, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x03,
  0xFF, 0x80, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x03,
  0xFF, 0x80, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x03,
  0xFF, 0x80, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00,
  0x3F, 0xC0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x07, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x3F, 0xE0, 0x00,
  0x00, 0x1F, 0xF0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x07, 0xFC, 0x00,
  0x00, 0x03, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xF8, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0xFF, 0xC0, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x00,
  0x3F, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x01, 0xFF, 0xFC,
  0x00, 0x3F, 0xFF, 0x00, 0x00, 0x7F, 0xF8, 0x00, 0x00, 0x7F, 0xF0, 0x00,
  0x1F, 0xFC, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x00,
  0x1F, 0xF0, 0x01, 0xFF, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x3F, 0xC0,
  0x0F, 0xE0, 0x00, 0x1F, 0xE0, 0x0F, 0xF0, 0x07, 0xFF, 0x0F, 0xF1, 0xFC,
  0x03, 0xFC, 0x03, 0xFF, 0xF1, 0xFE, 0x1F, 0xC0, 0x7F, 0x00, 0xFF, 0xFF,
  0x3F, 0xC1, 0xF8, 0x1F, 0xC0, 0x3F, 0xFF, 0xEF, 0xF0, 0x3F, 0x03, 0xF8,
  0x0F, 0xFF, 0xFF, 0xFE, 0x03, 0xF0, 0xFE, 0x03, 0xFF, 0xFF, 0xFF, 0xC0,
  0x7E, 0x1F, 0xC0, 0xFF, 0xFF, 0xFF, 0xF8, 0x0F, 0xC3, 0xF0, 0x1F, 0xFC,
  0x1F, 0xFF, 0x00, 0xF8, 0xFE, 0x07, 0xFE, 0x00, 0xFF, 0xC0, 0x1F, 0x1F,
  0x80, 0xFF, 0x80, 0x0F, 0xF8, 0x03, 0xF3, 0xF0, 0x3F, 0xE0, 0x01, 0xFF,
  0x00, 0x7E, 0x7E, 0x07, 0xF8, 0x00, 0x3F, 0xE0, 0x0F, 0xDF, 0x80, 0xFF,
  0x00, 0x07, 0xF8, 0x01, 0xFB, 0xF0, 0x3F, 0xE0, 0x00, 0xFF, 0x00, 0x3E,
  0x7E, 0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x07, 0xCF, 0xC0, 0xFF, 0x00, 0x03,
  0xFC, 0x01, 0xF9, 0xF8, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x3F, 0x3F, 0x03,
  0xF8, 0x00, 0x0F, 0xE0, 0x07, 0xE7, 0xC0, 0xFF, 0x00, 0x03, 0xFC, 0x01,
  0xF8, 0xF8, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x7F, 0x1F, 0x03, 0xFE, 0x00,
  0x1F, 0xF0, 0x0F, 0xC3, 0xF0, 0x7F, 0xC0, 0x03, 0xFE, 0x03, 0xF8, 0x7E,
  0x07, 0xF8, 0x00, 0xFF, 0x80, 0xFE, 0x0F, 0xC0, 0xFF, 0x80, 0x3F, 0xF0,
  0x3F, 0xC1, 0xF8, 0x1F, 0xFC, 0x1F, 0xFE, 0x0F, 0xF0, 0x3F, 0x03, 0xFF,
  0xFF, 0xFF, 0xC7, 0xFC, 0x03, 0xF0, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x7E, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0x0F, 0xC0, 0x7F, 0xFF, 0xFF,
  0xFF, 0xF8, 0x00, 0xFC, 0x07, 0xFF, 0xFB, 0xFF, 0xFC, 0x00, 0x1F, 0x80,
  0x7F, 0xFE, 0x7F, 0xFF, 0x00, 0x03, 0xF8, 0x07, 0xFF, 0x07, 0xFF, 0x80,
  0x00, 0x3F, 0x80, 0x1F, 0x80, 0x7F, 0x80, 0x00, 0x07, 0xF8, 0x00, 0x00,
  0x00, 0x00, 0x03, 0xF8, 0x7F, 0x80, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x0F,
  0xF8, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xC0, 0xFF, 0x80, 0x00, 0x00, 0x00,
  0x0F, 0xF0, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x07, 0xFE, 0x01, 0xFF, 0xE0,
  0x00, 0x00, 0x03, 0xFF, 0x80, 0x1F, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xE0,
  0x01, 0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x07,
  0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xE0,
  0x00, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x07, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xE0, 0x00, 0x00, 0x00,
  0x00, 0x7F, 0xE0, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0x00,
  0x1F, 0xFC, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x0F,
  0xFF, 0x80, 0x00, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x07, 0xFF,
  0xF0, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x00, 0x03, 0xFF, 0xFE,
  0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0xDF, 0xF0, 0x00,
  0x00, 0x07, 0xFC, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xE3, 0xFE, 0x00, 0x00,
  0x03, 0xFF, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xF0, 0xFF, 0xC0, 0x00, 0x00,
  0xFF, 0x83, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x1F, 0xF8, 0x00, 0x00, 0x7F,
  0xC0, 0x7F, 0xC0, 0x00, 0x03, 0xFE, 0x03, 0xFE, 0x00, 0x00, 0x3F, 0xE0,
  0x1F, 0xF8, 0x00, 0x01, 0xFF, 0x00, 0x7F, 0xC0, 0x00, 0x1F, 0xF8, 0x03,
  0xFF, 0x00, 0x00, 0xFF, 0x80, 0x0F, 0xF8, 0x00, 0x07, 0xFC, 0x00, 0x7F,
  0xC0, 0x00, 0x7F, 0xC0, 0x03, 0xFF, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xF8,
  0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0x0F, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0x07,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFC, 0x03, 0xFF,
  0xFF, 0xFF, 0xFF, 0xE0, 0x1F, 0xF0, 0x00, 0x01, 0xFF, 0x01, 0xFF, 0x80,
  0x00, 0x0F, 0xFC, 0x0F, 0xF8, 0x00, 0x00, 0x7F, 0xE0, 0x7F, 0xC0, 0x00,
  0x01, 0xFF, 0x87, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0x3F, 0xE0, 0x00, 0x00,
  0x3F, 0xE3, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x9F, 0xF8, 0x00, 0x00, 0x0F,
  0xFC, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF,
  0x80, 0xFF, 0xFF, 0xFF, 0xC0, 0x07, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF,
  0xFF, 0xFF, 0x81, 0xFF, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xFF, 0xF8,
  0x7F, 0xFF, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF,
  0xFF, 0xF8, 0xFF, 0x80, 0x01, 0xFF, 0xE7, 0xFC, 0x00, 0x03, 0xFF, 0x3F,
  0xE0, 0x00, 0x0F, 0xF9, 0xFF, 0x00, 0x00, 0x7F, 0xCF, 0xF8, 0x00, 0x03,
  0xFE, 0x7F, 0xC0, 0x00, 0x1F, 0xF3, 0xFE, 0x00, 0x00, 0xFF, 0x9F, 0xF0,
  0x00, 0x07, 0xF8, 0xFF, 0x80, 0x00, 0x7F, 0xC7, 0xFC, 0x00, 0x0F, 0xFC,
  0x3F, 0xFF, 0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF,
  0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xFF, 0xFC, 0x1F,
  0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0xFF, 0xFF, 0xFF,
  0xFF, 0x3F, 0xE0, 0x00, 0x3F, 0xFD, 0xFF, 0x00, 0x00, 0x7F, 0xEF, 0xF8,
  0x00, 0x01, 0xFF, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x3F,
  0xFF, 0xF0, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x0F, 0xFF, 0xFC, 0x00,
  0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF,
  0xF8, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF,
  0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xC7, 0xFF,
  0xFF, 0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0xF8,
  0x0F, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00,
  0x0F, 0xFF, 0xF0, 0x00, 0x00, 0xFF, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF,
  0xFC, 0x00, 0x0F, 0xFF, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xFF, 0xFC, 0x01,
  0xFF, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0x80,
  0xFF, 0xF8, 0x3F, 0xFC, 0x00, 0x7F, 0xF0, 0x7F, 0xF0, 0x00, 0x7F, 0xF1,
  0xFF, 0x80, 0x00, 0x7F, 0xE3, 0xFF, 0x00, 0x00, 0x7F, 0xEF, 0xFC, 0x00,
  0x00, 0xFF, 0xDF, 0xF8, 0x00, 0x00, 0xFE, 0x3F, 0xE0, 0x00, 0x01, 0x80,
  0x7F, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x00, 0x03, 0xFF, 0x00,
  0x00, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x00,
  0x1F, 0xF0, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x00, 0x7F, 0xC0,
  0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x00,
  0x03, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x00, 0x0F, 0xFC,
  0x00, 0x00, 0x30, 0x0F, 0xF8, 0x00, 0x00, 0x7C, 0x1F, 0xF0, 0x00, 0x00,
  0xFF, 0x3F, 0xF0, 0x00, 0x03, 0xFF, 0x7F, 0xE0, 0x00, 0x07, 0xFE, 0x7F,
  0xE0, 0x00, 0x1F, 0xF8, 0xFF, 0xC0, 0x00, 0x3F, 0xF0, 0xFF, 0xC0, 0x00,
  0xFF, 0xE1, 0xFF, 0xE0, 0x03, 0xFF, 0x81, 0xFF, 0xF0, 0x3F, 0xFF, 0x03,
  0xFF, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF,
  0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x01, 0xFF, 0xFF, 0xFC, 0x00,
  0x01, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x1F,
  0xF8, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x00,
  0x3F, 0xFF, 0xFF, 0xFC, 0x01, 0xFF, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF,
  0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0xFF, 0xFE, 0x1F,
  0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0x80, 0x07, 0xFF, 0xC7, 0xFC, 0x00, 0x0F,
  0xFF, 0x3F, 0xE0, 0x00, 0x3F, 0xF9, 0xFF, 0x00, 0x00, 0xFF, 0xEF, 0xF8,
  0x00, 0x03, 0xFF, 0x7F, 0xC0, 0x00, 0x0F, 0xFB, 0xFE, 0x00, 0x00, 0x7F,
  0xDF, 0xF0, 0x00, 0x03, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xFC, 0x00,
  0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00,
  0x3F, 0xFF, 0xF0, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x0F, 0xFF, 0xFC,
  0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x1F,
  0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0xC0, 0x00, 0x0F, 0xFF, 0xFE, 0x00,
  0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x03, 0xFE, 0xFF, 0x80, 0x00, 0x3F, 0xF7,
  0xFC, 0x00, 0x01, 0xFF, 0xBF, 0xE0, 0x00, 0x1F, 0xF9, 0xFF, 0x00, 0x01,
  0xFF, 0xCF, 0xF8, 0x00, 0x7F, 0xFC, 0x7F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF,
  0xFF, 0xFF, 0xFE, 0x1F, 0xFF, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xFE,
  0x07, 0xFF, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF, 0xFF, 0xFC, 0x01, 0xFF, 0xFF,
  0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
  0xBF, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF,
  0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF,
  0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF,
  0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x0F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF,
  0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF,
  0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x8F, 0xF8,
  0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x03, 0xFE,
  0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x1F, 0xF0,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x01, 0xFF, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xC7, 0xFF, 0xFF,
  0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF,
  0xFC, 0x7F, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF, 0xFF,
  0xE3, 0xFE, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
  0x1F, 0xF0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x07,
  0xFC, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x00, 0x01, 0xFF, 0xFF,
  0xC0, 0x00, 0x01, 0xFF, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0x80,
  0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFF, 0xFE, 0x00, 0x3F,
  0xFF, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xC0,
  0x3F, 0xFF, 0x03, 0xFF, 0xC0, 0x03, 0xFF, 0xC1, 0xFF, 0xC0, 0x00, 0x7F,
  0xF8, 0x7F, 0xE0, 0x00, 0x0F, 0xFE, 0x3F, 0xF0, 0x00, 0x01, 0xFF, 0x8F,
  0xFC, 0x00, 0x00, 0x3F, 0xE7, 0xFE, 0x00, 0x00, 0x0F, 0xE1, 0xFF, 0x80,
  0x00, 0x03, 0x80, 0x7F, 0xC0, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00,
  0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x1F, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0x80, 0x01,
  0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x7F, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xFF,
  0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xDF,
  0xF0, 0x00, 0x7F, 0xFF, 0xF7, 0xFC, 0x00, 0x00, 0x07, 0xFD, 0xFF, 0x80,
  0x00, 0x01, 0xFF, 0x7F, 0xE0, 0x00, 0x00, 0x7F, 0xCF, 0xFC, 0x00, 0x00,
  0x1F, 0xF3, 0xFF, 0x80, 0x00, 0x07, 0xFC, 0x7F, 0xE0, 0x00, 0x01, 0xFF,
  0x1F, 0xFE, 0x00, 0x01, 0xFF, 0xC3, 0xFF, 0xC0, 0x01, 0xFF, 0xF0, 0xFF,
  0xFE, 0x03, 0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF,
  0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0xFF,
  0xE0, 0x00, 0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xFF, 0xE0, 0x00,
  0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01,
  0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01,
  0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01,
  0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01,
  0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xF0, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x1F, 0xF0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x07, 0xFC, 0x00, 0x00,
  0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x7F,
  0xC0, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x1F,
  0xF0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xE0,
  0x00, 0x01, 0xFF, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x7F, 0xC0, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x07,
  0xFC, 0x3E, 0x00, 0x3F, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xFF,
  0xFC, 0x00, 0x7F, 0xFF, 0xE0, 0x07, 0xFF, 0xFF, 0x80, 0x3F, 0xE7, 0xFC,
  0x03, 0xFF, 0x3F, 0xF8, 0x3F, 0xF9, 0xFF, 0xFF, 0xFF, 0x87, 0xFF, 0xFF,
  0xFC, 0x3F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xE0,
  0x0F, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x1F, 0xF0, 0x00, 0xFF,
  0x80, 0x00, 0x1F, 0xFE, 0xFF, 0x80, 0x00, 0x3F, 0xFC, 0xFF, 0x80, 0x00,
  0x7F, 0xF8, 0xFF, 0x80, 0x00, 0xFF, 0xF0, 0xFF, 0x80, 0x01, 0xFF, 0xE0,
  0xFF, 0x80, 0x03, 0xFF, 0xC0, 0xFF, 0x80, 0x07, 0xFF, 0x80, 0xFF, 0x80,
  0x0F, 0xFF, 0x00, 0xFF, 0x80, 0x1F, 0xFE, 0x00, 0xFF, 0x80, 0x3F, 0xFC,
  0x00, 0xFF, 0x80, 0x7F, 0xF8, 0x00, 0xFF, 0x80, 0xFF, 0xF0, 0x00, 0xFF,
  0x81, 0xFF, 0xE0, 0x00, 0xFF, 0x83, 0xFF, 0xC0, 0x00, 0xFF, 0x87, 0xFF,
  0x80, 0x00, 0xFF, 0x8F, 0xFF, 0x00, 0x00, 0xFF, 0x9F, 0xFE, 0x00, 0x00,
  0xFF, 0xBF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF,
  0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x80,
  0x00, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0xFF,
  0xFF, 0x7F, 0xE0, 0x00, 0xFF, 0xFE, 0x7F, 0xF0, 0x00, 0xFF, 0xFC, 0x3F,
  0xF0, 0x00, 0xFF, 0xF8, 0x3F, 0xF8, 0x00, 0xFF, 0xF0, 0x1F, 0xFC, 0x00,
  0xFF, 0xE0, 0x1F, 0xFC, 0x00, 0xFF, 0xC0, 0x0F, 0xFE, 0x00, 0xFF, 0x80,
  0x07, 0xFF, 0x00, 0xFF, 0x80, 0x07, 0xFF, 0x00, 0xFF, 0x80, 0x03, 0xFF,
  0x80, 0xFF, 0x80, 0x03, 0xFF, 0xC0, 0xFF, 0x80, 0x01, 0xFF, 0xC0, 0xFF,
  0x80, 0x00, 0xFF, 0xE0, 0xFF, 0x80, 0x00, 0xFF, 0xF0, 0xFF, 0x80, 0x00,
  0x7F, 0xF0, 0xFF, 0x80, 0x00, 0x7F, 0xF8, 0xFF, 0x80, 0x00, 0x3F, 0xF8,
  0xFF, 0x80, 0x00, 0x1F, 0xFC, 0xFF, 0x80, 0x00, 0x1F, 0xFE, 0xFF, 0x80,
  0x00, 0x0F, 0xFE, 0xFF, 0x80, 0x00, 0x0F, 0xFF, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x03,
  0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF,
  0xFF, 0xFF, 0xE0, 0x00, 0x7F, 0xFF, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF,
  0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xFF, 0xFF,
  0xF0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xF8,
  0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x0F, 0xEF, 0xFF, 0xFF, 0xF8, 0x01,
  0xFE, 0xFF, 0xFF, 0xBF, 0x80, 0x1F, 0xEF, 0xFF, 0xFB, 0xFC, 0x01, 0xFE,
  0xFF, 0xFF, 0xBF, 0xC0, 0x1F, 0xCF, 0xFF, 0xFB, 0xFC, 0x03, 0xFC, 0xFF,
  0xFF, 0x9F, 0xC0, 0x3F, 0xCF, 0xFF, 0xF9, 0xFE, 0x03, 0xFC, 0xFF, 0xFF,
  0x9F, 0xE0, 0x7F, 0x8F, 0xFF, 0xF9, 0xFE, 0x07, 0xF8, 0xFF, 0xFF, 0x8F,
  0xF0, 0x7F, 0x8F, 0xFF, 0xF8, 0xFF, 0x07, 0xF8, 0xFF, 0xFF, 0x8F, 0xF0,
  0xFF, 0x0F, 0xFF, 0xF8, 0xFF, 0x0F, 0xF0, 0xFF, 0xFF, 0x87, 0xF8, 0xFF,
  0x0F, 0xFF, 0xF8, 0x7F, 0x8F, 0xF0, 0xFF, 0xFF, 0x87, 0xF9, 0xFE, 0x0F,
  0xFF, 0xF8, 0x7F, 0x9F, 0xE0, 0xFF, 0xFF, 0x83, 0xFD, 0xFE, 0x0F, 0xFF,
  0xF8, 0x3F, 0xDF, 0xE0, 0xFF, 0xFF, 0x83, 0xFF, 0xFC, 0x0F, 0xFF, 0xF8,
  0x3F, 0xFF, 0xC0, 0xFF, 0xFF, 0x81, 0xFF, 0xFC, 0x0F, 0xFF, 0xF8, 0x1F,
  0xFF, 0xC0, 0xFF, 0xFF, 0x81, 0xFF, 0xF8, 0x0F, 0xFF, 0xF8, 0x1F, 0xFF,
  0x80, 0xFF, 0xFF, 0x80, 0xFF, 0xF8, 0x0F, 0xFF, 0xF8, 0x0F, 0xFF, 0x80,
  0xFF, 0xFF, 0x80, 0xFF, 0xF0, 0x0F, 0xFF, 0xF8, 0x0F, 0xFF, 0x00, 0xFF,
  0xFF, 0x80, 0x7F, 0xF0, 0x0F, 0xFF, 0xF8, 0x07, 0xFF, 0x00, 0xFF, 0xFF,
  0x80, 0x7F, 0xE0, 0x0F, 0xFF, 0xF8, 0x07, 0xFE, 0x00, 0xFF, 0xFF, 0x80,
  0x3F, 0xE0, 0x0F, 0xF0, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x03,
  0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFE, 0x00,
  0x01, 0xFF, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF,
  0xC0, 0x00, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xFF, 0xC0, 0x03, 0xFF,
  0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0xF0, 0x01,
  0xFF, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xFE,
  0x00, 0xFF, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xEF, 0xFE, 0x03, 0xFF, 0xFC,
  0xFF, 0xC0, 0x7F, 0xFF, 0x9F, 0xFC, 0x0F, 0xFF, 0xF1, 0xFF, 0x81, 0xFF,
  0xFE, 0x1F, 0xF8, 0x3F, 0xFF, 0xC3, 0xFF, 0x87, 0xFF, 0xF8, 0x3F, 0xF0,
  0xFF, 0xFF, 0x07, 0xFF, 0x1F, 0xFF, 0xE0, 0x7F, 0xF3, 0xFF, 0xFC, 0x07,
  0xFE, 0x7F, 0xFF, 0x80, 0xFF, 0xEF, 0xFF, 0xF0, 0x0F, 0xFD, 0xFF, 0xFE,
  0x00, 0xFF, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0xF8, 0x01, 0xFF, 0xFF,
  0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0x3F,
  0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFE, 0x00,
  0x07, 0xFF, 0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFF,
  0x00, 0x01, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x01, 0xFF,
  0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xFF,
  0xE0, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0xFE,
  0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0x00,
  0x07, 0xFF, 0xFF, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x3F,
  0xFF, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xC0, 0x7F, 0xFE, 0x03, 0xFF, 0xE0,
  0x01, 0xFF, 0xE0, 0x7F, 0xF0, 0x00, 0x1F, 0xFE, 0x1F, 0xFC, 0x00, 0x01,
  0xFF, 0xC3, 0xFF, 0x00, 0x00, 0x1F, 0xFC, 0xFF, 0xC0, 0x00, 0x01, 0xFF,
  0x9F, 0xF8, 0x00, 0x00, 0x3F, 0xF3, 0xFE, 0x00, 0x00, 0x03, 0xFF, 0xFF,
  0xC0, 0x00, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0x00,
  0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x0F,
  0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xFF,
  0xF0, 0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0,
  0x00, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x80, 0x00,
  0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x1F, 0xFB, 0xFE, 0x00, 0x00, 0x03,
  0xFF, 0x7F, 0xE0, 0x00, 0x00, 0xFF, 0xCF, 0xFC, 0x00, 0x00, 0x1F, 0xF8,
  0xFF, 0xC0, 0x00, 0x07, 0xFF, 0x1F, 0xFC, 0x00, 0x01, 0xFF, 0xC1, 0xFF,
  0xC0, 0x00, 0x7F, 0xF8, 0x3F, 0xFE, 0x00, 0x1F, 0xFE, 0x03, 0xFF, 0xF0,
  0x1F, 0xFF, 0x80, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xFF,
  0xFC, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xC0,
  0x00, 0x3F, 0xFF, 0xFF, 0xE0, 0x00, 0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x00,
  0x0F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0xFF, 0xFF,
  0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xFF, 0x83, 0xFF,
  0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0xCF,
  0xFF, 0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0x80, 0x07, 0xFF,
  0xBF, 0xE0, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x03,
  0xFF, 0xFF, 0x80, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xF8, 0x00,
  0x07, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xE0,
  0x00, 0x3F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xFE, 0x00, 0x3F, 0xFE, 0xFF,
  0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF, 0xFF, 0xF3,
  0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xFC,
  0x0F, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xF8, 0x00, 0xFF, 0x80, 0x00,
  0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xE0, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF,
  0xFF, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0xFF,
  0xFF, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xFF, 0xFF,
  0xF0, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xC0, 0x7F, 0xFE,
  0x00, 0xFF, 0xF8, 0x00, 0x7F, 0xF8, 0x07, 0xFF, 0x00, 0x01, 0xFF, 0xE0,
  0x7F, 0xF0, 0x00, 0x07, 0xFF, 0x03, 0xFF, 0x00, 0x00, 0x1F, 0xFC, 0x3F,
  0xF0, 0x00, 0x00, 0x7F, 0xE1, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0x0F, 0xF8,
  0x00, 0x00, 0x0F, 0xFC, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xE7, 0xFE, 0x00,
  0x00, 0x03, 0xFF, 0x3F, 0xF0, 0x00, 0x00, 0x0F, 0xF9, 0xFF, 0x00, 0x00,
  0x00, 0x7F, 0xCF, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x7F, 0xC0, 0x00, 0x00,
  0x1F, 0xF3, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x9F, 0xF0, 0x00, 0x00, 0x07,
  0xFC, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE7, 0xFC, 0x00, 0x00, 0x01, 0xFF,
  0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF9, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xCF,
  0xFC, 0x00, 0x00, 0x03, 0xFE, 0x7F, 0xE0, 0x00, 0x00, 0x3F, 0xF3, 0xFF,
  0x00, 0x00, 0x01, 0xFF, 0x8F, 0xF8, 0x00, 0x40, 0x0F, 0xF8, 0x7F, 0xE0,
  0x07, 0xC0, 0xFF, 0xC3, 0xFF, 0x00, 0x3F, 0x87, 0xFE, 0x0F, 0xFC, 0x03,
  0xFE, 0x7F, 0xE0, 0x7F, 0xF0, 0x1F, 0xFF, 0xFF, 0x03, 0xFF, 0xC0, 0x7F,
  0xFF, 0xF0, 0x0F, 0xFF, 0x00, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0x01, 0xFF,
  0xF8, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFF, 0xFF, 0xFC,
  0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0,
  0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x03, 0xFF, 0xFF, 0x7F, 0xF8, 0x00, 0x03, 0xFF, 0x80, 0xFF, 0xC0, 0x00,
  0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0xFF,
  0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF,
  0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF,
  0xF0, 0xFF, 0x80, 0x00, 0xFF, 0xF0, 0xFF, 0x80, 0x00, 0x3F, 0xF0, 0xFF,
  0x80, 0x00, 0x3F, 0xF0, 0xFF, 0x80, 0x00, 0x1F, 0xF0, 0xFF, 0x80, 0x00,
  0x1F, 0xF8, 0xFF, 0x80, 0x00, 0x1F, 0xF0, 0xFF, 0x80, 0x00, 0x1F, 0xF0,
  0xFF, 0x80, 0x00, 0x3F, 0xF0, 0xFF, 0x80, 0x00, 0x3F, 0xF0, 0xFF, 0x80,
  0x00, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF,
  0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0xFF,
  0xFF, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xFF,
  0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0xFF, 0x81, 0xFF, 0xE0, 0x00,
  0xFF, 0x80, 0x7F, 0xF0, 0x00, 0xFF, 0x80, 0x3F, 0xF8, 0x00, 0xFF, 0x80,
  0x1F, 0xFC, 0x00, 0xFF, 0x80, 0x0F, 0xFE, 0x00, 0xFF, 0x80, 0x0F, 0xFF,
  0x00, 0xFF, 0x80, 0x07, 0xFF, 0x00, 0xFF, 0x80, 0x03, 0xFF, 0x80, 0xFF,
  0x80, 0x03, 0xFF, 0xC0, 0xFF, 0x80, 0x01, 0xFF, 0xC0, 0xFF, 0x80, 0x00,
  0xFF, 0xE0, 0xFF, 0x80, 0x00, 0xFF, 0xF0, 0xFF, 0x80, 0x00, 0x7F, 0xF0,
  0xFF, 0x80, 0x00, 0x3F, 0xF8, 0xFF, 0x80, 0x00, 0x3F, 0xF8, 0xFF, 0x80,
  0x00, 0x1F, 0xFC, 0xFF, 0x80, 0x00, 0x0F, 0xFE, 0xFF, 0x80, 0x00, 0x0F,
  0xFE, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x01,
  0xFF, 0xFF, 0x00, 0x00, 0x3F, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF,
  0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFF, 0xF8, 0x1F, 0xFE, 0x03, 0xFF, 0xC1,
  0xFF, 0x80, 0x07, 0xFF, 0x0F, 0xF8, 0x00, 0x1F, 0xF8, 0x7F, 0xC0, 0x00,
  0xFF, 0xC3, 0xFE, 0x00, 0x03, 0xFE, 0x1F, 0xF0, 0x00, 0x1F, 0xF8, 0xFF,
  0x80, 0x00, 0xFF, 0xC7, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00,
  0x00, 0xFF, 0xF8, 0x00, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x00, 0x1F, 0xFF,
  0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xFE, 0x00,
  0x0F, 0xFF, 0xFF, 0xF8, 0x00, 0x1F, 0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF,
  0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xF8, 0x00,
  0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x00, 0x3F,
  0xF8, 0x00, 0x00, 0x00, 0x7F, 0xE0, 0xF0, 0x00, 0x03, 0xFF, 0xFF, 0x80,
  0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x00, 0x7F, 0xDF, 0xF0, 0x00, 0x03, 0xFE,
  0xFF, 0xC0, 0x00, 0x3F, 0xF7, 0xFF, 0x00, 0x03, 0xFF, 0x9F, 0xFC, 0x00,
  0x3F, 0xF8, 0xFF, 0xF8, 0x07, 0xFF, 0xC3, 0xFF, 0xFF, 0xFF, 0xFC, 0x1F,
  0xFF, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xFF,
  0xE0, 0x07, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0xE0, 0x00, 0x1F,
  0xFF, 0xF8, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x3F, 0xFF,
  0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F,
  0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00,
  0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0,
  0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F,
  0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00,
  0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF,
  0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07,
  0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC,
  0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF,
  0xFF, 0x00, 0x01, 0xFF, 0xFF, 0xF0, 0x00, 0x3F, 0xF7, 0xFF, 0x00, 0x1F,
  0xFC, 0xFF, 0xF8, 0x0F, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF,
  0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0xFF, 0x01, 0xFF, 0xFF, 0xFF, 0xE0, 0x1F,
  0xFF, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0xFE, 0x00,
  0x00, 0x1F, 0xFE, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xDF, 0xF0,
  0x00, 0x00, 0x3F, 0xE7, 0xFE, 0x00, 0x00, 0x1F, 0xF9, 0xFF, 0x80, 0x00,
  0x07, 0xFE, 0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x0F, 0xFC, 0x00, 0x00, 0xFF,
  0xC3, 0xFF, 0x00, 0x00, 0x3F, 0xF0, 0x7F, 0xC0, 0x00, 0x0F, 0xF8, 0x1F,
  0xF8, 0x00, 0x07, 0xFE, 0x07, 0xFE, 0x00, 0x01, 0xFF, 0x80, 0xFF, 0x80,
  0x00, 0x7F, 0xC0, 0x3F, 0xF0, 0x00, 0x3F, 0xF0, 0x0F, 0xFC, 0x00, 0x0F,
  0xF8, 0x01, 0xFF, 0x80, 0x03, 0xFE, 0x00, 0x7F, 0xE0, 0x01, 0xFF, 0x80,
  0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFF, 0x00, 0x1F, 0xF0, 0x00, 0xFF,
  0xC0, 0x0F, 0xFC, 0x00, 0x1F, 0xF0, 0x03, 0xFE, 0x00, 0x07, 0xFE, 0x00,
  0xFF, 0x80, 0x01, 0xFF, 0x80, 0x7F, 0xE0, 0x00, 0x3F, 0xE0, 0x1F, 0xF0,
  0x00, 0x0F, 0xFC, 0x07, 0xFC, 0x00, 0x03, 0xFF, 0x03, 0xFF, 0x00, 0x00,
  0x7F, 0xC0, 0xFF, 0x80, 0x00, 0x1F, 0xF8, 0x3F, 0xE0, 0x00, 0x07, 0xFE,
  0x1F, 0xF0, 0x00, 0x00, 0xFF, 0x87, 0xFC, 0x00, 0x00, 0x3F, 0xF1, 0xFF,
  0x00, 0x00, 0x07, 0xFC, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0x3F, 0xE0, 0x00,
  0x00, 0x7F, 0xEF, 0xF8, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x03,
  0xFF, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF,
  0xE0, 0x00, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00,
  0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00,
  0x03, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x7F, 0xF8, 0x00, 0x00, 0x00, 0x1F,
  0xFE, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0,
  0x00, 0x00, 0xFF, 0xC0, 0x00, 0xFF, 0xF0, 0x00, 0x3F, 0xEF, 0xF8, 0x00,
  0x1F, 0xFE, 0x00, 0x07, 0xFD, 0xFF, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0xFF,
  0xBF, 0xE0, 0x00, 0x7F, 0xF8, 0x00, 0x3F, 0xF7, 0xFE, 0x00, 0x1F, 0xFF,
  0x80, 0x07, 0xFC, 0x7F, 0xC0, 0x03, 0xFF, 0xF0, 0x00, 0xFF, 0x8F, 0xF8,
  0x00, 0x7F, 0xFE, 0x00, 0x1F, 0xF1, 0xFF, 0x00, 0x0F, 0xFF, 0xC0, 0x03,
  0xFE, 0x3F, 0xE0, 0x03, 0xFF, 0xFC, 0x00, 0xFF, 0x87, 0xFE, 0x00, 0x7F,
  0xFF, 0x80, 0x1F, 0xF0, 0x7F, 0xC0, 0x0F, 0xFF, 0xF0, 0x03, 0xFE, 0x0F,
  0xF8, 0x03, 0xFF, 0xFE, 0x00, 0x7F, 0xC1, 0xFF, 0x00, 0x7F, 0xFF, 0xE0,
  0x1F, 0xF0, 0x3F, 0xE0, 0x0F, 0xF7, 0xFC, 0x03, 0xFE, 0x03, 0xFE, 0x01,
  0xFE, 0x7F, 0x80, 0x7F, 0xC0, 0x7F, 0xC0, 0x7F, 0xCF, 0xF0, 0x0F, 0xF8,
  0x0F, 0xF8, 0x0F, 0xF1, 0xFF, 0x01, 0xFE, 0x01, 0xFF, 0x01, 0xFE, 0x3F,
  0xE0, 0x7F, 0xC0, 0x1F, 0xF0, 0x3F, 0xC3, 0xFC, 0x0F, 0xF8, 0x03, 0xFE,
  0x0F, 0xF8, 0x7F, 0x81, 0xFF, 0x00, 0x7F, 0xC1, 0xFE, 0x0F, 0xF8, 0x3F,
  0xC0, 0x0F, 0xF8, 0x3F, 0xC0, 0xFF, 0x0F, 0xF8, 0x00, 0xFF, 0x07, 0xF8,
  0x1F, 0xE1, 0xFF, 0x00, 0x1F, 0xF1, 0xFF, 0x03, 0xFC, 0x3F, 0xE0, 0x03,
  0xFE, 0x3F, 0xC0, 0x7F, 0xC7, 0xF8, 0x00, 0x7F, 0xC7, 0xF8, 0x07, 0xF8,
  0xFF, 0x00, 0x07, 0xF9, 0xFF, 0x00, 0xFF, 0x3F, 0xE0, 0x00, 0xFF, 0xBF,
  0xE0, 0x1F, 0xE7, 0xFC, 0x00, 0x1F, 0xF7, 0xF8, 0x03, 0xFE, 0xFF, 0x00,
  0x03, 0xFE, 0xFF, 0x00, 0x3F, 0xDF, 0xE0, 0x00, 0x3F, 0xFF, 0xE0, 0x07,
  0xFB, 0xFC, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0x80, 0x00, 0xFF,
  0xFF, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xE0, 0x01, 0xFF, 0xFC,
  0x00, 0x03, 0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0x00,
  0x07, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xE0, 0x00, 0x7F, 0xFE, 0x00, 0x00,
  0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0x80, 0x01, 0xFF,
  0xF0, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x3F, 0xFC,
  0x00, 0x03, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0x80, 0x00, 0x7F, 0xF0, 0x00,
  0x00, 0xFF, 0xF0, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x01,
  0xFF, 0xC0, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x1F, 0xF8, 0x00, 0x3F, 0xF0,
  0x00, 0x03, 0xFF, 0x0F, 0xFE, 0x00, 0x01, 0xFF, 0xC1, 0xFF, 0xC0, 0x00,
  0xFF, 0xE0, 0x3F, 0xF0, 0x00, 0x3F, 0xF0, 0x0F, 0xFE, 0x00, 0x1F, 0xFC,
  0x01, 0xFF, 0xC0, 0x0F, 0xFE, 0x00, 0x3F, 0xF0, 0x03, 0xFF, 0x00, 0x0F,
  0xFE, 0x01, 0xFF, 0xC0, 0x01, 0xFF, 0xC0, 0xFF, 0xE0, 0x00, 0x3F, 0xF0,
  0x3F, 0xF0, 0x00, 0x0F, 0xFE, 0x1F, 0xFC, 0x00, 0x01, 0xFF, 0xCF, 0xFE,
  0x00, 0x00, 0x3F, 0xF3, 0xFF, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xC0, 0x00,
  0x01, 0xFF, 0xFF, 0xE0, 0x00, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x00, 0x0F,
  0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xFF,
  0x00, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xE0, 0x00,
  0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x00,
  0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x01, 0xFF,
  0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xFF, 0xF0,
  0x00, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xC0, 0x00,
  0x03, 0xFF, 0x7F, 0xF0, 0x00, 0x01, 0xFF, 0xCF, 0xFE, 0x00, 0x00, 0xFF,
  0xE1, 0xFF, 0xC0, 0x00, 0x3F, 0xF0, 0x7F, 0xF0, 0x00, 0x1F, 0xFC, 0x0F,
  0xFE, 0x00, 0x0F, 0xFE, 0x01, 0xFF, 0xC0, 0x03, 0xFF, 0x80, 0x7F, 0xF0,
  0x01, 0xFF, 0xC0, 0x0F, 0xFE, 0x00, 0xFF, 0xE0, 0x01, 0xFF, 0xC0, 0x3F,
  0xF8, 0x00, 0x7F, 0xF0, 0x1F, 0xFC, 0x00, 0x0F, 0xFE, 0x0F, 0xFE, 0x00,
  0x01, 0xFF, 0xC3, 0xFF, 0x80, 0x00, 0x7F, 0xF1, 0xFF, 0xC0, 0x00, 0x0F,
  0xFE, 0xFF, 0xE0, 0x00, 0x01, 0xFF, 0xC0, 0xFF, 0xE0, 0x00, 0x01, 0xFF,
  0xDF, 0xF8, 0x00, 0x00, 0x7F, 0xE7, 0xFF, 0x00, 0x00, 0x3F, 0xF8, 0xFF,
  0xE0, 0x00, 0x0F, 0xFC, 0x1F, 0xF8, 0x00, 0x07, 0xFE, 0x07, 0xFF, 0x00,
  0x03, 0xFF, 0x80, 0xFF, 0xC0, 0x00, 0xFF, 0xC0, 0x1F, 0xF8, 0x00, 0x7F,
  0xE0, 0x07, 0xFF, 0x00, 0x1F, 0xF8, 0x00, 0xFF, 0xC0, 0x0F, 0xFC, 0x00,
  0x1F, 0xF8, 0x07, 0xFE, 0x00, 0x07, 0xFE, 0x01, 0xFF, 0x80, 0x00, 0xFF,
  0xC0, 0xFF, 0xC0, 0x00, 0x3F, 0xF8, 0x3F, 0xF0, 0x00, 0x07, 0xFE, 0x1F,
  0xF8, 0x00, 0x00, 0xFF, 0xC7, 0xFC, 0x00, 0x00, 0x3F, 0xF3, 0xFF, 0x00,
  0x00, 0x07, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00,
  0x3F, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0xFF,
  0xFC, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x80,
  0x00, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00,
  0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFC,
  0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00,
  0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00,
  0x03, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F,
  0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00,
  0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF,
  0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF,
  0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0xE3,
  0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x3F,
  0xFC, 0x00, 0x00, 0x07, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00,
  0x0F, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x3F, 0xFC, 0x00,
  0x00, 0x07, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFE,
  0x00, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xFC, 0x00, 0x00, 0x07,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00,
  0x01, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0x07, 0xFF, 0x00,
  0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x01, 0xFF,
  0xC0, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x00,
  0xFF, 0xE0, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0xC0, 0x00,
  0x00, 0x3F, 0xF8, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0xE0,
  0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF,
  0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC,
  0x00, 0x3F, 0x80, 0x07, 0xE0, 0x01, 0xF8, 0x00, 0x7E, 0x00, 0x1F, 0xC0,
  0x03, 0xF0, 0x00, 0xFC, 0x00, 0x3F, 0x00, 0x0F, 0xE0, 0x01, 0xF8, 0x00,
  0x7E, 0x00, 0x1F, 0x80, 0x07, 0xF0, 0x00, 0xFC, 0x00, 0x3F, 0x00, 0x0F,
  0xC0, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x1F, 0x80, 0x07, 0xE0, 0x01, 0xFC,
  0x00, 0x7F, 0x00, 0x0F, 0xC0, 0x03, 0xF0, 0x00, 0xFE, 0x00, 0x3F, 0x80,
  0x07, 0xE0, 0x01, 0xF8, 0x00, 0x7F, 0x00, 0x1F, 0xC0, 0x03, 0xF0, 0x00,
  0xFC, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x01, 0xF8, 0x00, 0x7E, 0x00, 0x1F,
  0xC0, 0x07, 0xF0, 0x00, 0xFC, 0x00, 0x3F, 0x00, 0x0F, 0xE0, 0x03, 0xF8,
  0x00, 0x7E, 0x00, 0x1F, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0xFE, 0x03, 0xFC,
  0x07, 0xF8, 0x0F, 0xF0, 0x1F, 0xE0, 0x3F, 0xC0, 0x7F, 0x80, 0xFF, 0x01,
  0xFE, 0x03, 0xFC, 0x07, 0xF8, 0x0F, 0xF0, 0x1F, 0xE0, 0x3F, 0xC0, 0x7F,
  0x80, 0xFF, 0x01, 0xFE, 0x03, 0xFC, 0x07, 0xF8, 0x0F, 0xF0, 0x1F, 0xE0,
  0x3F, 0xC0, 0x7F, 0x80, 0xFF, 0x01, 0xFE, 0x03, 0xFC, 0x07, 0xF8, 0x0F,
  0xF0, 0x1F, 0xE0, 0x3F, 0xC0, 0x7F, 0x80, 0xFF, 0x01, 0xFE, 0x03, 0xFC,
  0x07, 0xF8, 0x0F, 0xF0, 0x1F, 0xE0, 0x3F, 0xC0, 0x7F, 0x80, 0xFF, 0x01,
  0xFE, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xC0, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0x00,
  0x00, 0x7F, 0xFC, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0x00, 0x01,
  0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0xFF, 0x7F, 0x80, 0x07, 0xFB,
  0xFC, 0x00, 0x7F, 0x8F, 0xF0, 0x03, 0xFC, 0x7F, 0x80, 0x3F, 0xC1, 0xFE,
  0x01, 0xFE, 0x0F, 0xF0, 0x1F, 0xE0, 0x7F, 0xC0, 0xFF, 0x01, 0xFE, 0x0F,
  0xF8, 0x0F, 0xF8, 0x7F, 0x80, 0x3F, 0xC7, 0xFC, 0x01, 0xFF, 0x3F, 0xC0,
  0x0F, 0xFB, 0xFE, 0x00, 0x3F, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x7F, 0xC0, 0xFF, 0x81, 0xFE, 0x03, 0xFC,
  0x07, 0xF0, 0x1F, 0xE0, 0x3F, 0x80, 0x7F, 0x00, 0xFC, 0x00, 0x3F, 0xF8,
  0x00, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF,
  0x00, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xFC,
  0x1F, 0xF8, 0x1F, 0xFC, 0x3F, 0xC0, 0x0F, 0xF8, 0xFF, 0x80, 0x1F, 0xF0,
  0x1E, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x01, 0xFF, 0x80,
  0x00, 0x1F, 0xFF, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0xFC, 0x07,
  0xFF, 0xFF, 0xF8, 0x3F, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xE1, 0xFF,
  0xFF, 0x3F, 0xC7, 0xFF, 0xC0, 0x7F, 0x8F, 0xFC, 0x00, 0xFF, 0x3F, 0xE0,
  0x03, 0xFE, 0x7F, 0xC0, 0x07, 0xFC, 0xFF, 0x80, 0x0F, 0xF9, 0xFF, 0x00,
  0x3F, 0xF3, 0xFF, 0x00, 0xFF, 0xE3, 0xFF, 0x07, 0xFF, 0xC7, 0xFF, 0xFF,
  0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xF9,
  0xFE, 0x1F, 0xFF, 0xE3, 0xFC, 0x0F, 0xFF, 0x07, 0xFC, 0x07, 0xF8, 0x00,
  0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x81, 0xFC,
  0x00, 0xFF, 0x8F, 0xFF, 0x00, 0xFF, 0x9F, 0xFF, 0xC0, 0xFF, 0xBF, 0xFF,
  0xE0, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF,
  0xFC, 0xFF, 0xF8, 0x3F, 0xFC, 0xFF, 0xF0, 0x0F, 0xFE, 0xFF, 0xE0, 0x07,
  0xFE, 0xFF, 0xC0, 0x03, 0xFE, 0xFF, 0xC0, 0x03, 0xFE, 0xFF, 0x80, 0x01,
  0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01,
  0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01,
  0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01,
  0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0xC0, 0x03, 0xFE, 0xFF, 0xC0, 0x03,
  0xFE, 0xFF, 0xE0, 0x07, 0xFE, 0xFF, 0xF0, 0x0F, 0xFC, 0xFF, 0xFC, 0x1F,
  0xFC, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0x7F, 0xFF,
  0xF0, 0xFF, 0x3F, 0xFF, 0xE0, 0xFF, 0x1F, 0xFF, 0xC0, 0xFF, 0x07, 0xFF,
  0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x03, 0xFF, 0xF8,
  0x00, 0x3F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xE0,
  0x7F, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xFF, 0x0F, 0xFE, 0x0F, 0xFE, 0x7F,
  0xE0, 0x0F, 0xF9, 0xFF, 0x00, 0x1F, 0xE7, 0xFC, 0x00, 0x7F, 0xFF, 0xE0,
  0x01, 0xF0, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00,
  0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x3C, 0x3F, 0xE0, 0x00, 0xFF, 0x7F,
  0xC0, 0x07, 0xFD, 0xFF, 0x00, 0x1F, 0xF7, 0xFE, 0x00, 0xFF, 0x8F, 0xFE,
  0x0F, 0xFE, 0x3F, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF,
  0xFE, 0x01, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xF8,
  0x00, 0x01, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x03,
  0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1F,
  0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8,
  0x01, 0xFC, 0x0F, 0xF0, 0x1F, 0xFE, 0x1F, 0xE0, 0x7F, 0xFF, 0x3F, 0xC3,
  0xFF, 0xFF, 0x7F, 0x8F, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0xFE, 0x7F,
  0xFF, 0xFF, 0xFD, 0xFF, 0xE0, 0xFF, 0xFB, 0xFF, 0x00, 0x7F, 0xF7, 0xFC,
  0x00, 0x7F, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0xC0,
  0x01, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFF, 0x00, 0x03, 0xFF, 0xFC, 0x00,
  0x07, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xE0, 0x00,
  0x3F, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0x80, 0x03,
  0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x3F,
  0xF7, 0xFC, 0x00, 0x7F, 0xEF, 0xFC, 0x01, 0xFF, 0xCF, 0xFE, 0x0F, 0xFF,
  0x9F, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0xFE, 0x1F, 0xFF, 0xFB, 0xFC,
  0x1F, 0xFF, 0xE7, 0xF8, 0x1F, 0xFF, 0x8F, 0xF0, 0x1F, 0xFE, 0x1F, 0xE0,
  0x07, 0xE0, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFF, 0xF8, 0x00,
  0x07, 0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xFC, 0x01,
  0xFF, 0xFF, 0xFC, 0x07, 0xFF, 0xFF, 0xFC, 0x1F, 0xFC, 0x1F, 0xF8, 0x3F,
  0xE0, 0x1F, 0xF8, 0xFF, 0xC0, 0x1F, 0xF1, 0xFF, 0x00, 0x1F, 0xE3, 0xFE,
  0x00, 0x3F, 0xEF, 0xF8, 0x00, 0x3F, 0xDF, 0xF0, 0x00, 0x7F, 0xBF, 0xE0,
  0x00, 0xFF, 0x7F, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF,
  0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00,
  0x00, 0x1F, 0xF0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x03,
  0xE0, 0x7F, 0xC0, 0x07, 0xFC, 0xFF, 0xC0, 0x1F, 0xF8, 0xFF, 0xC0, 0x3F,
  0xE1, 0xFF, 0xC1, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFC,
  0x03, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0x00,
  0x01, 0xFF, 0xFC, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x1F, 0xE0, 0x03,
  0xFF, 0xF0, 0x1F, 0xFF, 0xC0, 0xFF, 0xFE, 0x07, 0xFF, 0xF8, 0x1F, 0xFF,
  0xE0, 0x7F, 0xFF, 0x81, 0xFF, 0x00, 0x07, 0xFC, 0x00, 0x1F, 0xE0, 0x00,
  0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF8, 0x03, 0xFF, 0xFF, 0xCF, 0xFF,
  0xFF, 0x3F, 0xFF, 0xFC, 0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF,
  0x3F, 0xFF, 0xFC, 0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01,
  0xFE, 0x00, 0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE,
  0x00, 0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00,
  0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07,
  0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF8,
  0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x80, 0x01, 0xFE, 0x00, 0x07, 0xF8, 0x00,
  0x1F, 0xE0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xFF, 0xC3, 0xFC, 0x0F,
  0xFF, 0xC7, 0xF8, 0x7F, 0xFF, 0xEF, 0xF1, 0xFF, 0xFF, 0xDF, 0xE3, 0xFF,
  0xFF, 0xFF, 0xCF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFC, 0x1F, 0xFF, 0x7F, 0xE0,
  0x0F, 0xFE, 0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFC, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x3F, 0xFF, 0xE0, 0x00,
  0x7F, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x03,
  0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x1F,
  0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0xE0, 0x01, 0xFF,
  0x7F, 0xC0, 0x07, 0xFE, 0xFF, 0xC0, 0x1F, 0xFD, 0xFF, 0xE0, 0xFF, 0xF9,
  0xFF, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xBF, 0xC3,
  0xFF, 0xFE, 0x7F, 0x83, 0xFF, 0xF8, 0xFF, 0x01, 0xFF, 0xE1, 0xFE, 0x00,
  0xFE, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x0F, 0xF7, 0xC0,
  0x00, 0x1F, 0xEF, 0xFC, 0x00, 0x7F, 0xDF, 0xF8, 0x00, 0xFF, 0xBF, 0xF0,
  0x03, 0xFF, 0x7F, 0xF8, 0x1F, 0xFC, 0x7F, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF,
  0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF,
  0xFC, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x1F, 0xFE, 0x00, 0xFF, 0x80, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x3F, 0xE0, 0x3F, 0x80, 0xFF, 0x87, 0xFF, 0x83, 0xFE, 0x3F,
  0xFF, 0x8F, 0xF9, 0xFF, 0xFF, 0x3F, 0xEF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF,
  0xFB, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFC, 0x03, 0xFF,
  0xFF, 0xE0, 0x07, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8,
  0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00,
  0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03,
  0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF,
  0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF,
  0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8,
  0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFC, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01,
  0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x01, 0xFF, 0x03,
  0xFF, 0x7F, 0xFF, 0x7F, 0xFE, 0x7F, 0xFE, 0x7F, 0xFE, 0x7F, 0xFC, 0xFF,
  0xF8, 0xFF, 0xF0, 0x3F, 0xC0, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF,
  0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0,
  0x00, 0x00, 0xFF, 0x80, 0x1F, 0xFB, 0xFE, 0x00, 0xFF, 0xCF, 0xF8, 0x07,
  0xFE, 0x3F, 0xE0, 0x3F, 0xF0, 0xFF, 0x81, 0xFF, 0x83, 0xFE, 0x0F, 0xFE,
  0x0F, 0xF8, 0x7F, 0xF0, 0x3F, 0xE3, 0xFF, 0x80, 0xFF, 0x8F, 0xFC, 0x03,
  0xFE, 0x7F, 0xE0, 0x0F, 0xFB, 0xFF, 0x00, 0x3F, 0xFF, 0xF8, 0x00, 0xFF,
  0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0x00, 0x3F, 0xFF,
  0xFC, 0x00, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF,
  0xC0, 0x3F, 0xFC, 0xFF, 0x80, 0xFF, 0xE3, 0xFE, 0x03, 0xFF, 0x07, 0xFC,
  0x0F, 0xF8, 0x1F, 0xF0, 0x3F, 0xE0, 0x3F, 0xE0, 0xFF, 0x80, 0xFF, 0xC3,
  0xFE, 0x01, 0xFF, 0x0F, 0xF8, 0x03, 0xFE, 0x3F, 0xE0, 0x0F, 0xF8, 0xFF,
  0x80, 0x1F, 0xF3, 0xFE, 0x00, 0x7F, 0xCF, 0xF8, 0x00, 0xFF, 0xBF, 0xE0,
  0x03, 0xFF, 0xFF, 0x80, 0x07, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0x00, 0x01, 0xF8, 0x00, 0x3F, 0x80, 0xFF, 0x0F, 0xFF,
  0x00, 0xFF, 0xE0, 0xFF, 0x1F, 0xFF, 0x83, 0xFF, 0xF8, 0xFF, 0x3F, 0xFF,
  0xC7, 0xFF, 0xFC, 0xFF, 0x7F, 0xFF, 0xEF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xF8, 0x7F,
  0xFF, 0x07, 0xFE, 0xFF, 0xE0, 0x1F, 0xFE, 0x03, 0xFF, 0xFF, 0xC0, 0x1F,
  0xFC, 0x01, 0xFF, 0xFF, 0x80, 0x1F, 0xF8, 0x01, 0xFF, 0xFF, 0x80, 0x1F,
  0xF8, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF8, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF8, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x01, 0xFF, 0xFF, 0x80, 0x0F,
  0xF0, 0x01, 0xFF, 0x00, 0x00, 0xFE, 0x03, 0xFC, 0x1F, 0xFE, 0x0F, 0xF0,
  0xFF, 0xFE, 0x3F, 0xC7, 0xFF, 0xFC, 0xFF, 0x3F, 0xFF, 0xFB, 0xFD, 0xFF,
  0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0x07, 0xFF, 0xFF, 0xF0, 0x0F,
  0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xF0, 0x01, 0xFF,
  0xFF, 0xC0, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF,
  0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8,
  0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00,
  0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03,
  0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF,
  0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF,
  0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xF0, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x7F, 0xFF, 0xE0, 0x00,
  0x7F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xFF, 0xC0,
  0x7F, 0xFF, 0xFF, 0xF0, 0x7F, 0xF8, 0x3F, 0xFC, 0x3F, 0xF0, 0x07, 0xFE,
  0x3F, 0xF0, 0x01, 0xFF, 0x9F, 0xF0, 0x00, 0x7F, 0xDF, 0xF8, 0x00, 0x3F,
  0xEF, 0xF8, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x03,
  0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0x80, 0x00,
  0x7F, 0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xF8, 0x00,
  0x0F, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0x00,
  0x03, 0xFE, 0x7F, 0xC0, 0x01, 0xFF, 0x3F, 0xF0, 0x01, 0xFF, 0x9F, 0xFC,
  0x01, 0xFF, 0x87, 0xFF, 0x83, 0xFF, 0x81, 0xFF, 0xFF, 0xFF, 0xC0, 0xFF,
  0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0xC0, 0x01,
  0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00,
  0x00, 0x01, 0xFC, 0x00, 0xFF, 0x07, 0xFF, 0x00, 0xFF, 0x1F, 0xFF, 0xC0,
  0xFF, 0x3F, 0xFF, 0xE0, 0xFF, 0x7F, 0xFF, 0xF0, 0xFF, 0x7F, 0xFF, 0xF8,
  0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFC, 0x1F, 0xFC, 0xFF, 0xF0, 0x0F, 0xFE,
  0xFF, 0xE0, 0x07, 0xFE, 0xFF, 0xC0, 0x03, 0xFE, 0xFF, 0xC0, 0x03, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xC0, 0x03, 0xFE,
  0xFF, 0xC0, 0x03, 0xFE, 0xFF, 0xE0, 0x07, 0xFE, 0xFF, 0xF0, 0x0F, 0xFE,
  0xFF, 0xFC, 0x1F, 0xFC, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xF8,
  0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xBF, 0xFF, 0xE0, 0xFF, 0x9F, 0xFF, 0xC0,
  0xFF, 0x87, 0xFF, 0x00, 0xFF, 0x81, 0xFC, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x00, 0xFF, 0xE0, 0xFF, 0x03, 0xFF, 0xF8, 0xFF, 0x07, 0xFF, 0xFC, 0xFF,
  0x0F, 0xFF, 0xFE, 0xFF, 0x1F, 0xFF, 0xFE, 0xFF, 0x3F, 0xFF, 0xFF, 0xFF,
  0x3F, 0xFC, 0x3F, 0xFF, 0x7F, 0xF0, 0x0F, 0xFF, 0x7F, 0xE0, 0x07, 0xFF,
  0x7F, 0xC0, 0x03, 0xFF, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0x80, 0x01, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF,
  0xFF, 0x80, 0x03, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0x7F, 0xC0, 0x07, 0xFF,
  0x7F, 0xE0, 0x07, 0xFF, 0x7F, 0xF0, 0x0F, 0xFF, 0x3F, 0xF8, 0x3F, 0xFF,
  0x3F, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFF,
  0x07, 0xFF, 0xFD, 0xFF, 0x03, 0xFF, 0xF9, 0xFF, 0x01, 0xFF, 0xE1, 0xFF,
  0x00, 0x3F, 0x81, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x01, 0xFF, 0x00, 0x07, 0xE7, 0xF8, 0xFF, 0xFF, 0xCF, 0xFF,
  0xFE, 0x7F, 0xFF, 0xF7, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xDF, 0xFF,
  0xFE, 0xFF, 0xFF, 0xE7, 0xFF, 0x83, 0x3F, 0xF8, 0x01, 0xFF, 0x80, 0x0F,
  0xFC, 0x00, 0x7F, 0xE0, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80,
  0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F,
  0xC0, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00,
  0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE,
  0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00, 0x00, 0x00, 0x3F,
  0xF0, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x7F, 0xFF,
  0xFE, 0x00, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF,
  0xFE, 0x1F, 0xF8, 0x0F, 0xFC, 0x3F, 0xC0, 0x0F, 0xF8, 0x7F, 0x80, 0x0F,
  0xF8, 0xFF, 0x00, 0x0F, 0x01, 0xFF, 0x00, 0x00, 0x03, 0xFF, 0xC0, 0x00,
  0x07, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xC0,
  0x0F, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xE0, 0x07, 0xFF, 0xFF, 0xE0,
  0x03, 0xFF, 0xFF, 0xE0, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x0F, 0xFF, 0xC0,
  0x00, 0x01, 0xFF, 0x83, 0xC0, 0x01, 0xFF, 0x7F, 0xC0, 0x03, 0xFE, 0xFF,
  0x80, 0x07, 0xFD, 0xFF, 0x80, 0x0F, 0xF9, 0xFF, 0xC0, 0x7F, 0xE3, 0xFF,
  0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xFF,
  0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x7F,
  0xE0, 0x00, 0x00, 0x08, 0x00, 0x07, 0x00, 0x03, 0xE0, 0x00, 0xFC, 0x00,
  0x7F, 0x80, 0x1F, 0xF0, 0x03, 0xFE, 0x00, 0x7F, 0xC0, 0x0F, 0xF8, 0x01,
  0xFF, 0x00, 0x3F, 0xE0, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x3F, 0xE0, 0x07,
  0xFC, 0x00, 0xFF, 0x80, 0x1F, 0xF0, 0x03, 0xFE, 0x00, 0x7F, 0xC0, 0x0F,
  0xF8, 0x01, 0xFF, 0x00, 0x3F, 0xE0, 0x07, 0xFC, 0x00, 0xFF, 0x80, 0x1F,
  0xF0, 0x03, 0xFE, 0x00, 0x7F, 0xC0, 0x0F, 0xF8, 0x01, 0xFF, 0x00, 0x3F,
  0xE0, 0x07, 0xFC, 0x00, 0xFF, 0x80, 0x1F, 0xF0, 0x83, 0xFF, 0xF8, 0x7F,
  0xFF, 0x07, 0xFF, 0xE0, 0xFF, 0xFC, 0x0F, 0xFF, 0x80, 0xFF, 0xF0, 0x07,
  0xF8, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F,
  0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80,
  0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01,
  0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F,
  0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x03, 0xFF, 0x7F, 0xC0, 0x0F, 0xFD, 0xFF,
  0x80, 0x7F, 0xF7, 0xFF, 0x07, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0x3F, 0xFF,
  0xFF, 0xFC, 0xFF, 0xFF, 0xEF, 0xF1, 0xFF, 0xFF, 0x3F, 0xC3, 0xFF, 0xF8,
  0xFF, 0x07, 0xFF, 0x83, 0xFC, 0x03, 0xF8, 0x00, 0x00, 0x7F, 0xC0, 0x00,
  0x7F, 0xDF, 0xF0, 0x00, 0x1F, 0xF7, 0xFC, 0x00, 0x0F, 0xF8, 0xFF, 0x80,
  0x03, 0xFE, 0x3F, 0xE0, 0x00, 0xFF, 0x87, 0xF8, 0x00, 0x7F, 0xC1, 0xFF,
  0x00, 0x1F, 0xF0, 0x7F, 0xC0, 0x07, 0xF8, 0x0F, 0xF8, 0x03, 0xFE, 0x03,
  0xFE, 0x00, 0xFF, 0x80, 0x7F, 0x80, 0x7F, 0xC0, 0x1F, 0xF0, 0x1F, 0xF0,
  0x07, 0xFC, 0x07, 0xF8, 0x00, 0xFF, 0x03, 0xFE, 0x00, 0x3F, 0xE0, 0xFF,
  0x80, 0x07, 0xF8, 0x3F, 0xC0, 0x01, 0xFE, 0x1F, 0xF0, 0x00, 0x7F, 0xC7,
  0xF8, 0x00, 0x0F, 0xF1, 0xFE, 0x00, 0x03, 0xFC, 0xFF, 0x80, 0x00, 0x7F,
  0xBF, 0xC0, 0x00, 0x1F, 0xEF, 0xF0, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x00,
  0xFF, 0xFE, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0xC0, 0x00,
  0x01, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xF8, 0x00, 0x00, 0x0F, 0xFE, 0x00,
  0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xF0,
  0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0xFF, 0x80, 0x0F, 0xF8, 0x00, 0xFF,
  0xBF, 0xC0, 0x07, 0xFC, 0x00, 0x7F, 0xDF, 0xE0, 0x03, 0xFE, 0x00, 0x3F,
  0xCF, 0xF8, 0x03, 0xFF, 0x80, 0x3F, 0xE3, 0xFC, 0x01, 0xFF, 0xC0, 0x1F,
  0xF1, 0xFE, 0x00, 0xFF, 0xE0, 0x0F, 0xF0, 0xFF, 0x80, 0x7F, 0xF0, 0x07,
  0xF8, 0x3F, 0xC0, 0x7F, 0xFC, 0x07, 0xFC, 0x1F, 0xE0, 0x3F, 0xFE, 0x03,
  0xFC, 0x0F, 0xF0, 0x1F, 0xFF, 0x01, 0xFE, 0x03, 0xFC, 0x0F, 0xFF, 0x81,
  0xFF, 0x01, 0xFE, 0x0F, 0xFF, 0xE0, 0xFF, 0x00, 0xFF, 0x07, 0xF7, 0xF0,
  0x7F, 0x80, 0x3F, 0x83, 0xFB, 0xF8, 0x3F, 0xC0, 0x1F, 0xE1, 0xFD, 0xFC,
  0x3F, 0xC0, 0x0F, 0xF1, 0xFE, 0x7F, 0x1F, 0xE0, 0x07, 0xF8, 0xFE, 0x3F,
  0x8F, 0xF0, 0x01, 0xFE, 0x7F, 0x1F, 0xCF, 0xF0, 0x00, 0xFF, 0x3F, 0x8F,
  0xE7, 0xF8, 0x00, 0x7F, 0xBF, 0xC3, 0xFB, 0xFC, 0x00, 0x1F, 0xDF, 0xC1,
  0xFD, 0xFC, 0x00, 0x0F, 0xFF, 0xE0, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xF0,
  0x7F, 0xFF, 0x00, 0x01, 0xFF, 0xF8, 0x1F, 0xFF, 0x80, 0x00, 0xFF, 0xF8,
  0x0F, 0xFF, 0x80, 0x00, 0x7F, 0xFC, 0x07, 0xFF, 0xC0, 0x00, 0x1F, 0xFE,
  0x03, 0xFF, 0xE0, 0x00, 0x0F, 0xFE, 0x00, 0xFF, 0xE0, 0x00, 0x07, 0xFF,
  0x00, 0x7F, 0xF0, 0x00, 0x01, 0xFF, 0x80, 0x3F, 0xF8, 0x00, 0x00, 0xFF,
  0xC0, 0x0F, 0xF8, 0x00, 0x00, 0x7F, 0xC0, 0x07, 0xFC, 0x00, 0x00, 0x3F,
  0xE0, 0x03, 0xFE, 0x00, 0x00, 0x7F, 0xF0, 0x01, 0xFF, 0x8F, 0xFC, 0x00,
  0xFF, 0xC1, 0xFF, 0x80, 0x3F, 0xF0, 0x7F, 0xF0, 0x1F, 0xF8, 0x0F, 0xFC,
  0x0F, 0xFC, 0x01, 0xFF, 0x83, 0xFF, 0x00, 0x7F, 0xF1, 0xFF, 0x80, 0x0F,
  0xFC, 0xFF, 0xC0, 0x01, 0xFF, 0xBF, 0xE0, 0x00, 0x3F, 0xFF, 0xF8, 0x00,
  0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x3F, 0xFF, 0x80,
  0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x3F, 0xF0,
  0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0x03, 0xFF,
  0xF0, 0x00, 0x00, 0xFF, 0xFE, 0x00, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x3F,
  0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x07, 0xFE, 0xFF, 0xC0, 0x03,
  0xFF, 0x3F, 0xF8, 0x00, 0xFF, 0x87, 0xFE, 0x00, 0x7F, 0xE0, 0xFF, 0xC0,
  0x3F, 0xF0, 0x3F, 0xF8, 0x1F, 0xF8, 0x07, 0xFE, 0x07, 0xFE, 0x00, 0xFF,
  0xC3, 0xFF, 0x00, 0x3F, 0xF9, 0xFF, 0x80, 0x07, 0xFE, 0x7F, 0xE0, 0x00,
  0xFF, 0xC0, 0x7F, 0xC0, 0x00, 0x7F, 0xDF, 0xF0, 0x00, 0x1F, 0xE7, 0xFC,
  0x00, 0x0F, 0xF8, 0xFF, 0x80, 0x03, 0xFE, 0x3F, 0xE0, 0x00, 0xFF, 0x07,
  0xF8, 0x00, 0x7F, 0xC1, 0xFF, 0x00, 0x1F, 0xF0, 0x7F, 0xC0, 0x07, 0xF8,
  0x0F, 0xF0, 0x03, 0xFE, 0x03, 0xFE, 0x00, 0xFF, 0x80, 0xFF, 0x80, 0x3F,
  0xC0, 0x1F, 0xE0, 0x1F, 0xF0, 0x07, 0xFC, 0x07, 0xF8, 0x00, 0xFF, 0x01,
  0xFE, 0x00, 0x3F, 0xC0, 0xFF, 0x80, 0x0F, 0xF8, 0x3F, 0xC0, 0x01, 0xFE,
  0x0F, 0xF0, 0x00, 0x7F, 0x87, 0xFC, 0x00, 0x1F, 0xF1, 0xFE, 0x00, 0x03,
  0xFC, 0x7F, 0x80, 0x00, 0xFF, 0x3F, 0xE0, 0x00, 0x1F, 0xEF, 0xF0, 0x00,
  0x07, 0xFB, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x00, 0x3F, 0xFF, 0x80,
  0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xFC,
  0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0xFF,
  0xE0, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00,
  0x01, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xF8, 0x00,
  0x01, 0xFF, 0xFC, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x1F, 0xFF, 0x00,
  0x00, 0x07, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF,
  0xFF, 0xF3, 0xFF, 0xFF, 0xFF, 0x9F, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF,
  0xE7, 0xFF, 0xFF, 0xFF, 0x3F, 0xFF, 0xFF, 0xF9, 0xFF, 0xFF, 0xFF, 0xC0,
  0x00, 0x1F, 0xFC, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x0F, 0xFC, 0x00, 0x00,
  0xFF, 0xE0, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x0F, 0xFE,
  0x00, 0x00, 0xFF, 0xE0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x7F, 0xF0, 0x00,
  0x07, 0xFF, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x7F,
  0xF0, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x03, 0xFF, 0x80,
  0x00, 0x3F, 0xF8, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x03,
  0xF8, 0x00, 0xFF, 0xC0, 0x1F, 0xFE, 0x00, 0xFF, 0xF0, 0x0F, 0xFF, 0x80,
  0xFF, 0xFC, 0x07, 0xFF, 0xE0, 0x3F, 0xFF, 0x01, 0xFF, 0x00, 0x0F, 0xF0,
  0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xE0, 0x00, 0xFF, 0x00, 0x07,
  0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFC, 0x00, 0x0F, 0xE0, 0x00, 0xFF, 0x00,
  0x07, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0xFF,
  0x80, 0x0F, 0xF8, 0x00, 0xFF, 0xC0, 0x3F, 0xFE, 0x01, 0xFF, 0xE0, 0x0F,
  0xFE, 0x00, 0x7F, 0xE0, 0x03, 0xFF, 0x00, 0x1F, 0xFC, 0x00, 0xFF, 0xF0,
  0x07, 0xFF, 0xC0, 0x07, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0x7F, 0xC0, 0x01,
  0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xE0,
  0x00, 0xFF, 0x00, 0x03, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0xFF, 0x00, 0x07,
  0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80,
  0x03, 0xFE, 0x00, 0x1F, 0xFF, 0x80, 0xFF, 0xFC, 0x07, 0xFF, 0xE0, 0x1F,
  0xFF, 0x00, 0x7F, 0xF8, 0x01, 0xFF, 0xC0, 0x07, 0xFE, 0x00, 0x07, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0x00, 0x07, 0xFE, 0x00, 0x3F, 0xFC, 0x01,
  0xFF, 0xF0, 0x0F, 0xFF, 0x80, 0x7F, 0xFE, 0x03, 0xFF, 0xF0, 0x1F, 0xFF,
  0x80, 0x07, 0xFC, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x03, 0xFC, 0x00,
  0x1F, 0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE,
  0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xE0, 0x00,
  0xFF, 0x00, 0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x00, 0xFF, 0x80, 0x07, 0xFF,
  0x00, 0x1F, 0xFE, 0x00, 0xFF, 0xF0, 0x03, 0xFF, 0x80, 0x07, 0xFC, 0x00,
  0x3F, 0xE0, 0x07, 0xFF, 0x00, 0x7F, 0xF8, 0x03, 0xFF, 0xC0, 0x3F, 0xF8,
  0x01, 0xFF, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xF8, 0x00, 0x3F,
  0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00,
  0x1F, 0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE,
  0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x0F, 0xF8, 0x0F, 0xFF, 0xC0, 0x7F,
  0xFE, 0x03, 0xFF, 0xF0, 0x1F, 0xFF, 0x00, 0xFF, 0xF8, 0x07, 0xFF, 0x80,
  0x3F, 0xF0, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x8F, 0xFF,
  0x80, 0x00, 0xCF, 0xFF, 0xF8, 0x01, 0xEF, 0xFF, 0xFF, 0x83, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0x70, 0x00, 0xFF, 0xFF, 0x30,
  0x00, 0x1F, 0xFE, 0x10, 0x00, 0x01, 0xFC, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x03, 0xE1, 0xF0,
  0xF8, 0x7C, 0x3F, 0x1F, 0x8F, 0xC7, 0xE7, 0xF3, 0xF9, 0xFC, 0xFE, 0x7F,
  0x3F, 0x9F, 0xCF, 0xF7, 0xFB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x78, 0x00,
  0x00, 0x01, 0xE0, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x3C, 0x00, 0x00,
  0x00, 0xF0, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00,
  0x78, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x07, 0xFF, 0x00, 0x00, 0xFF, 0xFC,
  0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xF8,
  0x1F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xC3, 0xFF, 0x8F, 0xFF, 0x9F,
  0xF8, 0x3F, 0xFE, 0x7F, 0xC0, 0xFF, 0xFB, 0xFF, 0x07, 0x9F, 0xFF, 0xF8,
  0x1E, 0x7C, 0x3F, 0xE0, 0x78, 0x00, 0xFF, 0x83, 0xE0, 0x03, 0xFE, 0x0F,
  0x00, 0x0F, 0xF0, 0x3C, 0x00, 0x3F, 0xC1, 0xF0, 0x00, 0xFF, 0x07, 0x80,
  0x03, 0xFC, 0x1E, 0x00, 0x0F, 0xF8, 0x78, 0x00, 0x3F, 0xE3, 0xC0, 0x00,
  0xFF, 0x8F, 0x00, 0x03, 0xFE, 0x3C, 0x0F, 0x0F, 0xF9, 0xF0, 0x3F, 0xDF,
  0xF7, 0x81, 0xFF, 0x7F, 0xFE, 0x07, 0xFD, 0xFF, 0xF8, 0x3F, 0xE3, 0xFF,
  0xC3, 0xFF, 0x8F, 0xFF, 0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF,
  0xFF, 0x80, 0x7F, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0xFF, 0xFE,
  0x00, 0x03, 0xFF, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x00,
  0x01, 0xE0, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00,
  0xF8, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x3C,
  0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x07,
  0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xF8, 0x00,
  0xFF, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xE0, 0x1F, 0xFC, 0x3F, 0xF8,
  0x07, 0xFC, 0x03, 0xFF, 0x03, 0xFE, 0x00, 0x7F, 0xC0, 0xFF, 0x80, 0x1F,
  0xF0, 0x3F, 0xC0, 0x03, 0xFC, 0x0F, 0xF0, 0x00, 0xE0, 0x03, 0xFC, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x1F,
  0xF0, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xFF, 0x80, 0x0F,
  0xFF, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xFE, 0x00,
  0x3F, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xF8, 0x00,
  0x00, 0x01, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x1F, 0xE0,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x0F,
  0xF0, 0x00, 0x00, 0x07, 0xFB, 0xF0, 0x00, 0x83, 0xFF, 0xFF, 0x00, 0x63,
  0xFF, 0xFF, 0xF0, 0x7C, 0x7F, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0xFF,
  0xE3, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x1F, 0x01, 0xFF,
  0xFF, 0xC7, 0x00, 0x0F, 0xFF, 0xE1, 0x00, 0x00, 0x3F, 0xC0, 0x04, 0x00,
  0x00, 0x10, 0x0E, 0x00, 0x00, 0x38, 0x1F, 0x00, 0x00, 0x78, 0x3F, 0x07,
  0xF0, 0xFC, 0x3F, 0x9F, 0xFC, 0xFE, 0x7F, 0xFF, 0xFF, 0xFF, 0x7F, 0xFF,
  0xFF, 0xFF, 0x3F, 0xFF, 0xFF, 0xFE, 0x1F, 0xFF, 0xFF, 0xFC, 0x0F, 0xFF,
  0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFC, 0x1F, 0xF8, 0x1F, 0xF0,
  0x07, 0xF8, 0x1F, 0xE0, 0x03, 0xFC, 0x1F, 0xE0, 0x03, 0xFC, 0x1F, 0xC0,
  0x01, 0xFC, 0x1F, 0xC0, 0x01, 0xFC, 0x1F, 0xC0, 0x01, 0xFC, 0x1F, 0xE0,
  0x03, 0xFC, 0x1F, 0xE0, 0x03, 0xFC, 0x1F, 0xF0, 0x07, 0xF8, 0x0F, 0xFC,
  0x1F, 0xF8, 0x0F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF,
  0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xFE, 0x7F, 0xFF, 0xFF, 0xFF, 0x7F, 0xFF,
  0xFF, 0xFF, 0x3F, 0x9F, 0xFC, 0xFE, 0x1F, 0x87, 0xF0, 0xFC, 0x0F, 0x00,
  0x00, 0x78, 0x0E, 0x00, 0x00, 0x38, 0x04, 0x00, 0x00, 0x10, 0xFF, 0xC0,
  0x00, 0x7F, 0xCF, 0xF8, 0x00, 0x0F, 0xF9, 0xFF, 0x80, 0x03, 0xFF, 0x1F,
  0xF0, 0x00, 0x7F, 0xC3, 0xFF, 0x00, 0x1F, 0xF8, 0x3F, 0xE0, 0x03, 0xFE,
  0x07, 0xFE, 0x00, 0xFF, 0xC0, 0x7F, 0xC0, 0x1F, 0xF0, 0x0F, 0xF8, 0x07,
  0xFE, 0x00, 0xFF, 0x80, 0xFF, 0x80, 0x1F, 0xF0, 0x3F, 0xF0, 0x01, 0xFF,
  0x07, 0xFC, 0x00, 0x3F, 0xE1, 0xFF, 0x80, 0x07, 0xFE, 0x3F, 0xE0, 0x00,
  0x7F, 0xCF, 0xFC, 0x00, 0x0F, 0xFD, 0xFF, 0x00, 0x00, 0xFF, 0xBF, 0xE0,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x00, 0x3F, 0xFF,
  0xC0, 0x03, 0xFF, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF,
  0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xFF, 0x87,
  0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x0F, 0xFF, 0xFF,
  0xFF, 0xE1, 0xFF, 0xFF, 0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xFF, 0x87, 0xFF,
  0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFE, 0x1F, 0xFF, 0xFF, 0xFF, 0xC3,
  0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0x00,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x00, 0xFF,
  0x80, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00,
  0x7F, 0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x3F, 0xE0, 0x00, 0x01,
  0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xFC, 0x00, 0xFF,
  0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xF8, 0x07, 0xFF, 0xFF, 0xF8, 0x0F, 0xFC,
  0x3F, 0xF0, 0x3F, 0xE0, 0x1F, 0xF0, 0x7F, 0xC0, 0x3F, 0xE0, 0xFF, 0x80,
  0x3F, 0xC0, 0xFF, 0x80, 0x78, 0x01, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0x80,
  0x00, 0x03, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xE0,
  0x00, 0x1F, 0xFF, 0xE0, 0x00, 0x7F, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0xF0,
  0x07, 0xFF, 0xFF, 0xF0, 0x0F, 0xF3, 0xFF, 0xF8, 0x1F, 0xC1, 0xFF, 0xF8,
  0x7F, 0x81, 0xFF, 0xF8, 0xFF, 0x00, 0xFF, 0xF9, 0xFE, 0x00, 0xFF, 0xF3,
  0xFC, 0x00, 0xFF, 0xF7, 0xF8, 0x00, 0x7F, 0xEF, 0xF8, 0x00, 0x7F, 0xFF,
  0xF8, 0x00, 0x7F, 0xDF, 0xF8, 0x00, 0x7F, 0xBF, 0xFC, 0x00, 0xFF, 0x7F,
  0xFC, 0x01, 0xFE, 0x7F, 0xFE, 0x03, 0xFC, 0x7F, 0xFE, 0x07, 0xF0, 0x7F,
  0xFF, 0x1F, 0xE0, 0x7F, 0xFF, 0x7F, 0x80, 0x3F, 0xFF, 0xFF, 0x00, 0x3F,
  0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x1F,
  0xFF, 0x80, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0x80, 0x00, 0x07,
  0xFF, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x03, 0xFE, 0x01, 0xE0, 0x07,
  0xFC, 0x3F, 0xC0, 0x0F, 0xF8, 0x7F, 0xC0, 0x1F, 0xF0, 0xFF, 0xC0, 0x3F,
  0xE1, 0xFF, 0xC0, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0x01, 0xFF, 0xFF, 0xFE,
  0x03, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80,
  0x01, 0xFF, 0xFC, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0xFF, 0x07, 0xFF, 0xF8,
  0x3F, 0xFF, 0xC1, 0xFF, 0xFE, 0x0F, 0xFF, 0xF0, 0x7F, 0xFF, 0x83, 0xFF,
  0xFC, 0x1F, 0xFF, 0xE0, 0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x00,
  0x03, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x1F,
  0xFC, 0x01, 0xFF, 0x80, 0x00, 0x7F, 0xC0, 0x00, 0x7F, 0xC0, 0x01, 0xFE,
  0x00, 0x00, 0x3F, 0xC0, 0x07, 0xF0, 0x00, 0x00, 0x3F, 0x80, 0x1F, 0xC0,
  0x00, 0x00, 0x1F, 0x80, 0x3F, 0x00, 0x00, 0x00, 0x1F, 0x80, 0xFC, 0x00,
  0x7F, 0x00, 0x1F, 0x83, 0xF0, 0x07, 0xFF, 0xC0, 0x3F, 0x07, 0xC0, 0x1F,
  0xFF, 0xC0, 0x3F, 0x0F, 0x80, 0x7F, 0xFF, 0xC0, 0x3E, 0x3E, 0x01, 0xFC,
  0x1F, 0xC0, 0x7C, 0x7C, 0x07, 0xE0, 0x1F, 0x80, 0x7D, 0xF0, 0x0F, 0xC0,
  0x1F, 0x80, 0xFB, 0xE0, 0x3F, 0x00, 0x18, 0x01, 0xF7, 0xC0, 0x7E, 0x00,
  0x00, 0x01, 0xEF, 0x80, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x01, 0xF0, 0x00,
  0x00, 0x07, 0xFC, 0x03, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x07, 0xC0, 0x00,
  0x00, 0x1F, 0xF0, 0x0F, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x1F, 0x00, 0x00,
  0x00, 0x7F, 0xE0, 0x3E, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x7E, 0x00, 0x00,
  0x01, 0xFF, 0x80, 0xFC, 0x00, 0x70, 0x07, 0xDF, 0x00, 0xFC, 0x01, 0xF8,
  0x0F, 0x9F, 0x01, 0xF8, 0x07, 0xE0, 0x1F, 0x3E, 0x01, 0xFC, 0x1F, 0xC0,
  0x7C, 0x3E, 0x01, 0xFF, 0xFF, 0x00, 0xF8, 0x7C, 0x01, 0xFF, 0xFC, 0x03,
  0xF0, 0xFC, 0x01, 0xFF, 0xF0, 0x07, 0xC0, 0xFC, 0x00, 0x7F, 0x00, 0x1F,
  0x80, 0xFC, 0x00, 0x00, 0x00, 0x7E, 0x01, 0xFC, 0x00, 0x00, 0x01, 0xF8,
  0x01, 0xFC, 0x00, 0x00, 0x0F, 0xF0, 0x01, 0xFE, 0x00, 0x00, 0x3F, 0xC0,
  0x01, 0xFF, 0x00, 0x01, 0xFF, 0x00, 0x01, 0xFF, 0xC0, 0x1F, 0xF8, 0x00,
  0x01, 0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x80, 0x00,
  0x00, 0x7F, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x00,
  0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x7F, 0xFE, 0x07,
  0xFF, 0xF8, 0x7F, 0xFF, 0xC3, 0xFF, 0xFF, 0x3F, 0x83, 0xF9, 0xF8, 0x1F,
  0xC1, 0xC0, 0x7E, 0x00, 0x0F, 0xF0, 0x07, 0xFF, 0x83, 0xFF, 0xFC, 0x7F,
  0xFF, 0xE7, 0xFF, 0xBF, 0x3F, 0xC1, 0xFB, 0xF8, 0x1F, 0xDF, 0xC0, 0xFE,
  0xFE, 0x07, 0xF7, 0xF8, 0xFF, 0xBF, 0xFF, 0xFC, 0xFF, 0xFF, 0xE7, 0xFF,
  0xFF, 0x0F, 0xFD, 0xF8, 0x3F, 0x87, 0xE0, 0x00, 0x7F, 0x07, 0xF0, 0x03,
  0xF8, 0x3F, 0x80, 0x3F, 0x83, 0xF8, 0x03, 0xFC, 0x3F, 0xC0, 0x1F, 0xC1,
  0xFC, 0x01, 0xFC, 0x1F, 0xC0, 0x1F, 0xE1, 0xFE, 0x00, 0xFE, 0x0F, 0xE0,
  0x0F, 0xF0, 0xFF, 0x00, 0xFF, 0x0F, 0xF0, 0x07, 0xF8, 0x7F, 0x80, 0x7F,
  0x87, 0xF8, 0x07, 0xFC, 0x7F, 0xC0, 0x3F, 0xC3, 0xFC, 0x03, 0xFE, 0x3F,
  0xE0, 0x0F, 0xF0, 0xFF, 0x00, 0x7F, 0xC7, 0xFC, 0x01, 0xFE, 0x1F, 0xE0,
  0x07, 0xF8, 0x7F, 0x80, 0x3F, 0xC3, 0xFC, 0x00, 0xFF, 0x0F, 0xF0, 0x03,
  0xF8, 0x3F, 0x80, 0x1F, 0xE1, 0xFE, 0x00, 0x7F, 0x07, 0xF8, 0x01, 0xFC,
  0x1F, 0xC0, 0x0F, 0xF0, 0xFF, 0x00, 0x3F, 0x83, 0xF8, 0x00, 0xFE, 0x0F,
  0xE0, 0x07, 0xF0, 0x7F, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3F, 0xC0,
  0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x01, 0xFE, 0x00,
  0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x0F, 0xF0, 0x00,
  0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x00,
  0x00, 0x1F, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x80, 0x00,
  0x07, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x1F, 0xFC, 0x01, 0xFF, 0xC0, 0x00,
  0x7F, 0xC0, 0x00, 0x7F, 0xC0, 0x01, 0xFE, 0x00, 0x00, 0x3F, 0xC0, 0x07,
  0xF0, 0x00, 0x00, 0x3F, 0x80, 0x1F, 0xC0, 0x00, 0x00, 0x1F, 0x80, 0x3F,
  0x00, 0x00, 0x00, 0x1F, 0x80, 0xFC, 0x3F, 0xFF, 0x80, 0x1F, 0x83, 0xF0,
  0x7F, 0xFF, 0xC0, 0x1F, 0x07, 0xC0, 0xFF, 0xFF, 0xC0, 0x3F, 0x0F, 0x81,
  0xFF, 0xFF, 0xC0, 0x3E, 0x3E, 0x03, 0xE0, 0x3F, 0x80, 0x7C, 0x7C, 0x07,
  0xC0, 0x1F, 0x80, 0x7D, 0xF0, 0x0F, 0x80, 0x3F, 0x00, 0xFB, 0xE0, 0x1F,
  0x00, 0x3E, 0x00, 0xF7, 0xC0, 0x3E, 0x00, 0xFC, 0x01, 0xEF, 0x80, 0x7C,
  0x01, 0xF8, 0x03, 0xFF, 0x00, 0xF8, 0x07, 0xE0, 0x07, 0xFC, 0x01, 0xFF,
  0xFF, 0x80, 0x0F, 0xF8, 0x03, 0xFF, 0xFE, 0x00, 0x1F, 0xF0, 0x07, 0xFF,
  0xF0, 0x00, 0x3F, 0xF0, 0x0F, 0xFF, 0xC0, 0x00, 0x7F, 0xE0, 0x1F, 0x0F,
  0xC0, 0x00, 0xFF, 0xC0, 0x3E, 0x0F, 0xC0, 0x01, 0xFF, 0x80, 0x7C, 0x0F,
  0xC0, 0x03, 0xDF, 0x00, 0xF8, 0x0F, 0xC0, 0x0F, 0x9F, 0x01, 0xF0, 0x1F,
  0x80, 0x1F, 0x3E, 0x03, 0xE0, 0x1F, 0x80, 0x7C, 0x3E, 0x07, 0xC0, 0x3F,
  0x00, 0xF8, 0x7C, 0x0F, 0x80, 0x3F, 0x03, 0xF0, 0xFC, 0x1F, 0x00, 0x7F,
  0x07, 0xC0, 0xFC, 0x3E, 0x00, 0x7E, 0x1F, 0x80, 0xFC, 0x00, 0x00, 0x00,
  0x7E, 0x01, 0xFC, 0x00, 0x00, 0x01, 0xF8, 0x01, 0xFC, 0x00, 0x00, 0x0F,
  0xF0, 0x01, 0xFE, 0x00, 0x00, 0x3F, 0xC0, 0x01, 0xFF, 0x00, 0x01, 0xFF,
  0x00, 0x01, 0xFF, 0xC0, 0x1F, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xE0,
  0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x7F, 0xFF, 0xFC, 0x00,
  0x00, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00,
  0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x03, 0xF8, 0x01,
  0xFF, 0xC0, 0x7F, 0xFC, 0x1F, 0xFF, 0xC7, 0xFF, 0xFC, 0xFC, 0x1F, 0xBF,
  0x01, 0xFF, 0xC0, 0x1F, 0xF8, 0x03, 0xFF, 0x00, 0x7F, 0xE0, 0x0F, 0xFC,
  0x01, 0xFF, 0xC0, 0x7E, 0xFC, 0x1F, 0x9F, 0xFF, 0xF1, 0xFF, 0xFC, 0x1F,
  0xFF, 0x01, 0xFF, 0xC0, 0x0F, 0xE0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00,
  0x3F, 0xC0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x01,
  0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x0F,
  0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x7F,
  0x80, 0x00, 0x00, 0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xF0, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00,
  0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00,
  0x03, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x03, 0xF8, 0x03, 0xFF, 0xC3, 0xFF,
  0xF8, 0xFF, 0xFF, 0x7F, 0xFF, 0xDF, 0x83, 0xF7, 0xE0, 0xFF, 0xF0, 0x1F,
  0x00, 0x0F, 0xC0, 0x03, 0xF0, 0x01, 0xF8, 0x00, 0xFC, 0x00, 0xFF, 0x00,
  0x7F, 0x00, 0x3F, 0x80, 0x3F, 0xC0, 0x1F, 0xE0, 0x0F, 0xE0, 0x07, 0xFF,
  0xFD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x03, 0xF8,
  0x01, 0xFF, 0xC0, 0x7F, 0xFC, 0x1F, 0xFF, 0xC7, 0xFF, 0xF8, 0xFC, 0x3F,
  0x1F, 0x03, 0xF0, 0xE0, 0xFC, 0x00, 0x3F, 0x80, 0x3F, 0xE0, 0x07, 0xF8,
  0x00, 0xFF, 0x80, 0x1F, 0xF8, 0x00, 0x1F, 0x80, 0x01, 0xF3, 0xE0, 0x3F,
  0xFC, 0x0F, 0xEF, 0xC3, 0xFD, 0xFF, 0xFF, 0x1F, 0xFF, 0xC1, 0xFF, 0xF8,
  0x1F, 0xFC, 0x00, 0xFE, 0x00, 0x0F, 0xF8, 0x7F, 0xC1, 0xFE, 0x0F, 0xF0,
  0x3F, 0x81, 0xFE, 0x07, 0xF0, 0x3F, 0x80, 0xFC, 0x00, 0xFF, 0x80, 0x07,
  0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07,
  0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF,
  0xF0, 0x03, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0xFF,
  0x87, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFB, 0xFF, 0xBF, 0xFF, 0xEF, 0xFE, 0xFF, 0xFF, 0x9F, 0xF3,
  0xFF, 0xFE, 0x1F, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xFC, 0x1F,
  0xFF, 0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF, 0xFF, 0xFE,
  0x7F, 0xFF, 0xFF, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0x87,
  0xF0, 0xFF, 0xFF, 0xF0, 0xFE, 0x1F, 0xFF, 0xFE, 0x1F, 0xC3, 0xFF, 0xFF,
  0xC3, 0xF8, 0x7F, 0xFF, 0xF8, 0x7F, 0x0F, 0xFF, 0xFF, 0x0F, 0xE1, 0xFF,
  0xFF, 0xE1, 0xFC, 0x3F, 0xFF, 0xFC, 0x3F, 0x87, 0xFF, 0xFF, 0x87, 0xF0,
  0xFF, 0xFF, 0xF0, 0xFE, 0x0F, 0xFF, 0xFE, 0x1F, 0xC1, 0xFF, 0xFF, 0xC3,
  0xF8, 0x1F, 0xFF, 0xF8, 0x7F, 0x03, 0xFF, 0xFF, 0x0F, 0xE0, 0x3F, 0xFF,
  0xE1, 0xFC, 0x03, 0xFF, 0xFC, 0x3F, 0x80, 0x1F, 0xFF, 0x87, 0xF0, 0x00,
  0x7F, 0xF0, 0xFE, 0x00, 0x01, 0xFE, 0x1F, 0xC0, 0x00, 0x3F, 0xC3, 0xF8,
  0x00, 0x07, 0xF8, 0x7F, 0x00, 0x00, 0xFF, 0x0F, 0xE0, 0x00, 0x1F, 0xE1,
  0xFC, 0x00, 0x03, 0xFC, 0x3F, 0x80, 0x00, 0x7F, 0x87, 0xF0, 0x00, 0x0F,
  0xF0, 0xFE, 0x00, 0x01, 0xFE, 0x1F, 0xC0, 0x00, 0x3F, 0xC3, 0xF8, 0x00,
  0x07, 0xF8, 0x7F, 0x00, 0x00, 0xFF, 0x0F, 0xE0, 0x00, 0x1F, 0xE1, 0xFC,
  0x00, 0x03, 0xFC, 0x3F, 0x80, 0x00, 0x7F, 0x87, 0xF0, 0x00, 0x0F, 0xF0,
  0xFE, 0x00, 0x01, 0xFE, 0x1F, 0xC0, 0x00, 0x3F, 0xC3, 0xF8, 0x00, 0x07,
  0xF8, 0x7F, 0x00, 0x00, 0xFF, 0x0F, 0xE0, 0x00, 0x1F, 0xE1, 0xFC, 0x00,
  0x03, 0xFC, 0x3F, 0x80, 0x00, 0x7F, 0x87, 0xF0, 0x00, 0x0F, 0xF0, 0xFE,
  0x00, 0x01, 0xFE, 0x1F, 0xC0, 0x00, 0x3F, 0xC3, 0xF8, 0x00, 0x07, 0xF8,
  0x7F, 0x00, 0x00, 0xFF, 0x0F, 0xE0, 0x00, 0x1F, 0xE1, 0xFC, 0x00, 0x03,
  0xFC, 0x3F, 0x80, 0x00, 0x7F, 0x87, 0xF0, 0x00, 0x0F, 0xF0, 0xFE, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x07,
  0xF8, 0x07, 0xFF, 0x07, 0xFF, 0xC2, 0x07, 0xF0, 0x01, 0xF8, 0x00, 0xFC,
  0x00, 0x7E, 0x00, 0xFE, 0xFF, 0xFE, 0x7F, 0xFE, 0x0F, 0xF8, 0x00, 0x01,
  0xF0, 0x3F, 0x07, 0xF0, 0xFF, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xF3,
  0xF8, 0x3F, 0x03, 0xF0, 0x3F, 0x03, 0xF0, 0x3F, 0x03, 0xF0, 0x3F, 0x03,
  0xF0, 0x3F, 0x03, 0xF0, 0x3F, 0x03, 0xF0, 0x3F, 0x03, 0xF0, 0x01, 0xFC,
  0x00, 0x3F, 0xF8, 0x07, 0xFF, 0xF0, 0x7F, 0xFF, 0xC3, 0xFF, 0xFE, 0x3F,
  0xC7, 0xF9, 0xF8, 0x1F, 0xDF, 0xC0, 0x7F, 0xFE, 0x03, 0xFF, 0xE0, 0x1F,
  0xFF, 0x00, 0xFF, 0xF8, 0x07, 0xFF, 0xC0, 0x3F, 0xFE, 0x01, 0xFF, 0xF8,
  0x0F, 0xFF, 0xC0, 0x7F, 0x7E, 0x07, 0xF3, 0xFC, 0x7F, 0x8F, 0xFF, 0xF8,
  0x7F, 0xFF, 0xC1, 0xFF, 0xFC, 0x03, 0xFF, 0x80, 0x07, 0xF0, 0x00, 0x7E,
  0x07, 0xE0, 0x03, 0xF8, 0x3F, 0x80, 0x0F, 0xE0, 0xFE, 0x00, 0x7F, 0x07,
  0xF0, 0x01, 0xFC, 0x1F, 0xC0, 0x0F, 0xF0, 0xFF, 0x00, 0x3F, 0x83, 0xF8,
  0x01, 0xFE, 0x1F, 0xE0, 0x07, 0xF8, 0x7F, 0x80, 0x3F, 0xC1, 0xFC, 0x00,
  0xFF, 0x0F, 0xF0, 0x03, 0xFC, 0x3F, 0xC0, 0x1F, 0xE1, 0xFE, 0x00, 0x7F,
  0x87, 0xF8, 0x03, 0xFE, 0x3F, 0xE0, 0x1F, 0xE1, 0xFE, 0x01, 0xFF, 0x1F,
  0xF0, 0x0F, 0xF0, 0xFF, 0x00, 0xFF, 0x0F, 0xF0, 0x07, 0xF8, 0x7F, 0x80,
  0x7F, 0x87, 0xF8, 0x07, 0xF8, 0x7F, 0x80, 0x3F, 0x83, 0xFC, 0x03, 0xFC,
  0x3F, 0xC0, 0x1F, 0xC1, 0xFC, 0x01, 0xFC, 0x1F, 0xE0, 0x0F, 0xE0, 0xFE,
  0x00, 0xFE, 0x0F, 0xE0, 0x07, 0xE0, 0x7E, 0x00, 0x00, 0x01, 0xF0, 0x00,
  0x00, 0x3E, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x3E, 0x00, 0x01, 0xFC, 0x00,
  0x00, 0x1F, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F, 0x00, 0x07, 0xFF, 0x00,
  0x00, 0x0F, 0x00, 0x07, 0xFF, 0x80, 0x00, 0x0F, 0x80, 0x03, 0xFF, 0xC0,
  0x00, 0x0F, 0x80, 0x01, 0xF7, 0xE0, 0x00, 0x07, 0xC0, 0x00, 0xF3, 0xF0,
  0x00, 0x07, 0xC0, 0x00, 0x41, 0xF8, 0x00, 0x03, 0xE0, 0x00, 0x00, 0xFC,
  0x00, 0x03, 0xE0, 0x00, 0x00, 0x7E, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x3F,
  0x00, 0x01, 0xF0, 0x00, 0x00, 0x1F, 0x80, 0x01, 0xF0, 0x00, 0x00, 0x0F,
  0xC0, 0x00, 0xF8, 0x00, 0x00, 0x07, 0xE0, 0x00, 0xF8, 0x00, 0x00, 0x03,
  0xF0, 0x00, 0x78, 0x00, 0x00, 0x01, 0xF8, 0x00, 0x7C, 0x00, 0x00, 0x00,
  0xFC, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x3E, 0x00, 0x00, 0x00,
  0x3F, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x1F, 0x80, 0x1F, 0x00, 0x00, 0x00,
  0x0F, 0xC0, 0x1F, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x0F, 0x00, 0x03, 0xF8,
  0x00, 0x00, 0x0F, 0x80, 0x01, 0xFC, 0x00, 0x00, 0x0F, 0x80, 0x01, 0xFE,
  0x00, 0x00, 0x07, 0xC0, 0x01, 0xFF, 0x00, 0x00, 0x07, 0xC0, 0x01, 0xFF,
  0x80, 0x00, 0x03, 0xE0, 0x01, 0xFF, 0xC0, 0x00, 0x03, 0xE0, 0x01, 0xF7,
  0xE0, 0x00, 0x03, 0xE0, 0x01, 0xFB, 0xF0, 0x00, 0x01, 0xF0, 0x01, 0xF9,
  0xF8, 0x00, 0x01, 0xF0, 0x01, 0xF8, 0xFC, 0x00, 0x00, 0xF8, 0x00, 0xF8,
  0x7E, 0x00, 0x00, 0xF8, 0x00, 0xF8, 0x3F, 0x00, 0x00, 0x78, 0x00, 0xFF,
  0xFF, 0xF0, 0x00, 0x7C, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0x7C, 0x00, 0x3F,
  0xFF, 0xFC, 0x00, 0x3E, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x3E, 0x00, 0x0F,
  0xFF, 0xFF, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x1F, 0x00, 0x00,
  0x00, 0x7E, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x0F, 0x80, 0x00,
  0x00, 0x1F, 0x80, 0x0F, 0x80, 0x00, 0x00, 0x0F, 0xC0, 0x07, 0xC0, 0x00,
  0x00, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x3E, 0x00, 0x03, 0xF0, 0x00,
  0x00, 0x7C, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x7C, 0x00, 0x0F, 0xF0, 0x00,
  0x00, 0xF8, 0x00, 0x7F, 0xF0, 0x00, 0x00, 0xF8, 0x00, 0xFF, 0xF0, 0x00,
  0x01, 0xF0, 0x00, 0xFF, 0xF0, 0x00, 0x03, 0xE0, 0x00, 0xFB, 0xF0, 0x00,
  0x03, 0xE0, 0x00, 0xF3, 0xF0, 0x00, 0x07, 0xC0, 0x00, 0x83, 0xF0, 0x00,
  0x07, 0xC0, 0x00, 0x03, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x03, 0xF0, 0x00,
  0x0F, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xF0, 0x00,
  0x3E, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x3E, 0x00, 0x00, 0x03, 0xF0, 0x00,
  0x7C, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x7C, 0x00, 0x00, 0x03, 0xF0, 0x00,
  0xF8, 0x00, 0x00, 0x03, 0xF0, 0x00, 0xF8, 0x00, 0x00, 0x03, 0xF0, 0x01,
  0xF0, 0x00, 0x00, 0x03, 0xF0, 0x01, 0xE0, 0x00, 0x00, 0x03, 0xF0, 0x03,
  0xE0, 0x00, 0x00, 0x03, 0xF0, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x07,
  0xC0, 0x1F, 0xE0, 0x00, 0x00, 0x0F, 0x80, 0x7F, 0xF8, 0x00, 0x00, 0x0F,
  0x80, 0xFF, 0xFC, 0x00, 0x00, 0x1F, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x1E,
  0x01, 0xFF, 0xFF, 0x00, 0x00, 0x3E, 0x03, 0xF8, 0x7F, 0x00, 0x00, 0x7C,
  0x03, 0xF0, 0x3F, 0x00, 0x00, 0x7C, 0x03, 0xF0, 0x3F, 0x00, 0x00, 0xF8,
  0x00, 0x00, 0x3F, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x7E, 0x00, 0x01, 0xF0,
  0x00, 0x00, 0xFE, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xFC, 0x00, 0x03, 0xE0,
  0x00, 0x03, 0xF8, 0x00, 0x07, 0xC0, 0x00, 0x0F, 0xF0, 0x00, 0x07, 0xC0,
  0x00, 0x1F, 0xE0, 0x00, 0x0F, 0x80, 0x00, 0x3F, 0x80, 0x00, 0x0F, 0x80,
  0x00, 0x7F, 0x00, 0x00, 0x1F, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1E, 0x00,
  0x01, 0xFF, 0xFF, 0x00, 0x3E, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x7C, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x7C, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0xF8, 0x00,
  0x07, 0xFF, 0xFF, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x03, 0xF8, 0x00,
  0x00, 0x00, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x00, 0xF8, 0x00, 0x7F, 0xFC,
  0x00, 0x00, 0x3E, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x0F, 0x80, 0x07, 0xFF,
  0xF8, 0x00, 0x01, 0xF0, 0x00, 0xFC, 0x3F, 0x00, 0x00, 0x7C, 0x00, 0x1F,
  0x03, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0xE0, 0xFC, 0x00, 0x03, 0xE0, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x78, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x1F, 0x00,
  0x00, 0x07, 0xF8, 0x00, 0x07, 0xC0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0xF8,
  0x00, 0x00, 0x1F, 0xF8, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x1F, 0x80, 0x07,
  0xC0, 0x00, 0x00, 0x01, 0xF0, 0x01, 0xF0, 0x00, 0x03, 0xE0, 0x3F, 0x00,
  0x7C, 0x00, 0x00, 0xFC, 0x0F, 0xE0, 0x0F, 0x80, 0x00, 0x0F, 0xC3, 0xFC,
  0x03, 0xE0, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x7C, 0x00, 0x00, 0x1F, 0xFF,
  0xC0, 0x1F, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0x03, 0xC0, 0x00, 0x00, 0x1F,
  0xFC, 0x00, 0xF8, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x3E, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x07, 0xC0, 0x01, 0xF8, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x7F,
  0x00, 0x00, 0x00, 0x3E, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x0F, 0x80, 0x03,
  0xFC, 0x00, 0x00, 0x01, 0xE0, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x7C, 0x00,
  0x3F, 0xF0, 0x00, 0x00, 0x1F, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x03, 0xE0,
  0x03, 0xEF, 0xC0, 0x00, 0x00, 0xF8, 0x00, 0xFD, 0xF8, 0x00, 0x00, 0x1F,
  0x00, 0x3F, 0x3F, 0x00, 0x00, 0x07, 0xC0, 0x0F, 0xC7, 0xE0, 0x00, 0x01,
  0xF0, 0x01, 0xF0, 0xFC, 0x00, 0x00, 0x3E, 0x00, 0x7C, 0x1F, 0x80, 0x00,
  0x0F, 0x80, 0x1F, 0xFF, 0xFE, 0x00, 0x01, 0xF0, 0x03, 0xFF, 0xFF, 0xC0,
  0x00, 0x7C, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0x0F, 0x00, 0x0F, 0xFF, 0xFF,
  0x00, 0x03, 0xE0, 0x01, 0xFF, 0xFF, 0xE0, 0x00, 0xF8, 0x00, 0x00, 0x07,
  0xE0, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x07, 0xC0, 0x00, 0x00,
  0x1F, 0x80, 0x00, 0xF8, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x3E, 0x00, 0x00,
  0x00, 0x7E, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x01, 0xFE,
  0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x3F,
  0xC0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0x00, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00,
  0x1F, 0xF0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x07, 0xFE, 0x00, 0x00,
  0x07, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00,
  0x07, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00,
  0x07, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00,
  0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x00, 0x7F, 0xC0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x3E, 0x1F, 0xF0, 0x00,
  0x1F, 0xEF, 0xF8, 0x00, 0x1F, 0xF3, 0xFE, 0x00, 0x0F, 0xF9, 0xFF, 0x80,
  0x0F, 0xFC, 0x7F, 0xF0, 0x3F, 0xFC, 0x3F, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF,
  0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x3F,
  0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x00, 0x00,
  0x3F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x03,
  0xF8, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x7F,
  0x00, 0x00, 0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x07, 0xE0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x00,
  0x00, 0x0F, 0xFE, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xF8, 0x00, 0x00, 0x00,
  0x07, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x03,
  0xFF, 0xF8, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0xC0, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0xEF, 0xF8,
  0x00, 0x00, 0x03, 0xFE, 0x7F, 0xE0, 0x00, 0x00, 0x1F, 0xF1, 0xFF, 0x00,
  0x00, 0x01, 0xFF, 0x8F, 0xF8, 0x00, 0x00, 0x0F, 0xF8, 0x7F, 0xE0, 0x00,
  0x00, 0x7F, 0xC1, 0xFF, 0x00, 0x00, 0x07, 0xFC, 0x0F, 0xFC, 0x00, 0x00,
  0x3F, 0xE0, 0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x01, 0xFF, 0x00, 0x00, 0x1F,
  0xF0, 0x0F, 0xFC, 0x00, 0x00, 0xFF, 0x80, 0x3F, 0xE0, 0x00, 0x0F, 0xFC,
  0x01, 0xFF, 0x80, 0x00, 0x7F, 0xC0, 0x07, 0xFC, 0x00, 0x03, 0xFE, 0x00,
  0x3F, 0xE0, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0xFF,
  0xFC, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,
  0x80, 0x07, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0,
  0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0xFE, 0x01,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x0F, 0xF8, 0x00, 0x00, 0xFF, 0x80, 0xFF,
  0xC0, 0x00, 0x07, 0xFE, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xF0, 0x3F, 0xE0,
  0x00, 0x00, 0xFF, 0xC3, 0xFF, 0x00, 0x00, 0x07, 0xFE, 0x1F, 0xF0, 0x00,
  0x00, 0x1F, 0xF1, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xCF, 0xFC, 0x00, 0x00,
  0x07, 0xFE, 0x7F, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0xC0, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xC0,
  0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00,
  0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00,
  0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x01, 0xFF,
  0xC0, 0x00, 0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xF8,
  0x00, 0x00, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00,
  0x00, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xC0, 0x00,
  0x00, 0x01, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x00,
  0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x00, 0x00,
  0x3F, 0xEF, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x7F, 0xE0, 0x00, 0x00, 0x1F,
  0xF1, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x8F, 0xF8, 0x00, 0x00, 0x0F, 0xF8,
  0x7F, 0xE0, 0x00, 0x00, 0x7F, 0xC1, 0xFF, 0x00, 0x00, 0x07, 0xFC, 0x0F,
  0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x01, 0xFF,
  0x00, 0x00, 0x1F, 0xF0, 0x0F, 0xFC, 0x00, 0x00, 0xFF, 0x80, 0x3F, 0xE0,
  0x00, 0x0F, 0xFC, 0x01, 0xFF, 0x80, 0x00, 0x7F, 0xC0, 0x07, 0xFC, 0x00,
  0x03, 0xFE, 0x00, 0x3F, 0xE0, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x80, 0x01,
  0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0xFF,
  0xFF, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x7F, 0xFF,
  0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF,
  0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x0F, 0xF8, 0x00, 0x00,
  0xFF, 0x80, 0xFF, 0xC0, 0x00, 0x07, 0xFE, 0x07, 0xFC, 0x00, 0x00, 0x3F,
  0xF0, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0xC3, 0xFF, 0x00, 0x00, 0x07, 0xFE,
  0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xF1, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xCF,
  0xFC, 0x00, 0x00, 0x07, 0xFE, 0x7F, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xFE,
  0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x00,
  0x03, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x1F, 0xFC, 0x00, 0x00, 0x00, 0x01,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x1F, 0xDF, 0xC0, 0x00, 0x00, 0x00, 0xFC,
  0xFE, 0x00, 0x00, 0x00, 0x0F, 0xE3, 0xF8, 0x00, 0x00, 0x00, 0xFE, 0x0F,
  0xE0, 0x00, 0x00, 0x0F, 0xE0, 0x3F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00,
  0x00, 0x01, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x00,
  0x00, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0x00, 0x00,
  0x7F, 0xFE, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x1F,
  0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x0F, 0xFF,
  0xF8, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x07, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x3F, 0xEF, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x7F, 0xE0,
  0x00, 0x00, 0x1F, 0xF1, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x8F, 0xF8, 0x00,
  0x00, 0x0F, 0xF8, 0x7F, 0xE0, 0x00, 0x00, 0x7F, 0xC1, 0xFF, 0x00, 0x00,
  0x07, 0xFC, 0x0F, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x3F, 0xE0, 0x00, 0x01,
  0xFF, 0x01, 0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x0F, 0xFC, 0x00, 0x00, 0xFF,
  0x80, 0x3F, 0xE0, 0x00, 0x0F, 0xFC, 0x01, 0xFF, 0x80, 0x00, 0x7F, 0xC0,
  0x07, 0xFC, 0x00, 0x03, 0xFE, 0x00, 0x3F, 0xE0, 0x00, 0x3F, 0xE0, 0x01,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xFF, 0xFF,
  0xF0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFF, 0xFF, 0xFC,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x3F, 0xFF, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x0F,
  0xF8, 0x00, 0x00, 0xFF, 0x80, 0xFF, 0xC0, 0x00, 0x07, 0xFE, 0x07, 0xFC,
  0x00, 0x00, 0x3F, 0xF0, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0xC3, 0xFF, 0x00,
  0x00, 0x07, 0xFE, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xF1, 0xFF, 0x80, 0x00,
  0x00, 0xFF, 0xCF, 0xFC, 0x00, 0x00, 0x07, 0xFE, 0x7F, 0xC0, 0x00, 0x00,
  0x1F, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x01, 0xF0, 0x07,
  0x80, 0x00, 0x00, 0x3F, 0xE0, 0x3C, 0x00, 0x00, 0x01, 0xFF, 0xC3, 0xE0,
  0x00, 0x00, 0x1F, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00,
  0x00, 0x07, 0x87, 0xFF, 0x80, 0x00, 0x00, 0x3C, 0x0F, 0xF8, 0x00, 0x00,
  0x01, 0xE0, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x3F,
  0xF8, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x1F, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xC0,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xF8, 0x00,
  0x00, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x00,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0x00,
  0x07, 0xFD, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xCF, 0xFC, 0x00, 0x00, 0x03,
  0xFE, 0x3F, 0xE0, 0x00, 0x00, 0x3F, 0xF1, 0xFF, 0x00, 0x00, 0x01, 0xFF,
  0x0F, 0xFC, 0x00, 0x00, 0x0F, 0xF8, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x81,
  0xFF, 0x80, 0x00, 0x07, 0xFC, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x3F,
  0xE0, 0x00, 0x03, 0xFE, 0x01, 0xFF, 0x80, 0x00, 0x1F, 0xF0, 0x07, 0xFC,
  0x00, 0x01, 0xFF, 0x80, 0x3F, 0xF0, 0x00, 0x0F, 0xF8, 0x00, 0xFF, 0x80,
  0x00, 0x7F, 0xC0, 0x07, 0xFC, 0x00, 0x07, 0xFC, 0x00, 0x3F, 0xF0, 0x00,
  0x3F, 0xFF, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x1F,
  0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x0F, 0xFF,
  0xFF, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF,
  0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0x00, 0x00,
  0x1F, 0xF0, 0x1F, 0xF8, 0x00, 0x00, 0xFF, 0xC0, 0xFF, 0x80, 0x00, 0x07,
  0xFE, 0x07, 0xFC, 0x00, 0x00, 0x1F, 0xF8, 0x7F, 0xE0, 0x00, 0x00, 0xFF,
  0xC3, 0xFE, 0x00, 0x00, 0x03, 0xFE, 0x3F, 0xF0, 0x00, 0x00, 0x1F, 0xF9,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0xCF, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xFF,
  0xC0, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x0F, 0xF0, 0x7F, 0x80, 0x00, 0x00,
  0x7F, 0x83, 0xFC, 0x00, 0x00, 0x03, 0xFC, 0x1F, 0xE0, 0x00, 0x00, 0x1F,
  0xE0, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x07, 0xF8, 0x00, 0x00, 0x07, 0xF8,
  0x3F, 0xC0, 0x00, 0x00, 0x3F, 0xC1, 0xFE, 0x00, 0x00, 0x01, 0xFE, 0x0F,
  0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00,
  0x00, 0x01, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x3F,
  0xFF, 0xE0, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0x00, 0x07, 0xFD, 0xFF,
  0x00, 0x00, 0x00, 0x7F, 0xCF, 0xFC, 0x00, 0x00, 0x03, 0xFE, 0x3F, 0xE0,
  0x00, 0x00, 0x3F, 0xF1, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x0F, 0xFC, 0x00,
  0x00, 0x0F, 0xF8, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x81, 0xFF, 0x80, 0x00,
  0x07, 0xFC, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x3F, 0xE0, 0x00, 0x03,
  0xFE, 0x01, 0xFF, 0x80, 0x00, 0x1F, 0xF0, 0x07, 0xFC, 0x00, 0x01, 0xFF,
  0x80, 0x3F, 0xF0, 0x00, 0x0F, 0xF8, 0x00, 0xFF, 0x80, 0x00, 0x7F, 0xC0,
  0x07, 0xFC, 0x00, 0x07, 0xFC, 0x00, 0x3F, 0xF0, 0x00, 0x3F, 0xFF, 0xFF,
  0xFF, 0x80, 0x03, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xFF, 0xFF,
  0xF0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0xFF, 0xFE,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0,
  0x3F, 0xFF, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x1F,
  0xF8, 0x00, 0x00, 0xFF, 0xC0, 0xFF, 0x80, 0x00, 0x07, 0xFE, 0x07, 0xFC,
  0x00, 0x00, 0x1F, 0xF8, 0x7F, 0xE0, 0x00, 0x00, 0xFF, 0xC3, 0xFE, 0x00,
  0x00, 0x03, 0xFE, 0x3F, 0xF0, 0x00, 0x00, 0x1F, 0xF9, 0xFF, 0x80, 0x00,
  0x00, 0xFF, 0xCF, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xC0, 0x00, 0x00,
  0x1F, 0xF8, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x00,
  0x00, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x00, 0x00, 0xF1, 0xE0, 0x00,
  0x00, 0x00, 0x07, 0x07, 0x00, 0x00, 0x00, 0x00, 0x78, 0x38, 0x00, 0x00,
  0x00, 0x03, 0xC1, 0xE0, 0x00, 0x00, 0x00, 0x0E, 0x0E, 0x00, 0x00, 0x00,
  0x00, 0x78, 0xF0, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00,
  0x1F, 0xFC, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x0F,
  0xFF, 0x80, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x00, 0x07, 0xFF,
  0xE0, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x00, 0x01, 0xFF, 0xFC,
  0x00, 0x00, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x80,
  0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0, 0x00,
  0x00, 0x03, 0xFE, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE7, 0xFE, 0x00, 0x00,
  0x01, 0xFF, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xF8, 0xFF, 0x80, 0x00, 0x00,
  0xFF, 0x87, 0xFE, 0x00, 0x00, 0x07, 0xFC, 0x1F, 0xF0, 0x00, 0x00, 0x7F,
  0xC0, 0xFF, 0xC0, 0x00, 0x03, 0xFE, 0x03, 0xFE, 0x00, 0x00, 0x1F, 0xF0,
  0x1F, 0xF0, 0x00, 0x01, 0xFF, 0x00, 0xFF, 0xC0, 0x00, 0x0F, 0xF8, 0x03,
  0xFE, 0x00, 0x00, 0xFF, 0xC0, 0x1F, 0xF8, 0x00, 0x07, 0xFC, 0x00, 0x7F,
  0xC0, 0x00, 0x3F, 0xE0, 0x03, 0xFE, 0x00, 0x03, 0xFE, 0x00, 0x1F, 0xF8,
  0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0x0F, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0, 0x07,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xF8, 0x03, 0xFF,
  0xFF, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0x80,
  0x00, 0x0F, 0xF8, 0x0F, 0xFC, 0x00, 0x00, 0x7F, 0xE0, 0x7F, 0xC0, 0x00,
  0x03, 0xFF, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0x3F, 0xF0, 0x00, 0x00,
  0x7F, 0xE1, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x1F, 0xF8, 0x00, 0x00, 0x0F,
  0xFC, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xE7, 0xFC, 0x00, 0x00, 0x01, 0xFF,
  0xFF, 0xE0, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFC, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00,
  0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF,
  0xFF, 0xE0, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x03,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0x00, 0x00, 0x1F, 0xF0, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x7F,
  0xE1, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x83, 0xFE, 0x00, 0x00,
  0x00, 0x00, 0x03, 0xFF, 0x07, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x07, 0xFC,
  0x0F, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x1F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x3F, 0xE0, 0x3F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xC0,
  0x7F, 0xC0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0x00, 0xFF, 0x80, 0x00, 0x00,
  0x00, 0x03, 0xFE, 0x01, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x03,
  0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xF0, 0x07, 0xFF, 0xFF, 0xFF, 0xC0,
  0x00, 0x7F, 0xC0, 0x0F, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0x80, 0x1F,
  0xFF, 0xFF, 0xFF, 0x00, 0x03, 0xFF, 0x00, 0x3F, 0xFF, 0xFF, 0xFE, 0x00,
  0x07, 0xFC, 0x00, 0x7F, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xF8, 0x00, 0xFF,
  0xFF, 0xFF, 0xF8, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0xFF, 0xFF, 0xF0, 0x00,
  0xFF, 0xC0, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFC,
  0x00, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x0F,
  0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xE0,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0x1F, 0xF8,
  0x00, 0x07, 0xFC, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x0F, 0xF8, 0x00,
  0x00, 0x00, 0xFF, 0xC0, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x01, 0xFF, 0x80,
  0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xC7, 0xFE, 0x00, 0x00, 0x7F, 0xFF, 0xFF,
  0xFF, 0x8F, 0xFC, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0xF0, 0x00,
  0x01, 0xFF, 0xFF, 0xFF, 0xFE, 0x7F, 0xE0, 0x00, 0x03, 0xFF, 0xFF, 0xFF,
  0xFC, 0xFF, 0xC0, 0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0x00, 0x00,
  0x0F, 0xFF, 0xFF, 0xFF, 0xF7, 0xFE, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF,
  0xE0, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xFF, 0xF0, 0x00, 0x00,
  0xFF, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0xFF,
  0xFC, 0x00, 0x7F, 0xFF, 0xFF, 0xFC, 0x01, 0xFF, 0xFF, 0xFF, 0xFC, 0x03,
  0xFF, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0x80, 0xFF, 0xF8, 0x3F, 0xFC, 0x00,
  0x7F, 0xF0, 0x7F, 0xF0, 0x00, 0x7F, 0xF1, 0xFF, 0x80, 0x00, 0x7F, 0xE3,
  0xFF, 0x00, 0x00, 0x7F, 0xEF, 0xFC, 0x00, 0x00, 0xFF, 0xDF, 0xF8, 0x00,
  0x00, 0xFE, 0x3F, 0xE0, 0x00, 0x01, 0x80, 0x7F, 0xC0, 0x00, 0x00, 0x01,
  0xFF, 0x80, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x07, 0xFE, 0x00,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x00,
  0x3F, 0xE0, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00,
  0x07, 0xFE, 0x00, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x30, 0x0F, 0xF8,
  0x00, 0x00, 0x7C, 0x1F, 0xF0, 0x00, 0x00, 0xFF, 0x3F, 0xF0, 0x00, 0x03,
  0xFF, 0x7F, 0xE0, 0x00, 0x07, 0xFE, 0x7F, 0xE0, 0x00, 0x1F, 0xF8, 0xFF,
  0xC0, 0x00, 0x3F, 0xF0, 0xFF, 0xC0, 0x00, 0xFF, 0xE1, 0xFF, 0xE0, 0x03,
  0xFF, 0x81, 0xFF, 0xF0, 0x3F, 0xFF, 0x03, 0xFF, 0xFF, 0xFF, 0xFC, 0x03,
  0xFF, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFF, 0xC0, 0x03, 0xFF, 0xFF,
  0xFF, 0x00, 0x01, 0xFF, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0xF0, 0x00,
  0x00, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x00, 0x00, 0x3F,
  0xC0, 0x00, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x00, 0x03, 0xFF, 0xE0, 0x00,
  0x00, 0x04, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00,
  0x1F, 0x80, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x01, 0xFC, 0x00,
  0x00, 0x07, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x07,
  0xFC, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00,
  0x03, 0xFC, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x0F, 0xE0, 0x00,
  0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x0F, 0xE0,
  0x00, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFF,
  0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF,
  0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xFB,
  0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xE0, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F,
  0xFF, 0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xF8,
  0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF,
  0xE3, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00,
  0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE,
  0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00,
  0x1F, 0xE0, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00,
  0x00, 0xFE, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x1F, 0x80, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF,
  0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF,
  0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF,
  0xFF, 0xFF, 0xFF, 0xBF, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xFF,
  0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x8F, 0xFF,
  0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F,
  0xFF, 0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00,
  0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x0F, 0xFE,
  0x00, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x00, 0xFE,
  0xFE, 0x00, 0x00, 0x3F, 0x3F, 0x80, 0x00, 0x1F, 0xC7, 0xF0, 0x00, 0x0F,
  0xE0, 0xFE, 0x00, 0x07, 0xF0, 0x1F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x3F, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF,
  0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF,
  0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xE0,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF,
  0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF,
  0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF,
  0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x8F, 0xFF,
  0xFF, 0xFF, 0xE3, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00,
  0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0x03, 0xFC, 0x1F, 0xE0, 0x00, 0xFF, 0x07, 0xF8, 0x00, 0x3F, 0xC1, 0xFE,
  0x00, 0x0F, 0xF0, 0x7F, 0x80, 0x03, 0xFC, 0x1F, 0xE0, 0x00, 0xFF, 0x07,
  0xF8, 0x00, 0x3F, 0xC1, 0xFE, 0x00, 0x0F, 0xF0, 0x7F, 0x80, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0xEF,
  0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF,
  0xBF, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFF,
  0xFE, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00,
  0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF,
  0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xE3,
  0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF,
  0x8F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF,
  0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x8F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xC0, 0x7F, 0xC0, 0xFF, 0x81, 0xFE, 0x03, 0xFC, 0x07, 0xF0,
  0x1F, 0xE0, 0x3F, 0x80, 0x7F, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0xF0, 0x7F, 0xC1, 0xFF, 0x07, 0xFC, 0x1F, 0xF0, 0x7F,
  0xC1, 0xFF, 0x07, 0xFC, 0x1F, 0xF0, 0x7F, 0xC1, 0xFF, 0x07, 0xFC, 0x1F,
  0xF0, 0x7F, 0xC1, 0xFF, 0x07, 0xFC, 0x1F, 0xF0, 0x7F, 0xC1, 0xFF, 0x07,
  0xFC, 0x1F, 0xF0, 0x7F, 0xC1, 0xFF, 0x07, 0xFC, 0x1F, 0xF0, 0x7F, 0xC1,
  0xFF, 0x07, 0xFC, 0x1F, 0xF0, 0x7F, 0xC1, 0xFF, 0x07, 0xFC, 0x1F, 0xF0,
  0x7F, 0xC1, 0xFF, 0x07, 0xFC, 0x1F, 0xF0, 0x7F, 0xC1, 0xFF, 0x07, 0xFC,
  0x1F, 0xF0, 0x7F, 0xC1, 0xFF, 0x07, 0xFC, 0x1F, 0xF0, 0x0F, 0xF8, 0x7F,
  0xC1, 0xFE, 0x0F, 0xF0, 0x3F, 0x81, 0xFE, 0x07, 0xF0, 0x3F, 0x80, 0xFC,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFE, 0x0F, 0xF8, 0x3F,
  0xE0, 0xFF, 0x83, 0xFE, 0x0F, 0xF8, 0x3F, 0xE0, 0xFF, 0x83, 0xFE, 0x0F,
  0xF8, 0x3F, 0xE0, 0xFF, 0x83, 0xFE, 0x0F, 0xF8, 0x3F, 0xE0, 0xFF, 0x83,
  0xFE, 0x0F, 0xF8, 0x3F, 0xE0, 0xFF, 0x83, 0xFE, 0x0F, 0xF8, 0x3F, 0xE0,
  0xFF, 0x83, 0xFE, 0x0F, 0xF8, 0x3F, 0xE0, 0xFF, 0x83, 0xFE, 0x0F, 0xF8,
  0x3F, 0xE0, 0xFF, 0x83, 0xFE, 0x0F, 0xF8, 0x3F, 0xE0, 0xFF, 0x83, 0xFE,
  0x0F, 0xF8, 0x3F, 0xE0, 0xFF, 0x83, 0xFE, 0x0F, 0xF8, 0x3F, 0xE0, 0xFF,
  0x83, 0xFE, 0x00, 0x03, 0xFE, 0x00, 0x3F, 0xF8, 0x01, 0xFF, 0xC0, 0x1F,
  0xFF, 0x01, 0xFD, 0xFC, 0x0F, 0xCF, 0xE0, 0xFE, 0x3F, 0x8F, 0xE0, 0xFE,
  0xFE, 0x03, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00,
  0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE,
  0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x01,
  0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE, 0x00, 0x1F, 0xF0,
  0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F,
  0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80,
  0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F,
  0xC0, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00,
  0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE,
  0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x00,
  0xFF, 0x07, 0xFF, 0xF8, 0x3F, 0xFF, 0xC1, 0xFF, 0xFE, 0x0F, 0xFF, 0xF0,
  0x7F, 0xFF, 0x83, 0xFF, 0xFC, 0x1F, 0xFF, 0xE0, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x01, 0xFF,
  0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00,
  0xFF, 0x80, 0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8,
  0x00, 0x7F, 0xC0, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07,
  0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0,
  0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00, 0x3F,
  0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE, 0x00,
  0x1F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xFF,
  0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xC0, 0x03, 0xFE, 0x00, 0x1F, 0xF0, 0x00,
  0xFF, 0x80, 0x07, 0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0x00, 0x0F, 0xF8,
  0x00, 0x7F, 0xC0, 0x03, 0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x00, 0x01,
  0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x7F, 0xFF, 0xFF, 0xF8, 0x00, 0x1F, 0xFF,
  0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFF, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0xFF,
  0xFE, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0xFF, 0xF8,
  0x07, 0xFC, 0x00, 0x3F, 0xFE, 0x01, 0xFF, 0x00, 0x03, 0xFF, 0xC0, 0x7F,
  0xC0, 0x00, 0x7F, 0xF0, 0x1F, 0xF0, 0x00, 0x0F, 0xFE, 0x07, 0xFC, 0x00,
  0x01, 0xFF, 0x81, 0xFF, 0x00, 0x00, 0x3F, 0xE0, 0x7F, 0xC0, 0x00, 0x0F,
  0xFC, 0x1F, 0xF0, 0x00, 0x03, 0xFF, 0x07, 0xFC, 0x00, 0x00, 0xFF, 0xC1,
  0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x7F, 0xC0, 0x00, 0x07, 0xFF, 0xFF, 0xFF,
  0xF8, 0x01, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0x1F, 0xFF, 0xFF, 0xFF, 0xE0, 0x07, 0xFF, 0xFF, 0xFF, 0xF8, 0x01, 0xFF,
  0xFF, 0xFF, 0xFE, 0x00, 0x7F, 0xC1, 0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x7F,
  0xC0, 0x00, 0x07, 0xFC, 0x1F, 0xF0, 0x00, 0x01, 0xFF, 0x07, 0xFC, 0x00,
  0x00, 0x7F, 0xC1, 0xFF, 0x00, 0x00, 0x3F, 0xF0, 0x7F, 0xC0, 0x00, 0x0F,
  0xFC, 0x1F, 0xF0, 0x00, 0x03, 0xFE, 0x07, 0xFC, 0x00, 0x01, 0xFF, 0x81,
  0xFF, 0x00, 0x00, 0x7F, 0xE0, 0x7F, 0xC0, 0x00, 0x3F, 0xF0, 0x1F, 0xF0,
  0x00, 0x1F, 0xFC, 0x07, 0xFC, 0x00, 0x3F, 0xFE, 0x01, 0xFF, 0xFF, 0xFF,
  0xFF, 0x80, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0xFF, 0xE0,
  0x07, 0xFF, 0xFF, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x7F,
  0xFF, 0xFF, 0xF8, 0x00, 0x1F, 0xFF, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFF,
  0xF0, 0x00, 0x00, 0x00, 0x3E, 0x00, 0xF0, 0x00, 0x1F, 0xF0, 0x1E, 0x00,
  0x03, 0xFF, 0x87, 0xC0, 0x00, 0xFF, 0xFF, 0xF8, 0x00, 0x1F, 0xFF, 0xFE,
  0x00, 0x03, 0xC3, 0xFF, 0xC0, 0x00, 0x78, 0x1F, 0xF0, 0x00, 0x0F, 0x00,
  0x7C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x01, 0xFF,
  0xFF, 0x00, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x00,
  0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xE0,
  0x00, 0x7F, 0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF,
  0xFC, 0x00, 0x3F, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFF, 0xF8, 0x00, 0xFF,
  0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFF, 0x00,
  0x7F, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0xFE, 0xFF,
  0xE0, 0x3F, 0xFF, 0xCF, 0xFC, 0x07, 0xFF, 0xF9, 0xFF, 0xC0, 0xFF, 0xFF,
  0x1F, 0xF8, 0x1F, 0xFF, 0xE1, 0xFF, 0x83, 0xFF, 0xFC, 0x3F, 0xF8, 0x7F,
  0xFF, 0x83, 0xFF, 0x0F, 0xFF, 0xF0, 0x7F, 0xF1, 0xFF, 0xFE, 0x07, 0xFF,
  0x3F, 0xFF, 0xC0, 0x7F, 0xE7, 0xFF, 0xF8, 0x0F, 0xFE, 0xFF, 0xFF, 0x00,
  0xFF, 0xDF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xFC, 0x01, 0xFF, 0xFF, 0xFF,
  0x80, 0x1F, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0x3F, 0xFF,
  0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xFF, 0x00, 0x07,
  0xFF, 0xFF, 0xE0, 0x00, 0x7F, 0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0xFF, 0x80,
  0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF,
  0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00,
  0x00, 0x07, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x00,
  0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x0F,
  0xE0, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xC0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x00,
  0x0F, 0xFF, 0xF8, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xE0, 0x00, 0x03, 0xFF,
  0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFF,
  0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0xFF, 0xFC, 0x07, 0xFF, 0xE0, 0x3F, 0xFE, 0x00, 0x1F, 0xFE, 0x07,
  0xFF, 0x00, 0x01, 0xFF, 0xE1, 0xFF, 0xC0, 0x00, 0x1F, 0xFC, 0x3F, 0xF0,
  0x00, 0x01, 0xFF, 0xCF, 0xFC, 0x00, 0x00, 0x1F, 0xF9, 0xFF, 0x80, 0x00,
  0x03, 0xFF, 0x3F, 0xE0, 0x00, 0x00, 0x3F, 0xFF, 0xFC, 0x00, 0x00, 0x07,
  0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF,
  0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xFF, 0xF0,
  0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF,
  0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xFF, 0xF8, 0x00, 0x00, 0x0F, 0xFF, 0xFF,
  0x00, 0x00, 0x01, 0xFF, 0xBF, 0xE0, 0x00, 0x00, 0x3F, 0xF7, 0xFE, 0x00,
  0x00, 0x0F, 0xFC, 0xFF, 0xC0, 0x00, 0x01, 0xFF, 0x8F, 0xFC, 0x00, 0x00,
  0x7F, 0xF1, 0xFF, 0xC0, 0x00, 0x1F, 0xFC, 0x1F, 0xFC, 0x00, 0x07, 0xFF,
  0x83, 0xFF, 0xE0, 0x01, 0xFF, 0xE0, 0x3F, 0xFF, 0x01, 0xFF, 0xF8, 0x07,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0, 0x07, 0xFF,
  0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF,
  0xFE, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0x80,
  0x00, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x00,
  0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00,
  0x00, 0x1F, 0xE0, 0x00, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x00, 0x07, 0xF0,
  0x00, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x0F, 0xFE, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x00, 0x0F,
  0xFF, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF,
  0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF,
  0xF0, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFC, 0x07, 0xFF, 0xE0,
  0x3F, 0xFE, 0x00, 0x1F, 0xFE, 0x07, 0xFF, 0x00, 0x01, 0xFF, 0xE1, 0xFF,
  0xC0, 0x00, 0x1F, 0xFC, 0x3F, 0xF0, 0x00, 0x01, 0xFF, 0xCF, 0xFC, 0x00,
  0x00, 0x1F, 0xF9, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0x3F, 0xE0, 0x00, 0x00,
  0x3F, 0xFF, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFF,
  0x80, 0x00, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x0F,
  0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xFF,
  0xF8, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xBF, 0xE0,
  0x00, 0x00, 0x3F, 0xF7, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0xFF, 0xC0, 0x00,
  0x01, 0xFF, 0x8F, 0xFC, 0x00, 0x00, 0x7F, 0xF1, 0xFF, 0xC0, 0x00, 0x1F,
  0xFC, 0x1F, 0xFC, 0x00, 0x07, 0xFF, 0x83, 0xFF, 0xE0, 0x01, 0xFF, 0xE0,
  0x3F, 0xFF, 0x01, 0xFF, 0xF8, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x7F,
  0xFF, 0xFF, 0xFF, 0xC0, 0x07, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF,
  0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x1F, 0xFF, 0xFF,
  0x80, 0x00, 0x00, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x03, 0xFF, 0x80, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x00, 0x1F, 0xFC, 0x00, 0x00,
  0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0x00, 0xFF, 0xF8, 0x00, 0x00, 0x00,
  0x3F, 0xBF, 0x80, 0x00, 0x00, 0x07, 0xE7, 0xF0, 0x00, 0x00, 0x01, 0xFC,
  0x7F, 0x00, 0x00, 0x00, 0x7F, 0x07, 0xF0, 0x00, 0x00, 0x1F, 0xC0, 0x7F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x00,
  0x0F, 0xFF, 0xF8, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xE0, 0x00, 0x03, 0xFF,
  0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFF,
  0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0xFF, 0xFC, 0x07, 0xFF, 0xE0, 0x3F, 0xFE, 0x00, 0x1F, 0xFE, 0x07,
  0xFF, 0x00, 0x01, 0xFF, 0xE1, 0xFF, 0xC0, 0x00, 0x1F, 0xFC, 0x3F, 0xF0,
  0x00, 0x01, 0xFF, 0xCF, 0xFC, 0x00, 0x00, 0x1F, 0xF9, 0xFF, 0x80, 0x00,
  0x03, 0xFF, 0x3F, 0xE0, 0x00, 0x00, 0x3F, 0xFF, 0xFC, 0x00, 0x00, 0x07,
  0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF,
  0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xFF, 0xF0,
  0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF,
  0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xFF, 0xF8, 0x00, 0x00, 0x0F, 0xFF, 0xFF,
  0x00, 0x00, 0x01, 0xFF, 0xBF, 0xE0, 0x00, 0x00, 0x3F, 0xF7, 0xFE, 0x00,
  0x00, 0x0F, 0xFC, 0xFF, 0xC0, 0x00, 0x01, 0xFF, 0x8F, 0xFC, 0x00, 0x00,
  0x7F, 0xF1, 0xFF, 0xC0, 0x00, 0x1F, 0xFC, 0x1F, 0xFC, 0x00, 0x07, 0xFF,
  0x83, 0xFF, 0xE0, 0x01, 0xFF, 0xE0, 0x3F, 0xFF, 0x01, 0xFF, 0xF8, 0x07,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xC0, 0x07, 0xFF,
  0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF,
  0xFE, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0x80,
  0x00, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x03, 0xE0, 0x0F, 0x00,
  0x00, 0x01, 0xFF, 0x01, 0xE0, 0x00, 0x00, 0x3F, 0xF8, 0x7C, 0x00, 0x00,
  0x0F, 0xFF, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0xFF, 0xE0, 0x00, 0x00, 0x3C,
  0x3F, 0xFC, 0x00, 0x00, 0x07, 0x81, 0xFF, 0x00, 0x00, 0x00, 0xF0, 0x07,
  0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x00,
  0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0x00, 0x00, 0x1F,
  0xFF, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFF,
  0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF,
  0xF8, 0x07, 0xFF, 0xE0, 0x3F, 0xFF, 0x01, 0xFF, 0xF0, 0x00, 0xFF, 0xF0,
  0x3F, 0xF8, 0x00, 0x0F, 0xFF, 0x0F, 0xFE, 0x00, 0x00, 0xFF, 0xE1, 0xFF,
  0x80, 0x00, 0x0F, 0xFE, 0x7F, 0xE0, 0x00, 0x00, 0xFF, 0xCF, 0xFC, 0x00,
  0x00, 0x1F, 0xF9, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xE0, 0x00, 0x00,
  0x3F, 0xFF, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x7F,
  0xFF, 0xE0, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFF,
  0x80, 0x00, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x0F,
  0xFF, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xFF,
  0xF8, 0x00, 0x00, 0x0F, 0xFD, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xBF, 0xF0,
  0x00, 0x00, 0x7F, 0xE7, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0x7F, 0xE0, 0x00,
  0x03, 0xFF, 0x8F, 0xFE, 0x00, 0x00, 0xFF, 0xE0, 0xFF, 0xE0, 0x00, 0x3F,
  0xFC, 0x1F, 0xFF, 0x00, 0x0F, 0xFF, 0x01, 0xFF, 0xF8, 0x0F, 0xFF, 0xC0,
  0x3F, 0xFF, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x3F,
  0xFF, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0xFC,
  0x00, 0x00, 0x00, 0x1F, 0xFC, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0xFF, 0x00,
  0x00, 0x03, 0xFC, 0x1F, 0xE0, 0x00, 0x00, 0x7F, 0x83, 0xFC, 0x00, 0x00,
  0x0F, 0xF0, 0x7F, 0x80, 0x00, 0x01, 0xFE, 0x0F, 0xF0, 0x00, 0x00, 0x3F,
  0xC1, 0xFE, 0x00, 0x00, 0x07, 0xF8, 0x3F, 0xC0, 0x00, 0x00, 0xFF, 0x07,
  0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x00,
  0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0x00, 0x00, 0x1F,
  0xFF, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFF,
  0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xFF,
  0xF8, 0x07, 0xFF, 0xE0, 0x3F, 0xFF, 0x01, 0xFF, 0xF0, 0x00, 0xFF, 0xF0,
  0x3F, 0xF8, 0x00, 0x0F, 0xFF, 0x0F, 0xFE, 0x00, 0x00, 0xFF, 0xE1, 0xFF,
  0x80, 0x00, 0x0F, 0xFE, 0x7F, 0xE0, 0x00, 0x00, 0xFF, 0xCF, 0xFC, 0x00,
  0x00, 0x1F, 0xF9, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xE0, 0x00, 0x00,
  0x3F, 0xFF, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x7F,
  0xFF, 0xE0, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0xFF,
  0x80, 0x00, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x0F,
  0xFF, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xFF,
  0xF8, 0x00, 0x00, 0x0F, 0xFD, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xBF, 0xF0,
  0x00, 0x00, 0x7F, 0xE7, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0x7F, 0xE0, 0x00,
  0x03, 0xFF, 0x8F, 0xFE, 0x00, 0x00, 0xFF, 0xE0, 0xFF, 0xE0, 0x00, 0x3F,
  0xFC, 0x1F, 0xFF, 0x00, 0x0F, 0xFF, 0x01, 0xFF, 0xF8, 0x0F, 0xFF, 0xC0,
  0x3F, 0xFF, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x3F,
  0xFF, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x1F, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x07, 0xFF, 0xFC,
  0x00, 0x00, 0x00, 0x1F, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x18,
  0x00, 0x07, 0x00, 0xF0, 0x00, 0x3E, 0x0F, 0xE0, 0x01, 0xFC, 0x7F, 0xC0,
  0x0F, 0xFB, 0xFF, 0x80, 0x7F, 0xF7, 0xFF, 0x03, 0xFF, 0x8F, 0xFE, 0x1F,
  0xFC, 0x1F, 0xFC, 0xFF, 0xE0, 0x3F, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xF8,
  0x00, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xF0, 0x00,
  0x07, 0xFF, 0x80, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x07,
  0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF,
  0xFF, 0xC0, 0x7F, 0xF3, 0xFF, 0x83, 0xFF, 0x87, 0xFF, 0x0F, 0xFC, 0x0F,
  0xFE, 0x7F, 0xE0, 0x1F, 0xFD, 0xFF, 0x00, 0x3F, 0xF3, 0xF8, 0x00, 0x7F,
  0x07, 0xC0, 0x00, 0xF8, 0x0E, 0x00, 0x01, 0xC0, 0x10, 0x00, 0x02, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x1F, 0xFC, 0x00, 0x78, 0x00, 0x07, 0xFF, 0xFC, 0x07, 0xE0, 0x01,
  0xFF, 0xFF, 0xFC, 0x7F, 0x80, 0x1F, 0xFF, 0xFF, 0xF3, 0xF8, 0x03, 0xFF,
  0xFF, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF,
  0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xFF, 0xFC, 0x01, 0xFF, 0xF8, 0x0F,
  0xFF, 0xC0, 0x1F, 0xFE, 0x00, 0x0F, 0xFF, 0x01, 0xFF, 0xE0, 0x00, 0x3F,
  0xF8, 0x0F, 0xFE, 0x00, 0x01, 0xFF, 0xE0, 0x7F, 0xE0, 0x00, 0x1F, 0xFF,
  0x07, 0xFE, 0x00, 0x01, 0xFF, 0xFC, 0x3F, 0xF0, 0x00, 0x1F, 0xFF, 0xE1,
  0xFF, 0x00, 0x01, 0xFF, 0xFF, 0x1F, 0xF8, 0x00, 0x1F, 0xEF, 0xFC, 0xFF,
  0xC0, 0x01, 0xFE, 0x7F, 0xE7, 0xFC, 0x00, 0x1F, 0xE1, 0xFF, 0x3F, 0xE0,
  0x01, 0xFE, 0x0F, 0xF9, 0xFF, 0x00, 0x1F, 0xE0, 0x7F, 0xCF, 0xF8, 0x00,
  0xFE, 0x03, 0xFE, 0x7F, 0xC0, 0x0F, 0xE0, 0x1F, 0xF3, 0xFE, 0x00, 0xFF,
  0x00, 0xFF, 0x9F, 0xF0, 0x0F, 0xF0, 0x07, 0xFC, 0xFF, 0x80, 0xFF, 0x00,
  0x3F, 0xE7, 0xFC, 0x0F, 0xF0, 0x01, 0xFF, 0x3F, 0xE0, 0xFF, 0x00, 0x0F,
  0xF9, 0xFF, 0x8F, 0xF0, 0x00, 0x7F, 0xCF, 0xFC, 0xFF, 0x00, 0x07, 0xFE,
  0x7F, 0xEF, 0xF0, 0x00, 0x3F, 0xF1, 0xFF, 0x7F, 0x00, 0x01, 0xFF, 0x8F,
  0xFF, 0xF0, 0x00, 0x1F, 0xF8, 0x7F, 0xFF, 0x80, 0x00, 0xFF, 0xC1, 0xFF,
  0xF8, 0x00, 0x0F, 0xFE, 0x0F, 0xFF, 0x80, 0x00, 0xFF, 0xE0, 0x3F, 0xF8,
  0x00, 0x0F, 0xFF, 0x01, 0xFF, 0xE0, 0x00, 0xFF, 0xF0, 0x07, 0xFF, 0xE0,
  0x3F, 0xFF, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xFF,
  0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xFF, 0xFF,
  0x80, 0x3F, 0xDF, 0xFF, 0xFF, 0xF0, 0x03, 0xFC, 0x3F, 0xFF, 0xFF, 0x00,
  0x0F, 0xC0, 0x7F, 0xFF, 0xE0, 0x00, 0x3C, 0x00, 0x7F, 0xF0, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x07,
  0xFC, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00,
  0x00, 0x7F, 0x00, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x00, 0xFE, 0x00,
  0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF,
  0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07,
  0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC,
  0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF,
  0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03,
  0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00,
  0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE,
  0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF,
  0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01,
  0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF,
  0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x03, 0xFF,
  0xFF, 0xE0, 0x00, 0x7F, 0xEF, 0xFE, 0x00, 0x3F, 0xF9, 0xFF, 0xF0, 0x1F,
  0xFF, 0x1F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xF8, 0x3F, 0xFF,
  0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xE0, 0x03,
  0xFF, 0xFF, 0xF8, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x00, 0x3F, 0xFC, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x07,
  0xF8, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00,
  0x0F, 0xF0, 0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0x00,
  0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF,
  0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF,
  0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00,
  0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF,
  0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F,
  0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00,
  0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0,
  0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF,
  0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F,
  0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0,
  0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0xE0, 0x00, 0x7F, 0xEF,
  0xFE, 0x00, 0x3F, 0xF9, 0xFF, 0xF0, 0x1F, 0xFF, 0x1F, 0xFF, 0xFF, 0xFF,
  0xE3, 0xFF, 0xFF, 0xFF, 0xF8, 0x3F, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF,
  0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0xF8, 0x00, 0x1F,
  0xFF, 0xFC, 0x00, 0x00, 0x3F, 0xFC, 0x00, 0x00, 0x00, 0x07, 0xFC, 0x00,
  0x00, 0x01, 0xFF, 0xC0, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0x0F, 0xFF,
  0x80, 0x00, 0x03, 0xFB, 0xF8, 0x00, 0x00, 0x7E, 0x7F, 0x00, 0x00, 0x1F,
  0xC7, 0xF0, 0x00, 0x07, 0xF0, 0x7F, 0x00, 0x01, 0xFC, 0x07, 0xF0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00,
  0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0,
  0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F,
  0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00,
  0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF,
  0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07,
  0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00,
  0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC,
  0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF,
  0xFE, 0x00, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03,
  0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFE, 0x00,
  0x03, 0xFF, 0xFF, 0xE0, 0x00, 0x7F, 0xEF, 0xFE, 0x00, 0x3F, 0xF9, 0xFF,
  0xF0, 0x1F, 0xFF, 0x1F, 0xFF, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xFF, 0xF8,
  0x3F, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF,
  0xE0, 0x03, 0xFF, 0xFF, 0xF8, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x00, 0x3F,
  0xFC, 0x00, 0x00, 0x01, 0xFE, 0x0F, 0xF0, 0x00, 0x3F, 0xC1, 0xFE, 0x00,
  0x07, 0xF8, 0x3F, 0xC0, 0x00, 0xFF, 0x07, 0xF8, 0x00, 0x1F, 0xE0, 0xFF,
  0x00, 0x03, 0xFC, 0x1F, 0xE0, 0x00, 0x7F, 0x83, 0xFC, 0x00, 0x0F, 0xF0,
  0x7F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x03, 0xFF,
  0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01,
  0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF,
  0x00, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF,
  0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00,
  0xFF, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00,
  0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF,
  0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF,
  0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00,
  0x7F, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0xFF, 0xFF,
  0xC0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x7F,
  0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xFF, 0x00, 0x03, 0xFF, 0x7F, 0xF0, 0x01,
  0xFF, 0xCF, 0xFF, 0x80, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF,
  0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xFE, 0x01,
  0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0xE0,
  0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x00, 0x01, 0xFE,
  0x00, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00,
  0x01, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x3F, 0xF8, 0x00, 0x00, 0x7F, 0xF7, 0xFE, 0x00, 0x00, 0x1F, 0xF9,
  0xFF, 0xC0, 0x00, 0x0F, 0xFE, 0x3F, 0xF8, 0x00, 0x03, 0xFF, 0x07, 0xFE,
  0x00, 0x01, 0xFF, 0x81, 0xFF, 0xC0, 0x00, 0xFF, 0xE0, 0x3F, 0xF0, 0x00,
  0x3F, 0xF0, 0x07, 0xFE, 0x00, 0x1F, 0xF8, 0x01, 0xFF, 0xC0, 0x07, 0xFE,
  0x00, 0x3F, 0xF0, 0x03, 0xFF, 0x00, 0x07, 0xFE, 0x01, 0xFF, 0x80, 0x01,
  0xFF, 0x80, 0x7F, 0xE0, 0x00, 0x3F, 0xF0, 0x3F, 0xF0, 0x00, 0x0F, 0xFE,
  0x0F, 0xFC, 0x00, 0x01, 0xFF, 0x87, 0xFE, 0x00, 0x00, 0x3F, 0xF1, 0xFF,
  0x00, 0x00, 0x0F, 0xFC, 0xFF, 0xC0, 0x00, 0x01, 0xFF, 0xFF, 0xE0, 0x00,
  0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x00, 0x01,
  0xFF, 0xFE, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x0F, 0xFF,
  0xC0, 0x00, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x7F, 0xF8, 0x00,
  0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x0F,
  0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0,
  0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00,
  0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x00,
  0x3F, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00,
  0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE,
  0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F,
  0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00,
  0x3F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0xFF,
  0xF0, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF,
  0xFF, 0xF3, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0x80, 0x07, 0xFF, 0xBF, 0xE0,
  0x00, 0x7F, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF,
  0x80, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x07, 0xFF,
  0xFE, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0x3F,
  0xFF, 0xF8, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0F, 0xFE, 0xFF, 0x80, 0x07,
  0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xF3, 0xFF, 0xFF,
  0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF,
  0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x7F,
  0xC0, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x0F, 0xFF,
  0xFF, 0x00, 0x1F, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFF, 0x80, 0x3F, 0xFF,
  0xFF, 0xC0, 0x7F, 0xF0, 0xFF, 0xC0, 0x7F, 0xC0, 0x7F, 0xC0, 0x7F, 0xC0,
  0x7F, 0xC0, 0x7F, 0x80, 0x7F, 0xC0, 0x7F, 0x80, 0x7F, 0xC0, 0xFF, 0x80,
  0x7F, 0xC0, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80, 0xFF, 0x80,
  0xFF, 0x00, 0xFF, 0x81, 0xFF, 0x00, 0xFF, 0x81, 0xFE, 0x00, 0xFF, 0x83,
  0xFE, 0x00, 0xFF, 0x83, 0xFC, 0x00, 0xFF, 0x87, 0xFC, 0x00, 0xFF, 0x87,
  0xFE, 0x00, 0xFF, 0x87, 0xFE, 0x00, 0xFF, 0x87, 0xFF, 0x00, 0xFF, 0x83,
  0xFF, 0x80, 0xFF, 0x81, 0xFF, 0xC0, 0xFF, 0x81, 0xFF, 0xE0, 0xFF, 0x80,
  0xFF, 0xF0, 0xFF, 0x80, 0x7F, 0xF8, 0xFF, 0x80, 0x3F, 0xFC, 0xFF, 0x80,
  0x1F, 0xFC, 0xFF, 0x80, 0x0F, 0xFE, 0xFF, 0x80, 0x07, 0xFE, 0xFF, 0x80,
  0x03, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0x80, 0xC1, 0xFF, 0xFF, 0x83,
  0xE3, 0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0x8F, 0xFF, 0xFE, 0xFF, 0x8F,
  0xFF, 0xFC, 0xFF, 0x87, 0xFF, 0xFC, 0xFF, 0x83, 0xFF, 0xF8, 0xFF, 0x80,
  0xFF, 0xE0, 0x00, 0x00, 0x3F, 0x80, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x01, 0xFC,
  0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x03, 0xF8,
  0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xE0,
  0x00, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xF0,
  0x3F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xC1, 0xFF, 0x81, 0xFF, 0xC3,
  0xFC, 0x00, 0xFF, 0x8F, 0xF8, 0x01, 0xFF, 0x01, 0xE0, 0x03, 0xFE, 0x00,
  0x00, 0x03, 0xFC, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x01, 0xFF, 0xF0, 0x00,
  0x7F, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0x83, 0xFF,
  0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFE, 0x1F, 0xFF, 0xF3, 0xFC, 0x7F, 0xFC,
  0x07, 0xF8, 0xFF, 0xC0, 0x0F, 0xF3, 0xFE, 0x00, 0x3F, 0xE7, 0xFC, 0x00,
  0x7F, 0xCF, 0xF8, 0x00, 0xFF, 0x9F, 0xF0, 0x03, 0xFF, 0x3F, 0xF0, 0x0F,
  0xFE, 0x3F, 0xF0, 0x7F, 0xFC, 0x7F, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF,
  0xF0, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0x9F, 0xE1, 0xFF, 0xFE, 0x3F,
  0xC0, 0xFF, 0xF0, 0x7F, 0xC0, 0x7F, 0x80, 0x00, 0x00, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x3F, 0xC0,
  0x00, 0x00, 0x7F, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xF8, 0x00,
  0x00, 0x0F, 0xE0, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0x80, 0x00,
  0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xF0, 0x0F,
  0xFF, 0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xC1, 0xFF,
  0x81, 0xFF, 0xC3, 0xFC, 0x00, 0xFF, 0x8F, 0xF8, 0x01, 0xFF, 0x01, 0xE0,
  0x03, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x01,
  0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF,
  0xFF, 0x83, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFE, 0x1F, 0xFF, 0xF3,
  0xFC, 0x7F, 0xFC, 0x07, 0xF8, 0xFF, 0xC0, 0x0F, 0xF3, 0xFE, 0x00, 0x3F,
  0xE7, 0xFC, 0x00, 0x7F, 0xCF, 0xF8, 0x00, 0xFF, 0x9F, 0xF0, 0x03, 0xFF,
  0x3F, 0xF0, 0x0F, 0xFE, 0x3F, 0xF0, 0x7F, 0xFC, 0x7F, 0xFF, 0xFF, 0xF8,
  0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0x9F, 0xE1,
  0xFF, 0xFE, 0x3F, 0xC0, 0xFF, 0xF0, 0x7F, 0xC0, 0x7F, 0x80, 0x00, 0x00,
  0x00, 0x1F, 0xF0, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x00, 0xFF, 0xE0, 0x00,
  0x03, 0xFF, 0xE0, 0x00, 0x0F, 0xEF, 0xE0, 0x00, 0x1F, 0x9F, 0xC0, 0x00,
  0x7F, 0x1F, 0xC0, 0x01, 0xFC, 0x1F, 0xC0, 0x07, 0xF0, 0x1F, 0xC0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03,
  0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0xF0, 0x03, 0xFF,
  0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF,
  0xFF, 0xC1, 0xFF, 0x81, 0xFF, 0xC3, 0xFC, 0x00, 0xFF, 0x8F, 0xF8, 0x01,
  0xFF, 0x01, 0xE0, 0x03, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x1F,
  0xF8, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF,
  0xC0, 0x7F, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFE,
  0x1F, 0xFF, 0xF3, 0xFC, 0x7F, 0xFC, 0x07, 0xF8, 0xFF, 0xC0, 0x0F, 0xF3,
  0xFE, 0x00, 0x3F, 0xE7, 0xFC, 0x00, 0x7F, 0xCF, 0xF8, 0x00, 0xFF, 0x9F,
  0xF0, 0x03, 0xFF, 0x3F, 0xF0, 0x0F, 0xFE, 0x3F, 0xF0, 0x7F, 0xFC, 0x7F,
  0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF,
  0xFF, 0x9F, 0xE1, 0xFF, 0xFE, 0x3F, 0xC0, 0xFF, 0xF0, 0x7F, 0xC0, 0x7F,
  0x80, 0x00, 0x00, 0x00, 0xF8, 0x03, 0xC0, 0x07, 0xFC, 0x07, 0x80, 0x0F,
  0xFE, 0x1F, 0x00, 0x3F, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0xF0,
  0xFF, 0xF0, 0x01, 0xE0, 0x7F, 0xC0, 0x03, 0xC0, 0x1F, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF,
  0xC0, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF,
  0xF8, 0x07, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF,
  0xE0, 0xFF, 0xC0, 0xFF, 0xE1, 0xFE, 0x00, 0x7F, 0xC7, 0xFC, 0x00, 0xFF,
  0x80, 0xF0, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x0F, 0xFC,
  0x00, 0x00, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xE0,
  0x3F, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0x87, 0xFF, 0xFF, 0xFF, 0x0F,
  0xFF, 0xF9, 0xFE, 0x3F, 0xFE, 0x03, 0xFC, 0x7F, 0xE0, 0x07, 0xF9, 0xFF,
  0x00, 0x1F, 0xF3, 0xFE, 0x00, 0x3F, 0xE7, 0xFC, 0x00, 0x7F, 0xCF, 0xF8,
  0x01, 0xFF, 0x9F, 0xF8, 0x07, 0xFF, 0x1F, 0xF8, 0x3F, 0xFE, 0x3F, 0xFF,
  0xFF, 0xFC, 0x7F, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF,
  0xCF, 0xF0, 0xFF, 0xFF, 0x1F, 0xE0, 0x7F, 0xF8, 0x3F, 0xE0, 0x3F, 0xC0,
  0x00, 0x00, 0x07, 0xF8, 0x3F, 0xC0, 0x0F, 0xF0, 0x7F, 0x80, 0x1F, 0xE0,
  0xFF, 0x00, 0x3F, 0xC1, 0xFE, 0x00, 0x7F, 0x83, 0xFC, 0x00, 0xFF, 0x07,
  0xF8, 0x01, 0xFE, 0x0F, 0xF0, 0x03, 0xFC, 0x1F, 0xE0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xC0,
  0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xF8,
  0x07, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF, 0xE0,
  0xFF, 0xC0, 0xFF, 0xE1, 0xFE, 0x00, 0x7F, 0xC7, 0xFC, 0x00, 0xFF, 0x80,
  0xF0, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0x00,
  0x00, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xE0, 0x3F,
  0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0x87, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF,
  0xF9, 0xFE, 0x3F, 0xFE, 0x03, 0xFC, 0x7F, 0xE0, 0x07, 0xF9, 0xFF, 0x00,
  0x1F, 0xF3, 0xFE, 0x00, 0x3F, 0xE7, 0xFC, 0x00, 0x7F, 0xCF, 0xF8, 0x01,
  0xFF, 0x9F, 0xF8, 0x07, 0xFF, 0x1F, 0xF8, 0x3F, 0xFE, 0x3F, 0xFF, 0xFF,
  0xFC, 0x7F, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF, 0xCF,
  0xF0, 0xFF, 0xFF, 0x1F, 0xE0, 0x7F, 0xF8, 0x3F, 0xE0, 0x3F, 0xC0, 0x00,
  0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x7F, 0xC0,
  0x00, 0x01, 0xE3, 0xC0, 0x00, 0x03, 0x83, 0x80, 0x00, 0x0F, 0x07, 0x00,
  0x00, 0x1E, 0x0F, 0x00, 0x00, 0x1C, 0x1C, 0x00, 0x00, 0x3C, 0x78, 0x00,
  0x00, 0x3F, 0xE0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x3E, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x1F, 0xFC, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x07, 0xFF, 0xFF, 0x80, 0x1F,
  0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0xFF, 0x81, 0xFF, 0xFF, 0xFF, 0x07, 0xFF,
  0xFF, 0xFE, 0x0F, 0xFC, 0x0F, 0xFE, 0x1F, 0xE0, 0x07, 0xFC, 0x7F, 0xC0,
  0x0F, 0xF8, 0x0F, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00,
  0xFF, 0xC0, 0x00, 0x0F, 0xFF, 0x80, 0x03, 0xFF, 0xFF, 0x00, 0x7F, 0xFF,
  0xFE, 0x03, 0xFF, 0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF, 0xFF,
  0xF0, 0xFF, 0xFF, 0x9F, 0xE3, 0xFF, 0xE0, 0x3F, 0xC7, 0xFE, 0x00, 0x7F,
  0x9F, 0xF0, 0x01, 0xFF, 0x3F, 0xE0, 0x03, 0xFE, 0x7F, 0xC0, 0x07, 0xFC,
  0xFF, 0x80, 0x1F, 0xF9, 0xFF, 0x80, 0x7F, 0xF1, 0xFF, 0x83, 0xFF, 0xE3,
  0xFF, 0xFF, 0xFF, 0xC7, 0xFF, 0xFF, 0xFF, 0x87, 0xFF, 0xFF, 0xFF, 0x87,
  0xFF, 0xFC, 0xFF, 0x0F, 0xFF, 0xF1, 0xFE, 0x07, 0xFF, 0x83, 0xFE, 0x03,
  0xFC, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x01, 0xFE, 0x00, 0x00, 0x7F, 0xFF,
  0x83, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xFB, 0xFF, 0xFE, 0x00, 0x3F, 0xFF,
  0xFF, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x07, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0xFF,
  0xC1, 0xFF, 0xFC, 0x1F, 0xFC, 0x7F, 0xC0, 0x1F, 0xFE, 0x01, 0xFF, 0x1F,
  0xE0, 0x03, 0xFF, 0x00, 0x3F, 0xE0, 0x78, 0x00, 0xFF, 0x80, 0x07, 0xF8,
  0x00, 0x00, 0x3F, 0xE0, 0x01, 0xFE, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x7F,
  0x80, 0x00, 0xFF, 0xFE, 0x00, 0x1F, 0xF0, 0x03, 0xFF, 0xFF, 0x00, 0x07,
  0xFC, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFD, 0xFF, 0xFF, 0x3F, 0xFF, 0xFF, 0xFF, 0x7F, 0xF8, 0x0F, 0xF0,
  0x00, 0x00, 0x3F, 0xF0, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0xFF,
  0x80, 0x00, 0x03, 0xFC, 0x00, 0x3F, 0xE0, 0x01, 0xE0, 0xFF, 0x00, 0x1F,
  0xFC, 0x00, 0x7F, 0xBF, 0xE0, 0x07, 0xFF, 0x00, 0x3F, 0xEF, 0xF8, 0x03,
  0xFF, 0xE0, 0x1F, 0xFB, 0xFF, 0x83, 0xFF, 0xFE, 0x0F, 0xFC, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x87, 0xFF,
  0xFF, 0xEF, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF, 0xE0, 0x1F,
  0xFF, 0xF0, 0x3F, 0xFF, 0xF0, 0x03, 0xFF, 0xF0, 0x03, 0xFF, 0xF0, 0x00,
  0x1F, 0xE0, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x03, 0xFF,
  0xF8, 0x00, 0x3F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF,
  0xE0, 0x7F, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xFF, 0x0F, 0xFE, 0x0F, 0xFE,
  0x7F, 0xE0, 0x0F, 0xF9, 0xFF, 0x00, 0x1F, 0xE7, 0xFC, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xF0, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x3C, 0x3F, 0xE0, 0x00, 0xFF,
  0x7F, 0xC0, 0x07, 0xFD, 0xFF, 0x00, 0x1F, 0xF7, 0xFE, 0x00, 0xFF, 0x8F,
  0xFE, 0x0F, 0xFE, 0x3F, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF, 0xC0, 0xFF,
  0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0x80, 0x03, 0xFF,
  0xF8, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x3F, 0xFE,
  0x00, 0x00, 0x81, 0xFC, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x0F, 0xC0,
  0x00, 0x00, 0x3F, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x1F, 0xFF, 0xC0, 0x00,
  0x7F, 0xFE, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x03,
  0xF8, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x07,
  0xF0, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F, 0xFF,
  0x80, 0x00, 0x7F, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF,
  0xC0, 0x1F, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xC1, 0xFF, 0xC1, 0xFF,
  0x83, 0xFE, 0x01, 0xFF, 0x8F, 0xFC, 0x01, 0xFF, 0x1F, 0xF0, 0x01, 0xFE,
  0x3F, 0xE0, 0x03, 0xFE, 0xFF, 0x80, 0x03, 0xFD, 0xFF, 0x00, 0x07, 0xFB,
  0xFE, 0x00, 0x0F, 0xF7, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xDF,
  0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF,
  0x80, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x03, 0xFC,
  0x00, 0x3E, 0x07, 0xFC, 0x00, 0x7F, 0xCF, 0xFC, 0x01, 0xFF, 0x8F, 0xFC,
  0x03, 0xFE, 0x1F, 0xFC, 0x1F, 0xFC, 0x1F, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF,
  0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFE, 0x00, 0x3F, 0xFF,
  0xF0, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x3F,
  0xC0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x03, 0xF8,
  0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x00,
  0x00, 0x1F, 0xFF, 0x80, 0x00, 0x7F, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xC0,
  0x0F, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xC1,
  0xFF, 0xC1, 0xFF, 0x83, 0xFE, 0x01, 0xFF, 0x8F, 0xFC, 0x01, 0xFF, 0x1F,
  0xF0, 0x01, 0xFE, 0x3F, 0xE0, 0x03, 0xFE, 0xFF, 0x80, 0x03, 0xFD, 0xFF,
  0x00, 0x07, 0xFB, 0xFE, 0x00, 0x0F, 0xF7, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF,
  0xFF, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0x7F, 0xFF,
  0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x03, 0xFC, 0x00, 0x3E, 0x07, 0xFC, 0x00, 0x7F, 0xCF, 0xFC, 0x01,
  0xFF, 0x8F, 0xFC, 0x03, 0xFE, 0x1F, 0xFC, 0x1F, 0xFC, 0x1F, 0xFF, 0xFF,
  0xF0, 0x3F, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFE,
  0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x07, 0xFC, 0x00,
  0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x00, 0xFF, 0xE0,
  0x00, 0x03, 0xFF, 0xE0, 0x00, 0x0F, 0xEF, 0xE0, 0x00, 0x1F, 0x9F, 0xC0,
  0x00, 0x7F, 0x1F, 0xC0, 0x01, 0xFC, 0x1F, 0xC0, 0x07, 0xF0, 0x1F, 0xC0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0xFE, 0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x7F, 0xFF, 0xC0, 0x03,
  0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0xC0, 0x7F,
  0xFF, 0xFF, 0xC1, 0xFF, 0xC1, 0xFF, 0x83, 0xFE, 0x01, 0xFF, 0x8F, 0xFC,
  0x01, 0xFF, 0x1F, 0xF0, 0x01, 0xFE, 0x3F, 0xE0, 0x03, 0xFE, 0xFF, 0x80,
  0x03, 0xFD, 0xFF, 0x00, 0x07, 0xFB, 0xFE, 0x00, 0x0F, 0xF7, 0xFF, 0xFF,
  0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF,
  0xFF, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x3E, 0x07, 0xFC, 0x00, 0x7F,
  0xCF, 0xFC, 0x01, 0xFF, 0x8F, 0xFC, 0x03, 0xFE, 0x1F, 0xFC, 0x1F, 0xFC,
  0x1F, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0x80,
  0x3F, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xC0, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x3F, 0xC0, 0x0F, 0xF0, 0x7F, 0x80,
  0x1F, 0xE0, 0xFF, 0x00, 0x3F, 0xC1, 0xFE, 0x00, 0x7F, 0x83, 0xFC, 0x00,
  0xFF, 0x07, 0xF8, 0x01, 0xFE, 0x0F, 0xF0, 0x03, 0xFC, 0x1F, 0xE0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0xE0, 0x07, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF,
  0xFF, 0xE0, 0xFF, 0xE0, 0xFF, 0xC1, 0xFF, 0x00, 0xFF, 0xC7, 0xFE, 0x00,
  0xFF, 0x8F, 0xF8, 0x00, 0xFF, 0x1F, 0xF0, 0x01, 0xFF, 0x7F, 0xC0, 0x01,
  0xFE, 0xFF, 0x80, 0x03, 0xFD, 0xFF, 0x00, 0x07, 0xFB, 0xFF, 0xFF, 0xFF,
  0xF7, 0xFF, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF,
  0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x1F, 0x03, 0xFE, 0x00, 0x3F, 0xE7,
  0xFE, 0x00, 0xFF, 0xC7, 0xFE, 0x01, 0xFF, 0x0F, 0xFE, 0x0F, 0xFE, 0x0F,
  0xFF, 0xFF, 0xF8, 0x1F, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0xC0, 0x1F,
  0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x03,
  0xFE, 0x00, 0x00, 0x7F, 0xC0, 0xFF, 0x81, 0xFE, 0x03, 0xFC, 0x07, 0xF0,
  0x1F, 0xE0, 0x3F, 0x80, 0x7F, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x3F, 0xC0, 0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x3F,
  0xC0, 0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x3F, 0xC0, 0xFF, 0x03, 0xFC, 0x0F,
  0xF0, 0x3F, 0xC0, 0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x3F, 0xC0, 0xFF, 0x03,
  0xFC, 0x0F, 0xF0, 0x3F, 0xC0, 0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x3F, 0xC0,
  0xFF, 0x03, 0xFC, 0x0F, 0xF0, 0x3F, 0xC0, 0xFF, 0x03, 0xFC, 0x0F, 0xF0,
  0x0F, 0xF8, 0x7F, 0xC1, 0xFE, 0x0F, 0xF0, 0x3F, 0x81, 0xFE, 0x07, 0xF0,
  0x3F, 0x80, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFE,
  0x07, 0xF8, 0x1F, 0xE0, 0x7F, 0x81, 0xFE, 0x07, 0xF8, 0x1F, 0xE0, 0x7F,
  0x81, 0xFE, 0x07, 0xF8, 0x1F, 0xE0, 0x7F, 0x81, 0xFE, 0x07, 0xF8, 0x1F,
  0xE0, 0x7F, 0x81, 0xFE, 0x07, 0xF8, 0x1F, 0xE0, 0x7F, 0x81, 0xFE, 0x07,
  0xF8, 0x1F, 0xE0, 0x7F, 0x81, 0xFE, 0x07, 0xF8, 0x1F, 0xE0, 0x7F, 0x81,
  0xFE, 0x07, 0xF8, 0x1F, 0xE0, 0x7F, 0x81, 0xFE, 0x00, 0x03, 0xFE, 0x00,
  0x3F, 0xF8, 0x01, 0xFF, 0xC0, 0x1F, 0xFF, 0x01, 0xFD, 0xFC, 0x0F, 0xCF,
  0xE0, 0xFE, 0x3F, 0x8F, 0xE0, 0xFE, 0xFE, 0x03, 0xF8, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x07, 0xF8,
  0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03,
  0xFC, 0x00, 0x1F, 0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F, 0xC0,
  0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F,
  0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00,
  0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xE0, 0x00, 0xFF,
  0x00, 0x07, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00,
  0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xE0, 0x00, 0xFF, 0x00, 0xFF, 0x07,
  0xFF, 0xF8, 0x3F, 0xFF, 0xC1, 0xFF, 0xFE, 0x0F, 0xFF, 0xF0, 0x7F, 0xFF,
  0x83, 0xFF, 0xFC, 0x1F, 0xFF, 0xE0, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0xFF, 0x00, 0x07,
  0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80,
  0x03, 0xFC, 0x00, 0x1F, 0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F,
  0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00,
  0x1F, 0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE,
  0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xE0, 0x00,
  0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF0,
  0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0xFF, 0x80,
  0x80, 0x00, 0x3F, 0xE1, 0xE0, 0x00, 0x07, 0xFB, 0xF0, 0x00, 0x01, 0xFF,
  0xF0, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x7F,
  0xFC, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x01, 0xFC, 0x7F, 0x80, 0x00, 0x78,
  0x1F, 0xE0, 0x00, 0x30, 0x07, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x03,
  0xF8, 0xFF, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0xE0, 0x0F,
  0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xFC, 0x0F, 0xFF, 0xFF, 0xFF, 0x0F,
  0xFF, 0xFF, 0xFF, 0x87, 0xFF, 0x83, 0xFF, 0xE7, 0xFF, 0x00, 0xFF, 0xF3,
  0xFF, 0x00, 0x1F, 0xFB, 0xFF, 0x00, 0x0F, 0xFD, 0xFF, 0x80, 0x03, 0xFF,
  0xFF, 0x80, 0x01, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x3F,
  0xFF, 0xF0, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x07,
  0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00,
  0xFF, 0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x7F, 0xCF, 0xF8, 0x00,
  0x7F, 0xE7, 0xFE, 0x00, 0x3F, 0xF1, 0xFF, 0x80, 0x3F, 0xF0, 0xFF, 0xF0,
  0x7F, 0xF0, 0x3F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xF8, 0x03, 0xFF,
  0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xF8, 0x00, 0x07,
  0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0xF8, 0x03, 0xC0, 0x0F,
  0xF8, 0x0F, 0x00, 0x3F, 0xF8, 0x7C, 0x01, 0xFF, 0xFF, 0xF0, 0x07, 0xFF,
  0xFF, 0x80, 0x1E, 0x1F, 0xFE, 0x00, 0x78, 0x1F, 0xF0, 0x01, 0xE0, 0x0F,
  0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x3F, 0x80, 0xFF, 0x07, 0xFF, 0x83, 0xFC, 0x3F, 0xFF, 0x8F,
  0xF1, 0xFF, 0xFF, 0x3F, 0xCF, 0xFF, 0xFE, 0xFF, 0x7F, 0xFF, 0xFB, 0xFF,
  0xFF, 0xFF, 0xEF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xE0,
  0x07, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xF0, 0x01,
  0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F,
  0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF,
  0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE,
  0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80,
  0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F, 0xFF, 0xE0, 0x00,
  0xFF, 0xFF, 0x80, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x3F,
  0xFF, 0xE0, 0x00, 0xFF, 0xFF, 0x80, 0x03, 0xFC, 0x01, 0xFF, 0x00, 0x00,
  0x00, 0x7F, 0xC0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x07, 0xF8, 0x00,
  0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x3F, 0x80,
  0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0x80, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x07, 0xFF,
  0xFF, 0xC0, 0x07, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xFC, 0x07, 0xFF,
  0xFF, 0xFF, 0x07, 0xFF, 0x83, 0xFF, 0xC3, 0xFF, 0x00, 0x7F, 0xE3, 0xFF,
  0x00, 0x1F, 0xF9, 0xFF, 0x00, 0x07, 0xFD, 0xFF, 0x80, 0x03, 0xFE, 0xFF,
  0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x3F, 0xFF,
  0xE0, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x07, 0xFF,
  0xFC, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF,
  0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xF0, 0x00, 0x3F,
  0xE7, 0xFC, 0x00, 0x1F, 0xF3, 0xFF, 0x00, 0x1F, 0xF9, 0xFF, 0xC0, 0x1F,
  0xF8, 0x7F, 0xF8, 0x3F, 0xF8, 0x1F, 0xFF, 0xFF, 0xFC, 0x0F, 0xFF, 0xFF,
  0xFC, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xFF,
  0xF8, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00,
  0x7F, 0x80, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00,
  0x1F, 0xC0, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x07, 0xFF, 0xFF, 0xC0, 0x07, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xFC,
  0x07, 0xFF, 0xFF, 0xFF, 0x07, 0xFF, 0x83, 0xFF, 0xC3, 0xFF, 0x00, 0x7F,
  0xE3, 0xFF, 0x00, 0x1F, 0xF9, 0xFF, 0x00, 0x07, 0xFD, 0xFF, 0x80, 0x03,
  0xFE, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00,
  0x3F, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xF8, 0x00,
  0x07, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xFF, 0x80,
  0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x3F, 0xFF, 0xF0,
  0x00, 0x3F, 0xE7, 0xFC, 0x00, 0x1F, 0xF3, 0xFF, 0x00, 0x1F, 0xF9, 0xFF,
  0xC0, 0x1F, 0xF8, 0x7F, 0xF8, 0x3F, 0xF8, 0x1F, 0xFF, 0xFF, 0xFC, 0x0F,
  0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xFC, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x3F, 0xE0, 0x00,
  0x00, 0x0F, 0xF8, 0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x07, 0xFF, 0x00,
  0x00, 0x07, 0xFF, 0xC0, 0x00, 0x07, 0xF7, 0xF0, 0x00, 0x03, 0xF3, 0xF8,
  0x00, 0x03, 0xF8, 0xFE, 0x00, 0x03, 0xF8, 0x3F, 0x80, 0x03, 0xF8, 0x0F,
  0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x07, 0xFF,
  0xFE, 0x00, 0x07, 0xFF, 0xFF, 0xC0, 0x07, 0xFF, 0xFF, 0xF0, 0x07, 0xFF,
  0xFF, 0xFC, 0x07, 0xFF, 0xFF, 0xFF, 0x07, 0xFF, 0x83, 0xFF, 0xC3, 0xFF,
  0x00, 0x7F, 0xE3, 0xFF, 0x00, 0x1F, 0xF9, 0xFF, 0x00, 0x07, 0xFD, 0xFF,
  0x80, 0x03, 0xFE, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF,
  0xE0, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x0F, 0xFF,
  0xF8, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x01, 0xFF,
  0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x3F,
  0xFF, 0xF0, 0x00, 0x3F, 0xE7, 0xFC, 0x00, 0x1F, 0xF3, 0xFF, 0x00, 0x1F,
  0xF9, 0xFF, 0xC0, 0x1F, 0xF8, 0x7F, 0xF8, 0x3F, 0xF8, 0x1F, 0xFF, 0xFF,
  0xFC, 0x0F, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xFC, 0x00, 0xFF, 0xFF,
  0xFC, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x7C, 0x01, 0xE0, 0x00, 0xFF, 0x80, 0xF0, 0x00, 0x7F,
  0xF0, 0xF8, 0x00, 0x7F, 0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xFC, 0x00, 0x1E,
  0x1F, 0xFE, 0x00, 0x0F, 0x03, 0xFE, 0x00, 0x07, 0x80, 0x3E, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFC, 0x00,
  0x0F, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xF8,
  0x0F, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0x07, 0xFF, 0x87, 0xFE, 0x00, 0xFF,
  0xC7, 0xFE, 0x00, 0x3F, 0xF3, 0xFE, 0x00, 0x0F, 0xFB, 0xFF, 0x00, 0x07,
  0xFD, 0xFF, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00,
  0x7F, 0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xF0, 0x00,
  0x0F, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF, 0x00,
  0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0,
  0x00, 0x7F, 0xCF, 0xF8, 0x00, 0x3F, 0xE7, 0xFE, 0x00, 0x3F, 0xF3, 0xFF,
  0x80, 0x3F, 0xF0, 0xFF, 0xF0, 0x7F, 0xF0, 0x3F, 0xFF, 0xFF, 0xF8, 0x1F,
  0xFF, 0xFF, 0xF8, 0x07, 0xFF, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xF8, 0x00,
  0x3F, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xC0, 0x00,
  0x03, 0xFC, 0x1F, 0xE0, 0x01, 0xFE, 0x0F, 0xF0, 0x00, 0xFF, 0x07, 0xF8,
  0x00, 0x7F, 0x83, 0xFC, 0x00, 0x3F, 0xC1, 0xFE, 0x00, 0x1F, 0xE0, 0xFF,
  0x00, 0x0F, 0xF0, 0x7F, 0x80, 0x07, 0xF8, 0x3F, 0xC0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x0F, 0xFF,
  0xFF, 0x80, 0x0F, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF,
  0xFF, 0xFE, 0x0F, 0xFF, 0x07, 0xFF, 0x87, 0xFE, 0x00, 0xFF, 0xC7, 0xFE,
  0x00, 0x3F, 0xF3, 0xFE, 0x00, 0x0F, 0xFB, 0xFF, 0x00, 0x07, 0xFD, 0xFF,
  0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF,
  0xC0, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x0F, 0xFF,
  0xF8, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x01, 0xFF,
  0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0xE0, 0x00, 0x7F,
  0xCF, 0xF8, 0x00, 0x3F, 0xE7, 0xFE, 0x00, 0x3F, 0xF3, 0xFF, 0x80, 0x3F,
  0xF0, 0xFF, 0xF0, 0x7F, 0xF0, 0x3F, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF, 0xFF,
  0xF8, 0x07, 0xFF, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x3F, 0xFF,
  0xF0, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x1F,
  0xF0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0xFF,
  0x80, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x07, 0xFC,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00,
  0x3F, 0xE0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F,
  0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x03,
  0xFE, 0x03, 0xC0, 0x0F, 0xFF, 0xE3, 0xF0, 0x1F, 0xFF, 0xFB, 0xF0, 0x1F,
  0xFF, 0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF, 0xFF, 0xF8, 0x1F,
  0xFF, 0xFF, 0xFC, 0x1F, 0xFE, 0x0F, 0xFF, 0x0F, 0xFC, 0x01, 0xFF, 0x8F,
  0xFC, 0x01, 0xFF, 0xE7, 0xFC, 0x01, 0xFF, 0xF7, 0xFC, 0x00, 0xFF, 0xFB,
  0xFE, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFD, 0xFF, 0xFF, 0x00, 0xFC, 0xFF,
  0xFF, 0x80, 0xFC, 0x3F, 0xFF, 0xC0, 0xFC, 0x1F, 0xFF, 0xE0, 0x7C, 0x0F,
  0xFF, 0xF0, 0x7E, 0x07, 0xFF, 0xF8, 0x7E, 0x03, 0xFF, 0xFE, 0x7E, 0x01,
  0xFF, 0xFF, 0x7E, 0x01, 0xFF, 0xFF, 0xFE, 0x00, 0xFF, 0xBF, 0xFE, 0x00,
  0x7F, 0xDF, 0xFF, 0x00, 0x7F, 0xCF, 0xFF, 0x00, 0x7F, 0xE3, 0xFF, 0x00,
  0x7F, 0xE1, 0xFF, 0xE0, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF,
  0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF,
  0xFF, 0xF0, 0x1F, 0x8F, 0xFF, 0xE0, 0x07, 0x80, 0xFF, 0x80, 0x01, 0x80,
  0x00, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F,
  0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x07, 0xE0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07,
  0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xFD, 0xFF, 0x00,
  0x3F, 0xF7, 0xFE, 0x01, 0xFF, 0xDF, 0xFC, 0x1F, 0xFF, 0x7F, 0xFF, 0xFF,
  0xFC, 0xFF, 0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0xBF, 0xC7, 0xFF, 0xFC, 0xFF,
  0x0F, 0xFF, 0xE3, 0xFC, 0x1F, 0xFE, 0x0F, 0xF0, 0x0F, 0xE0, 0x00, 0x00,
  0x00, 0x01, 0xFF, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x01, 0xFE, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07,
  0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xFD, 0xFF, 0x00, 0x3F, 0xF7, 0xFE,
  0x01, 0xFF, 0xDF, 0xFC, 0x1F, 0xFF, 0x7F, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF,
  0xFF, 0xF3, 0xFF, 0xFF, 0xBF, 0xC7, 0xFF, 0xFC, 0xFF, 0x0F, 0xFF, 0xE3,
  0xFC, 0x1F, 0xFE, 0x0F, 0xF0, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x1F, 0xF0,
  0x00, 0x00, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0x80, 0x00, 0x1F, 0xFF, 0x00,
  0x00, 0xFE, 0xFE, 0x00, 0x03, 0xF3, 0xF8, 0x00, 0x1F, 0xC7, 0xF0, 0x00,
  0xFE, 0x0F, 0xE0, 0x07, 0xF0, 0x1F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFE, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07,
  0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07,
  0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x0F, 0xFD, 0xFF, 0x00, 0x3F, 0xF7, 0xFE, 0x01, 0xFF, 0xDF,
  0xFC, 0x1F, 0xFF, 0x7F, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xF3, 0xFF,
  0xFF, 0xBF, 0xC7, 0xFF, 0xFC, 0xFF, 0x0F, 0xFF, 0xE3, 0xFC, 0x1F, 0xFE,
  0x0F, 0xF0, 0x0F, 0xE0, 0x00, 0x00, 0x07, 0xF8, 0x3F, 0xC0, 0x1F, 0xE0,
  0xFF, 0x00, 0x7F, 0x83, 0xFC, 0x01, 0xFE, 0x0F, 0xF0, 0x07, 0xF8, 0x3F,
  0xC0, 0x1F, 0xE0, 0xFF, 0x00, 0x7F, 0x83, 0xFC, 0x01, 0xFE, 0x0F, 0xF0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07,
  0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF,
  0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xFE, 0x00,
  0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xE0, 0x03, 0xFF, 0x7F, 0xC0, 0x0F,
  0xFD, 0xFF, 0x80, 0x7F, 0xF7, 0xFF, 0x07, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF,
  0x3F, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xEF, 0xF1, 0xFF, 0xFF, 0x3F, 0xC3,
  0xFF, 0xF8, 0xFF, 0x07, 0xFF, 0x83, 0xFC, 0x03, 0xF8, 0x00, 0x00, 0x00,
  0x00, 0x7F, 0xC0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF0, 0x00,
  0x00, 0x07, 0xF8, 0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0xFF, 0x00,
  0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x07, 0xE0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x1F, 0xF7, 0xFC,
  0x00, 0x07, 0xF9, 0xFF, 0x00, 0x03, 0xFE, 0x3F, 0xE0, 0x00, 0xFF, 0x8F,
  0xF8, 0x00, 0x3F, 0xC1, 0xFE, 0x00, 0x1F, 0xF0, 0x7F, 0xC0, 0x07, 0xFC,
  0x1F, 0xF0, 0x01, 0xFE, 0x03, 0xFC, 0x00, 0xFF, 0x80, 0xFF, 0x80, 0x3F,
  0xE0, 0x3F, 0xE0, 0x0F, 0xF0, 0x07, 0xF8, 0x07, 0xFC, 0x01, 0xFF, 0x01,
  0xFE, 0x00, 0x3F, 0xC0, 0x7F, 0x80, 0x0F, 0xF0, 0x3F, 0xE0, 0x03, 0xFE,
  0x0F, 0xF0, 0x00, 0x7F, 0x83, 0xFC, 0x00, 0x1F, 0xE1, 0xFF, 0x00, 0x07,
  0xFC, 0x7F, 0x80, 0x00, 0xFF, 0x1F, 0xE0, 0x00, 0x3F, 0xCF, 0xF8, 0x00,
  0x07, 0xFB, 0xFC, 0x00, 0x01, 0xFE, 0xFF, 0x00, 0x00, 0x7F, 0xFF, 0xC0,
  0x00, 0x0F, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x00, 0xFF, 0xFC,
  0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0xC0, 0x00, 0x00, 0xFF,
  0xE0, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00,
  0x0F, 0xF8, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0x80, 0x00,
  0x00, 0xFF, 0xC0, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x07, 0xFF, 0xF8, 0x00,
  0x01, 0xFF, 0xFE, 0x00, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x1F, 0xFF, 0x80,
  0x00, 0x07, 0xFF, 0xC0, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x00, 0x7F, 0xC0,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x81,
  0xFC, 0x00, 0xFF, 0x87, 0xFF, 0x00, 0xFF, 0x9F, 0xFF, 0xC0, 0xFF, 0xBF,
  0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF,
  0xFF, 0xFC, 0xFF, 0xFC, 0x1F, 0xFC, 0xFF, 0xF0, 0x0F, 0xFE, 0xFF, 0xE0,
  0x07, 0xFE, 0xFF, 0xC0, 0x03, 0xFE, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0x80,
  0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80,
  0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80,
  0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80,
  0x01, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xC0, 0x03, 0xFE, 0xFF, 0xC0,
  0x03, 0xFE, 0xFF, 0xE0, 0x07, 0xFE, 0xFF, 0xF0, 0x0F, 0xFE, 0xFF, 0xFC,
  0x1F, 0xFC, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF,
  0xFF, 0xF0, 0xFF, 0xBF, 0xFF, 0xE0, 0xFF, 0x9F, 0xFF, 0xC0, 0xFF, 0x87,
  0xFF, 0x00, 0xFF, 0x81, 0xFC, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0x01, 0xFE, 0x0F, 0xF0, 0x00, 0x7F,
  0x83, 0xFC, 0x00, 0x1F, 0xE0, 0xFF, 0x00, 0x07, 0xF8, 0x3F, 0xC0, 0x01,
  0xFE, 0x0F, 0xF0, 0x00, 0x7F, 0x83, 0xFC, 0x00, 0x1F, 0xE0, 0xFF, 0x00,
  0x07, 0xF8, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00,
  0x7F, 0xDF, 0xF0, 0x00, 0x1F, 0xE7, 0xFC, 0x00, 0x0F, 0xF8, 0xFF, 0x80,
  0x03, 0xFE, 0x3F, 0xE0, 0x00, 0xFF, 0x07, 0xF8, 0x00, 0x7F, 0xC1, 0xFF,
  0x00, 0x1F, 0xF0, 0x7F, 0xC0, 0x07, 0xF8, 0x0F, 0xF0, 0x03, 0xFE, 0x03,
  0xFE, 0x00, 0xFF, 0x80, 0xFF, 0x80, 0x3F, 0xC0, 0x1F, 0xE0, 0x1F, 0xF0,
  0x07, 0xFC, 0x07, 0xF8, 0x00, 0xFF, 0x01, 0xFE, 0x00, 0x3F, 0xC0, 0xFF,
  0x80, 0x0F, 0xF8, 0x3F, 0xC0, 0x01, 0xFE, 0x0F, 0xF0, 0x00, 0x7F, 0x87,
  0xFC, 0x00, 0x1F, 0xF1, 0xFE, 0x00, 0x03, 0xFC, 0x7F, 0x80, 0x00, 0xFF,
  0x3F, 0xE0, 0x00, 0x1F, 0xEF, 0xF0, 0x00, 0x07, 0xFB, 0xFC, 0x00, 0x01,
  0xFF, 0xFF, 0x00, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x0F, 0xFF, 0xE0, 0x00,
  0x03, 0xFF, 0xF0, 0x00, 0x00, 0x7F, 0xFC, 0x00, 0x00, 0x1F, 0xFF, 0x00,
  0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x00, 0x3F, 0xF8,
  0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x00, 0xFF,
  0xC0, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x07,
  0xFE, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x1F,
  0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFC, 0x00, 0x00,
  0x7F, 0xFE, 0x00, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x07, 0xFF, 0x80, 0x00,
  0x01, 0xFF, 0x00, 0x00, 0x00 };

const GFXglyph myFont32pt8bGlyphs[] PROGMEM = {
  {     0,   0,   0,  18,    0,    1 },   // 0x20 ' '
  {     0,   9,  45,  21,    6,  -44 },   // 0x21 '!'
  {    51,  24,  16,  30,    3,  -44 },   // 0x22 '"'
  {    99,  33,  45,  35,    1,  -44 },   // 0x23 '#'
  {   285,  30,  55,  35,    2,  -47 },   // 0x24 '$'
  {   492,  50,  48,  56,    3,  -45 },   // 0x25 '%'
  {   792,  41,  47,  46,    3,  -45 },   // 0x26 '&'
  {  1033,   9,  16,  15,    3,  -44 },   // 0x27 '''
  {  1051,  16,  58,  21,    3,  -44 },   // 0x28 '('
  {  1167,  16,  58,  21,    2,  -44 },   // 0x29 ')'
  {  1283,  22,  21,  25,    1,  -44 },   // 0x2A '*'
  {  1341,  31,  32,  37,    3,  -37 },   // 0x2B '+'
  {  1465,   9,  19,  18,    4,   -8 },   // 0x2C ','
  {  1487,  17,   8,  21,    4,  -19 },   // 0x2D '-'
  {  1504,   8,   9,  18,    5,   -8 },   // 0x2E '.'
  {  1513,  18,  45,  18,    0,  -44 },   // 0x2F '/'
  {  1615,  29,  46,  35,    3,  -44 },   // 0x30 '0'
  {  1782,  20,  45,  35,    5,  -44 },   // 0x31 '1'
  {  1895,  30,  45,  35,    2,  -44 },   // 0x32 '2'
  {  2064,  30,  46,  35,    2,  -44 },   // 0x33 '3'
  {  2237,  33,  44,  35,    1,  -43 },   // 0x34 '4'
  {  2419,  30,  45,  35,    3,  -43 },   // 0x35 '5'
  {  2588,  30,  46,  35,    3,  -44 },   // 0x36 '6'
  {  2761,  29,  44,  35,    3,  -43 },   // 0x37 '7'
  {  2921,  29,  46,  35,    3,  -44 },   // 0x38 '8'
  {  3088,  30,  46,  35,    2,  -44 },   // 0x39 '9'
  {  3261,   9,  33,  21,    6,  -32 },   // 0x3A ':'
  {  3299,  10,  43,  21,    5,  -32 },   // 0x3B ';'
  {  3353,  31,  34,  37,    3,  -38 },   // 0x3C '<'
  {  3485,  31,  22,  37,    3,  -32 },   // 0x3D '='
  {  3571,  31,  34,  37,    3,  -38 },   // 0x3E '>'
  {  3703,  33,  46,  38,    3,  -45 },   // 0x3F '?'
  {  3893,  59,  61,  61,    2,  -45 },   // 0x40 '@'
  {  4343,  45,  45,  46,    0,  -44 },   // 0x41 'A'
  {  4597,  37,  45,  46,    5,  -44 },   // 0x42 'B'
  {  4806,  39,  46,  46,    3,  -45 },   // 0x43 'C'
  {  5031,  37,  45,  46,    5,  -44 },   // 0x44 'D'
  {  5240,  34,  45,  42,    5,  -44 },   // 0x45 'E'
  {  5432,  31,  45,  38,    5,  -44 },   // 0x46 'F'
  {  5607,  42,  47,  49,    3,  -45 },   // 0x47 'G'
  {  5854,  36,  45,  46,    5,  -44 },   // 0x48 'H'
  {  6057,   9,  45,  18,    4,  -44 },   // 0x49 'I'
  {  6108,  29,  46,  35,    1,  -44 },   // 0x4A 'J'
  {  6275,  40,  45,  46,    5,  -44 },   // 0x4B 'K'
  {  6500,  32,  45,  38,    5,  -44 },   // 0x4C 'L'
  {  6680,  44,  45,  52,    4,  -44 },   // 0x4D 'M'
  {  6928,  35,  45,  46,    5,  -44 },   // 0x4E 'N'
  {  7125,  43,  47,  49,    3,  -45 },   // 0x4F 'O'
  {  7378,  34,  45,  42,    5,  -44 },   // 0x50 'P'
  {  7570,  45,  51,  49,    3,  -45 },   // 0x51 'Q'
  {  7857,  40,  45,  46,    5,  -44 },   // 0x52 'R'
  {  8082,  37,  47,  42,    2,  -45 },   // 0x53 'S'
  {  8300,  36,  45,  38,    1,  -44 },   // 0x54 'T'
  {  8503,  35,  46,  46,    5,  -44 },   // 0x55 'U'
  {  8705,  42,  45,  42,    0,  -44 },   // 0x56 'V'
  {  8942,  59,  45,  59,    0,  -44 },   // 0x57 'W'
  {  9274,  42,  45,  42,    0,  -44 },   // 0x58 'X'
  {  9511,  42,  45,  42,    0,  -44 },   // 0x59 'Y'
  {  9748,  36,  45,  38,    1,  -44 },   // 0x5A 'Z'
  {  9951,  16,  58,  21,    4,  -44 },   // 0x5B '['
  { 10067,  18,  45,  18,    0,  -44 },   // 0x5C '\'
  { 10169,  15,  58,  21,    1,  -44 },   // 0x5D ']'
  { 10278,  29,  24,  37,    4,  -45 },   // 0x5E '^'
  { 10365,  36,   7,  35,   -1,    6 },   // 0x5F '_'
  { 10397,  14,   9,  21,    1,  -44 },   // 0x60 '`'
  { 10413,  31,  35,  35,    2,  -33 },   // 0x61 'a'
  { 10549,  32,  46,  38,    4,  -44 },   // 0x62 'b'
  { 10733,  30,  35,  35,    3,  -33 },   // 0x63 'c'
  { 10865,  31,  46,  38,    3,  -44 },   // 0x64 'd'
  { 11044,  31,  35,  35,    2,  -33 },   // 0x65 'e'
  { 11180,  22,  46,  21,    1,  -45 },   // 0x66 'f'
  { 11307,  31,  48,  38,    3,  -33 },   // 0x67 'g'
  { 11493,  30,  45,  38,    4,  -44 },   // 0x68 'h'
  { 11662,   8,  45,  18,    5,  -44 },   // 0x69 'i'
  { 11707,  16,  59,  18,   -3,  -44 },   // 0x6A 'j'
  { 11825,  30,  45,  35,    4,  -44 },   // 0x6B 'k'
  { 11994,   8,  45,  18,    5,  -44 },   // 0x6C 'l'
  { 12039,  48,  34,  56,    4,  -33 },   // 0x6D 'm'
  { 12243,  30,  34,  38,    4,  -33 },   // 0x6E 'n'
  { 12371,  33,  35,  38,    3,  -33 },   // 0x6F 'o'
  { 12516,  32,  47,  38,    4,  -33 },   // 0x70 'p'
  { 12704,  32,  47,  38,    3,  -33 },   // 0x71 'q'
  { 12892,  21,  34,  25,    4,  -33 },   // 0x72 'r'
  { 12982,  31,  35,  35,    1,  -33 },   // 0x73 's'
  { 13118,  19,  45,  21,    1,  -43 },   // 0x74 't'
  { 13225,  30,  34,  38,    4,  -32 },   // 0x75 'u'
  { 13353,  34,  33,  35,    0,  -32 },   // 0x76 'v'
  { 13494,  49,  33,  49,    0,  -32 },   // 0x77 'w'
  { 13697,  34,  33,  35,    0,  -32 },   // 0x78 'x'
  { 13838,  34,  47,  35,    0,  -32 },   // 0x79 'y'
  { 14038,  29,  33,  32,    1,  -32 },   // 0x7A 'z'
  { 14158,  21,  60,  25,    2,  -45 },   // 0x7B '{'
  { 14316,   7,  59,  18,    5,  -44 },   // 0x7C '|'
  { 14368,  21,  60,  25,    1,  -45 },   // 0x7D '}'
  { 14526,  33,  12,  37,    2,  -27 },   // 0x7E '~'
  { 14576,  31,  39,  47,    8,  -38 },   // 0x7F
  { 14728,  31,  39,  47,    8,  -38 },   // 0x80
  { 14880,  31,  39,  47,    8,  -38 },   // 0x81
  { 15032,  31,  39,  47,    8,  -38 },   // 0x82
  { 15184,  31,  39,  47,    8,  -38 },   // 0x83
  { 15336,  31,  39,  47,    8,  -38 },   // 0x84
  { 15488,  31,  39,  47,    8,  -38 },   // 0x85
  { 15640,  31,  39,  47,    8,  -38 },   // 0x86
  { 15792,  31,  39,  47,    8,  -38 },   // 0x87
  { 15944,  31,  39,  47,    8,  -38 },   // 0x88
  { 16096,  31,  39,  47,    8,  -38 },   // 0x89
  { 16248,  31,  39,  47,    8,  -38 },   // 0x8A
  { 16400,  31,  39,  47,    8,  -38 },   // 0x8B
  { 16552,  31,  39,  47,    8,  -38 },   // 0x8C
  { 16704,  31,  39,  47,    8,  -38 },   // 0x8D
  { 16856,  31,  39,  47,    8,  -38 },   // 0x8E
  { 17008,  31,  39,  47,    8,  -38 },   // 0x8F
  { 17160,  31,  39,  47,    8,  -38 },   // 0x90
  { 17312,  31,  39,  47,    8,  -38 },   // 0x91
  { 17464,  31,  39,  47,    8,  -38 },   // 0x92
  { 17616,  31,  39,  47,    8,  -38 },   // 0x93
  { 17768,  31,  39,  47,    8,  -38 },   // 0x94
  { 17920,  31,  39,  47,    8,  -38 },   // 0x95
  { 18072,  31,  39,  47,    8,  -38 },   // 0x96
  { 18224,  31,  39,  47,    8,  -38 },   // 0x97
  { 18376,  31,  39,  47,    8,  -38 },   // 0x98
  { 18528,  31,  39,  47,    8,  -38 },   // 0x99
  { 18680,  31,  39,  47,    8,  -38 },   // 0x9A
  { 18832,  31,  39,  47,    8,  -38 },   // 0x9B
  { 18984,  31,  39,  47,    8,  -38 },   // 0x9C
  { 19136,  31,  39,  47,    8,  -38 },   // 0x9D
  { 19288,  31,  39,  47,    8,  -38 },   // 0x9E
  { 19440,  31,  39,  47,    8,  -38 },   // 0x9F
  { 19592,   0,   0,  18,    0,    1 },   // 0xA0
  { 19592,   9,  46,  21,    6,  -32 },   // 0xA1
  { 19644,  30,  58,  35,    3,  -44 },   // 0xA2
  { 19862,  34,  47,  35,    0,  -45 },   // 0xA3
  { 20062,  32,  33,  35,    1,  -38 },   // 0xA4
  { 20194,  35,  45,  35,    0,  -44 },   // 0xA5
  { 20391,   7,  59,  18,    5,  -44 },   // 0xA6
  { 20443,  31,  59,  35,    2,  -45 },   // 0xA7
  { 20672,  21,   8,  21,    0,  -44 },   // 0xA8
  { 20693,  47,  47,  46,    0,  -45 },   // 0xA9
  { 20970,  21,  23,  23,    1,  -45 },   // 0xAA
  { 21031,  29,  29,  35,    3,  -30 },   // 0xAB
  { 21137,  31,  22,  37,    3,  -32 },   // 0xAC
  { 21223,  17,   8,  21,    4,  -19 },   // 0xAD
  { 21240,  47,  47,  46,    0,  -45 },   // 0xAE
  { 21517,  36,   7,  35,   -1,  -54 },   // 0xAF
  { 21549,  19,  19,  25,    3,  -45 },   // 0xB0
  { 21595,  31,  43,  35,    2,  -42 },   // 0xB1
  { 21762,  18,  23,  21,    1,  -45 },   // 0xB2
  { 21814,  19,  23,  21,    1,  -45 },   // 0xB3
  { 21869,  14,   9,  21,    6,  -44 },   // 0xB4
  { 21885,  30,  46,  36,    3,  -32 },   // 0xB5
  { 22058,  35,  57,  35,    0,  -44 },   // 0xB6
  { 22308,   9,   9,  21,    6,  -26 },   // 0xB7
  { 22319,  17,  11,  21,    1,    1 },   // 0xB8
  { 22343,  12,  23,  21,    3,  -45 },   // 0xB9
  { 22378,  21,  23,  23,    1,  -45 },   // 0xBA
  { 22439,  29,  29,  35,    3,  -30 },   // 0xBB
  { 22545,  49,  46,  53,    3,  -44 },   // 0xBC
  { 22827,  48,  47,  53,    3,  -45 },   // 0xBD
  { 23109,  51,  47,  53,    1,  -45 },   // 0xBE
  { 23409,  33,  47,  38,    3,  -32 },   // 0xBF
  { 23603,  45,  58,  46,    0,  -57 },   // 0xC0
  { 23930,  45,  58,  46,    0,  -57 },   // 0xC1
  { 24257,  45,  58,  46,    0,  -57 },   // 0xC2
  { 24584,  45,  57,  46,    0,  -56 },   // 0xC3
  { 24905,  45,  57,  46,    0,  -56 },   // 0xC4
  { 25226,  45,  54,  46,    0,  -53 },   // 0xC5
  { 25530,  63,  45,  63,   -3,  -44 },   // 0xC6
  { 25885,  39,  57,  46,    3,  -45 },   // 0xC7
  { 26163,  34,  58,  42,    5,  -57 },   // 0xC8
  { 26410,  34,  58,  42,    5,  -57 },   // 0xC9
  { 26657,  34,  58,  42,    5,  -57 },   // 0xCA
  { 26904,  34,  57,  42,    5,  -56 },   // 0xCB
  { 27147,  14,  58,  18,   -1,  -57 },   // 0xCC
  { 27249,  14,  58,  18,    4,  -57 },   // 0xCD
  { 27351,  21,  58,  18,   -1,  -57 },   // 0xCE
  { 27504,  21,  57,  18,   -2,  -56 },   // 0xCF
  { 27654,  42,  45,  46,    0,  -44 },   // 0xD0
  { 27891,  35,  57,  46,    5,  -56 },   // 0xD1
  { 28141,  43,  59,  49,    3,  -57 },   // 0xD2
  { 28459,  43,  59,  49,    3,  -57 },   // 0xD3
  { 28777,  43,  59,  49,    3,  -57 },   // 0xD4
  { 29095,  43,  58,  49,    3,  -56 },   // 0xD5
  { 29407,  43,  58,  49,    3,  -56 },   // 0xD6
  { 29719,  30,  30,  37,    3,  -36 },   // 0xD7
  { 29832,  45,  50,  49,    2,  -47 },   // 0xD8
  { 30114,  35,  59,  46,    5,  -57 },   // 0xD9
  { 30373,  35,  59,  46,    5,  -57 },   // 0xDA
  { 30632,  35,  59,  46,    5,  -57 },   // 0xDB
  { 30891,  35,  58,  46,    5,  -56 },   // 0xDC
  { 31145,  42,  58,  42,    0,  -57 },   // 0xDD
  { 31450,  34,  45,  42,    5,  -44 },   // 0xDE
  { 31642,  32,  47,  38,    4,  -45 },   // 0xDF
  { 31830,  31,  47,  35,    2,  -45 },   // 0xE0
  { 32013,  31,  47,  35,    2,  -45 },   // 0xE1
  { 32196,  31,  47,  35,    2,  -45 },   // 0xE2
  { 32379,  31,  46,  35,    2,  -44 },   // 0xE3
  { 32558,  31,  46,  35,    2,  -44 },   // 0xE4
  { 32737,  31,  50,  35,    2,  -48 },   // 0xE5
  { 32931,  50,  35,  56,    3,  -33 },   // 0xE6
  { 33150,  30,  45,  35,    3,  -33 },   // 0xE7
  { 33319,  31,  47,  35,    2,  -45 },   // 0xE8
  { 33502,  31,  47,  35,    2,  -45 },   // 0xE9
  { 33685,  31,  47,  35,    2,  -45 },   // 0xEA
  { 33868,  31,  46,  35,    2,  -44 },   // 0xEB
  { 34047,  14,  46,  18,   -1,  -45 },   // 0xEC
  { 34128,  14,  46,  18,    4,  -45 },   // 0xED
  { 34209,  21,  46,  18,   -2,  -45 },   // 0xEE
  { 34330,  21,  45,  18,   -2,  -44 },   // 0xEF
  { 34449,  33,  46,  38,    3,  -44 },   // 0xF0
  { 34639,  30,  45,  38,    4,  -44 },   // 0xF1
  { 34808,  33,  47,  38,    3,  -45 },   // 0xF2
  { 35002,  33,  47,  38,    3,  -45 },   // 0xF3
  { 35196,  33,  47,  38,    3,  -45 },   // 0xF4
  { 35390,  33,  46,  38,    3,  -44 },   // 0xF5
  { 35580,  33,  46,  38,    3,  -44 },   // 0xF6
  { 35770,  31,  32,  35,    2,  -37 },   // 0xF7
  { 35894,  33,  38,  38,    3,  -35 },   // 0xF8
  { 36051,  30,  47,  38,    4,  -45 },   // 0xF9
  { 36228,  30,  47,  38,    4,  -45 },   // 0xFA
  { 36405,  30,  47,  38,    4,  -45 },   // 0xFB
  { 36582,  30,  46,  38,    4,  -44 },   // 0xFC
  { 36755,  34,  60,  35,    0,  -45 },   // 0xFD
  { 37010,  32,  58,  38,    4,  -44 },   // 0xFE
  { 37242,  34,  59,  35,    0,  -44 } }; // 0xFF

const GFXfont myFont32pt8b PROGMEM = {
  (uint8_t  *)myFont32pt8bBitmaps,
  (GFXglyph *)myFont32pt8bGlyphs,
  0x20, 0xFF, 72 };

// Approx. 39068 bytes
