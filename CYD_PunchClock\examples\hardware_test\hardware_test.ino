/*
 * Hardware Test Sketch
 * ESP32-2432S028R (CYD) + MFRC522 Component Testing
 * 
 * This sketch tests all hardware components individually
 * Use this to verify your hardware setup before running the main firmware
 */

#include <TFT_eSPI.h>
#include <XPT2046_Touchscreen.h>
#include <MFRC522.h>
#include <SPI.h>
#include <SD.h>
#include <FastLED.h>

// Pin definitions for ESP32-2432S028R
#define TFT_BL      21
#define TOUCH_CS    33
#define TOUCH_IRQ   36
#define SD_CS       5
#define RGB_LED_PIN 4
#define SPEAKER_PIN 26
#define LDR_PIN     34
#define RFID_SDA    22
#define RFID_RST    16

// Component objects
TFT_eSPI tft = TFT_eSPI();
XPT2046_Touchscreen touch(TOUCH_CS, TOUCH_IRQ);
MFRC522 mfrc522(RFID_SDA, RFID_RST);
CRGB leds[1];

// Test state
int currentTest = 0;
unsigned long lastTestTime = 0;
bool testPassed[8] = {false, false, false, false, false, false, false, false};

void setup() {
  Serial.begin(115200);
  Serial.println("\n=== ESP32-2432S028R Hardware Test ===");
  
  // Initialize display
  tft.init();
  tft.setRotation(0);
  tft.fillScreen(TFT_BLACK);
  
  // Set up backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  
  // Show welcome message
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("Hardware Test");
  tft.setTextSize(1);
  tft.setCursor(10, 40);
  tft.println("ESP32-2432S028R + MFRC522");
  
  delay(2000);
  
  // Start tests
  runAllTests();
}

void loop() {
  // Show test results
  displayTestResults();
  
  // Check for touch to restart tests
  if (touch.touched()) {
    TS_Point p = touch.getPoint();
    if (p.z > 600) {
      delay(200); // Debounce
      Serial.println("Restarting tests...");
      runAllTests();
    }
  }
  
  delay(100);
}

void runAllTests() {
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_YELLOW);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("Running Tests...");
  
  // Reset test results
  for (int i = 0; i < 8; i++) {
    testPassed[i] = false;
  }
  
  // Test 1: Display
  testPassed[0] = testDisplay();
  delay(1000);
  
  // Test 2: Touch
  testPassed[1] = testTouch();
  delay(1000);
  
  // Test 3: SD Card
  testPassed[2] = testSDCard();
  delay(1000);
  
  // Test 4: RFID
  testPassed[3] = testRFID();
  delay(1000);
  
  // Test 5: RGB LED
  testPassed[4] = testRGBLED();
  delay(1000);
  
  // Test 6: Speaker
  testPassed[5] = testSpeaker();
  delay(1000);
  
  // Test 7: Light Sensor
  testPassed[6] = testLightSensor();
  delay(1000);
  
  // Test 8: WiFi
  testPassed[7] = testWiFi();
  delay(1000);
  
  Serial.println("All tests completed!");
}

bool testDisplay() {
  Serial.println("Testing Display...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_GREEN);
  tft.setTextSize(1);
  tft.setCursor(10, 10);
  tft.println("Test 1: Display");
  
  // Draw test patterns
  tft.fillRect(10, 30, 50, 50, TFT_RED);
  tft.fillRect(70, 30, 50, 50, TFT_GREEN);
  tft.fillRect(130, 30, 50, 50, TFT_BLUE);
  
  tft.drawCircle(120, 150, 30, TFT_WHITE);
  tft.drawLine(10, 200, 230, 200, TFT_YELLOW);
  
  tft.setCursor(10, 220);
  tft.println("Display test patterns shown");
  
  Serial.println("Display: PASS");
  return true;
}

bool testTouch() {
  Serial.println("Testing Touch...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_CYAN);
  tft.setCursor(10, 10);
  tft.println("Test 2: Touch Screen");
  tft.setCursor(10, 30);
  tft.println("Touch anywhere to test");
  
  touch.begin();
  touch.setRotation(0);
  
  unsigned long startTime = millis();
  bool touched = false;
  
  while (millis() - startTime < 5000 && !touched) {
    if (touch.touched()) {
      TS_Point p = touch.getPoint();
      if (p.z > 600) {
        tft.setCursor(10, 60);
        tft.printf("Touch detected: X=%d, Y=%d", p.x, p.y);
        touched = true;
        Serial.printf("Touch: X=%d, Y=%d, Z=%d\n", p.x, p.y, p.z);
      }
    }
    delay(50);
  }
  
  if (touched) {
    tft.setCursor(10, 80);
    tft.setTextColor(TFT_GREEN);
    tft.println("Touch: PASS");
    Serial.println("Touch: PASS");
    return true;
  } else {
    tft.setCursor(10, 80);
    tft.setTextColor(TFT_RED);
    tft.println("Touch: FAIL (timeout)");
    Serial.println("Touch: FAIL");
    return false;
  }
}

bool testSDCard() {
  Serial.println("Testing SD Card...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_MAGENTA);
  tft.setCursor(10, 10);
  tft.println("Test 3: SD Card");
  
  if (!SD.begin(SD_CS)) {
    tft.setCursor(10, 30);
    tft.setTextColor(TFT_RED);
    tft.println("SD Card: FAIL");
    tft.println("Check card insertion");
    Serial.println("SD Card: FAIL");
    return false;
  }
  
  uint8_t cardType = SD.cardType();
  uint64_t cardSize = SD.cardSize() / (1024 * 1024);
  
  tft.setCursor(10, 30);
  tft.setTextColor(TFT_GREEN);
  tft.println("SD Card: PASS");
  tft.setCursor(10, 50);
  tft.printf("Size: %lluMB", cardSize);
  
  // Test file operations
  File testFile = SD.open("/test.txt", FILE_WRITE);
  if (testFile) {
    testFile.println("Hardware test");
    testFile.close();
    
    testFile = SD.open("/test.txt", FILE_READ);
    if (testFile) {
      String content = testFile.readString();
      testFile.close();
      SD.remove("/test.txt");
      
      tft.setCursor(10, 70);
      tft.println("File operations: OK");
      Serial.printf("SD Card: PASS (%lluMB)\n", cardSize);
      return true;
    }
  }
  
  tft.setCursor(10, 70);
  tft.setTextColor(TFT_RED);
  tft.println("File operations: FAIL");
  Serial.println("SD Card: File operations failed");
  return false;
}

bool testRFID() {
  Serial.println("Testing RFID...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_ORANGE);
  tft.setCursor(10, 10);
  tft.println("Test 4: RFID Reader");
  
  SPI.begin();
  mfrc522.PCD_Init();
  
  // Check RFID version
  byte version = mfrc522.PCD_ReadRegister(MFRC522::VersionReg);
  
  tft.setCursor(10, 30);
  tft.printf("RFID Version: 0x%02X", version);
  
  if (version == 0x00 || version == 0xFF) {
    tft.setCursor(10, 50);
    tft.setTextColor(TFT_RED);
    tft.println("RFID: FAIL");
    tft.println("Check wiring");
    Serial.println("RFID: FAIL - No response");
    return false;
  }
  
  // Perform self-test
  bool selfTest = mfrc522.PCD_PerformSelfTest();
  
  tft.setCursor(10, 50);
  if (selfTest) {
    tft.setTextColor(TFT_GREEN);
    tft.println("RFID: PASS");
    tft.setCursor(10, 70);
    tft.println("Self-test: OK");
    Serial.println("RFID: PASS");
  } else {
    tft.setTextColor(TFT_RED);
    tft.println("RFID: FAIL");
    tft.setCursor(10, 70);
    tft.println("Self-test: FAIL");
    Serial.println("RFID: FAIL - Self-test failed");
  }
  
  // Re-initialize after self-test
  mfrc522.PCD_Init();
  
  return selfTest;
}

bool testRGBLED() {
  Serial.println("Testing RGB LED...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setCursor(10, 10);
  tft.println("Test 5: RGB LED");
  
  FastLED.addLeds<WS2812, RGB_LED_PIN, GRB>(leds, 1);
  FastLED.setBrightness(128);
  
  // Test colors
  const CRGB colors[] = {CRGB::Red, CRGB::Green, CRGB::Blue, CRGB::White, CRGB::Black};
  const char* colorNames[] = {"Red", "Green", "Blue", "White", "Off"};
  
  for (int i = 0; i < 5; i++) {
    leds[0] = colors[i];
    FastLED.show();
    
    tft.fillRect(10, 30, 200, 20, TFT_BLACK);
    tft.setCursor(10, 30);
    tft.printf("LED Color: %s", colorNames[i]);
    
    delay(500);
  }
  
  tft.setCursor(10, 60);
  tft.setTextColor(TFT_GREEN);
  tft.println("RGB LED: PASS");
  
  Serial.println("RGB LED: PASS");
  return true;
}

bool testSpeaker() {
  Serial.println("Testing Speaker...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_YELLOW);
  tft.setCursor(10, 10);
  tft.println("Test 6: Speaker");
  
  pinMode(SPEAKER_PIN, OUTPUT);
  
  // Test different tones
  int frequencies[] = {440, 880, 1320, 1760};
  
  for (int i = 0; i < 4; i++) {
    tft.fillRect(10, 30, 200, 20, TFT_BLACK);
    tft.setCursor(10, 30);
    tft.printf("Playing: %dHz", frequencies[i]);
    
    tone(SPEAKER_PIN, frequencies[i], 300);
    delay(400);
  }
  
  noTone(SPEAKER_PIN);
  
  tft.setCursor(10, 60);
  tft.setTextColor(TFT_GREEN);
  tft.println("Speaker: PASS");
  tft.setCursor(10, 80);
  tft.println("(if you heard tones)");
  
  Serial.println("Speaker: PASS");
  return true;
}

bool testLightSensor() {
  Serial.println("Testing Light Sensor...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_CYAN);
  tft.setCursor(10, 10);
  tft.println("Test 7: Light Sensor");
  
  pinMode(LDR_PIN, INPUT);
  
  int lightValue = analogRead(LDR_PIN);
  
  tft.setCursor(10, 30);
  tft.printf("Light Level: %d", lightValue);
  
  tft.setCursor(10, 50);
  if (lightValue > 0 && lightValue < 4095) {
    tft.setTextColor(TFT_GREEN);
    tft.println("Light Sensor: PASS");
    Serial.printf("Light Sensor: PASS (value: %d)\n", lightValue);
    return true;
  } else {
    tft.setTextColor(TFT_RED);
    tft.println("Light Sensor: FAIL");
    Serial.println("Light Sensor: FAIL");
    return false;
  }
}

bool testWiFi() {
  Serial.println("Testing WiFi...");
  
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_BLUE);
  tft.setCursor(10, 10);
  tft.println("Test 8: WiFi");
  
  WiFi.mode(WIFI_STA);
  WiFi.disconnect();
  delay(100);
  
  int networks = WiFi.scanNetworks();
  
  tft.setCursor(10, 30);
  tft.printf("Networks found: %d", networks);
  
  if (networks > 0) {
    tft.setCursor(10, 50);
    tft.setTextColor(TFT_GREEN);
    tft.println("WiFi: PASS");
    
    // Show first few networks
    for (int i = 0; i < min(3, networks); i++) {
      tft.setCursor(10, 70 + i * 15);
      tft.setTextSize(1);
      tft.printf("%s (%d)", WiFi.SSID(i).c_str(), WiFi.RSSI(i));
    }
    
    Serial.printf("WiFi: PASS (%d networks found)\n", networks);
    return true;
  } else {
    tft.setCursor(10, 50);
    tft.setTextColor(TFT_RED);
    tft.println("WiFi: FAIL");
    Serial.println("WiFi: FAIL - No networks found");
    return false;
  }
}

void displayTestResults() {
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(2);
  tft.setCursor(10, 10);
  tft.println("Test Results");
  
  const char* testNames[] = {
    "Display", "Touch", "SD Card", "RFID",
    "RGB LED", "Speaker", "Light Sensor", "WiFi"
  };
  
  tft.setTextSize(1);
  int passCount = 0;
  
  for (int i = 0; i < 8; i++) {
    tft.setCursor(10, 40 + i * 20);
    tft.setTextColor(testPassed[i] ? TFT_GREEN : TFT_RED);
    tft.printf("%s: %s", testNames[i], testPassed[i] ? "PASS" : "FAIL");
    
    if (testPassed[i]) passCount++;
  }
  
  tft.setCursor(10, 220);
  tft.setTextColor(TFT_YELLOW);
  tft.printf("Score: %d/8 tests passed", passCount);
  
  tft.setCursor(10, 240);
  tft.setTextColor(TFT_CYAN);
  tft.println("Touch screen to retest");
  
  // Print summary to serial
  Serial.println("\n=== Test Summary ===");
  for (int i = 0; i < 8; i++) {
    Serial.printf("%s: %s\n", testNames[i], testPassed[i] ? "PASS" : "FAIL");
  }
  Serial.printf("Score: %d/8 tests passed\n", passCount);
  Serial.println("==================");
}
