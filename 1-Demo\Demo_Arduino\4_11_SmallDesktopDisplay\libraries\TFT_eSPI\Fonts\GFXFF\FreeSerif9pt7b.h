const uint8_t FreeSerif9pt7bBitmaps[] PROGMEM = {
  0xFF, 0xEA, 0x03, 0xDE, 0xF7, 0x20, 0x11, 0x09, 0x04, 0x82, 0x4F, 0xF9,
  0x10, 0x89, 0xFF, 0x24, 0x12, 0x09, 0x0C, 0x80, 0x10, 0x7C, 0xD6, 0xD2,
  0xD0, 0xF0, 0x38, 0x1E, 0x17, 0x93, 0x93, 0xD6, 0x7C, 0x10, 0x38, 0x43,
  0x3C, 0x39, 0x21, 0x8A, 0x0C, 0x50, 0x65, 0x39, 0xCB, 0x20, 0xB9, 0x05,
  0x88, 0x4C, 0x44, 0x64, 0x21, 0xC0, 0x0E, 0x00, 0xC8, 0x06, 0x40, 0x32,
  0x01, 0xA0, 0x07, 0x78, 0x31, 0x87, 0x88, 0x46, 0x86, 0x34, 0x30, 0xC1,
  0xC7, 0x17, 0xCF, 0x00, 0xFE, 0x08, 0x88, 0x84, 0x63, 0x18, 0xC6, 0x10,
  0x82, 0x08, 0x20, 0x82, 0x08, 0x21, 0x0C, 0x63, 0x18, 0xC4, 0x22, 0x22,
  0x00, 0x63, 0x9A, 0xDC, 0x72, 0xB6, 0x08, 0x08, 0x04, 0x02, 0x01, 0x0F,
  0xF8, 0x40, 0x20, 0x10, 0x08, 0x00, 0xD8, 0xF0, 0xF0, 0x08, 0x84, 0x22,
  0x10, 0x8C, 0x42, 0x31, 0x00, 0x1C, 0x31, 0x98, 0xD8, 0x3C, 0x1E, 0x0F,
  0x07, 0x83, 0xC1, 0xE0, 0xD8, 0xC4, 0x61, 0xC0, 0x13, 0x8C, 0x63, 0x18,
  0xC6, 0x31, 0x8C, 0x67, 0x80, 0x3C, 0x4E, 0x86, 0x06, 0x06, 0x04, 0x0C,
  0x08, 0x10, 0x20, 0x41, 0xFE, 0x3C, 0xC6, 0x06, 0x04, 0x1C, 0x3E, 0x07,
  0x03, 0x03, 0x03, 0x06, 0xF8, 0x04, 0x18, 0x71, 0x64, 0xC9, 0xA3, 0x46,
  0xFE, 0x18, 0x30, 0x60, 0x0F, 0x10, 0x20, 0x3C, 0x0E, 0x07, 0x03, 0x03,
  0x03, 0x02, 0x04, 0xF8, 0x07, 0x1C, 0x30, 0x60, 0x60, 0xDC, 0xE6, 0xC3,
  0xC3, 0xC3, 0x43, 0x66, 0x3C, 0x7F, 0x82, 0x02, 0x02, 0x04, 0x04, 0x04,
  0x08, 0x08, 0x08, 0x10, 0x10, 0x3C, 0x8F, 0x1E, 0x3E, 0x4F, 0x06, 0x36,
  0xC7, 0x8F, 0x1B, 0x33, 0xC0, 0x3C, 0x66, 0xC2, 0xC3, 0xC3, 0xC3, 0xC3,
  0x63, 0x3F, 0x06, 0x06, 0x0C, 0x38, 0x60, 0xF0, 0x0F, 0xD8, 0x00, 0x03,
  0x28, 0x01, 0x87, 0x0E, 0x1C, 0x0C, 0x03, 0x80, 0x70, 0x0E, 0x00, 0x80,
  0xFF, 0x80, 0x00, 0x00, 0x0F, 0xF8, 0x80, 0x1C, 0x01, 0xC0, 0x1C, 0x01,
  0xC0, 0xE0, 0xE0, 0xE0, 0xC0, 0x00, 0x79, 0x1A, 0x18, 0x30, 0x60, 0x83,
  0x04, 0x10, 0x20, 0x40, 0x03, 0x00, 0x0F, 0x83, 0x8C, 0x60, 0x26, 0x02,
  0xC7, 0x9C, 0xC9, 0xD8, 0x9D, 0x99, 0xD9, 0x26, 0xEC, 0x60, 0x03, 0x04,
  0x0F, 0x80, 0x02, 0x00, 0x10, 0x01, 0xC0, 0x16, 0x00, 0x98, 0x04, 0xC0,
  0x43, 0x03, 0xF8, 0x20, 0x61, 0x03, 0x18, 0x1D, 0xE1, 0xF0, 0xFF, 0x86,
  0x1C, 0xC1, 0x98, 0x33, 0x0C, 0x7E, 0x0C, 0x31, 0x83, 0x30, 0x66, 0x0C,
  0xC3, 0x7F, 0xC0, 0x1F, 0x26, 0x1D, 0x81, 0xE0, 0x1C, 0x01, 0x80, 0x30,
  0x06, 0x00, 0xC0, 0x0C, 0x00, 0xC1, 0x8F, 0xC0, 0xFF, 0x03, 0x1C, 0x30,
  0x63, 0x07, 0x30, 0x33, 0x03, 0x30, 0x33, 0x03, 0x30, 0x33, 0x06, 0x30,
  0xCF, 0xF0, 0xFF, 0x98, 0x26, 0x01, 0x80, 0x61, 0x1F, 0xC6, 0x11, 0x80,
  0x60, 0x18, 0x16, 0x0F, 0xFE, 0xFF, 0xB0, 0x58, 0x0C, 0x06, 0x13, 0xF9,
  0x84, 0xC0, 0x60, 0x30, 0x18, 0x1E, 0x00, 0x1F, 0x23, 0x0E, 0x60, 0x26,
  0x00, 0xC0, 0x0C, 0x0F, 0xC0, 0x6C, 0x06, 0xC0, 0x66, 0x06, 0x30, 0x60,
  0xF8, 0xF1, 0xEC, 0x19, 0x83, 0x30, 0x66, 0x0C, 0xFF, 0x98, 0x33, 0x06,
  0x60, 0xCC, 0x19, 0x83, 0x78, 0xF0, 0xF6, 0x66, 0x66, 0x66, 0x66, 0x6F,
  0x3C, 0x61, 0x86, 0x18, 0x61, 0x86, 0x18, 0x6D, 0xBC, 0xF3, 0xE6, 0x08,
  0x61, 0x06, 0x20, 0x64, 0x07, 0x80, 0x6C, 0x06, 0x60, 0x63, 0x06, 0x18,
  0x60, 0xCF, 0x3F, 0xF0, 0x18, 0x06, 0x01, 0x80, 0x60, 0x18, 0x06, 0x01,
  0x80, 0x60, 0x18, 0x16, 0x0B, 0xFE, 0xF0, 0x0E, 0x70, 0x38, 0xE0, 0x71,
  0xE1, 0x62, 0xC2, 0xC5, 0xC9, 0x89, 0x93, 0x13, 0x26, 0x23, 0x8C, 0x47,
  0x18, 0x84, 0x33, 0x88, 0xF0, 0xE0, 0xEE, 0x09, 0xC1, 0x2C, 0x25, 0xC4,
  0x9C, 0x91, 0x92, 0x1A, 0x41, 0xC8, 0x19, 0x03, 0x70, 0x20, 0x1F, 0x06,
  0x31, 0x83, 0x20, 0x2C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x68, 0x09,
  0x83, 0x18, 0xC1, 0xF0, 0xFE, 0x31, 0x98, 0x6C, 0x36, 0x1B, 0x19, 0xF8,
  0xC0, 0x60, 0x30, 0x18, 0x1E, 0x00, 0x1F, 0x06, 0x31, 0x83, 0x20, 0x2C,
  0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x68, 0x19, 0x83, 0x18, 0xC0, 0xE0,
  0x0E, 0x00, 0xE0, 0x07, 0xFE, 0x0C, 0x61, 0x86, 0x30, 0xC6, 0x18, 0xC6,
  0x1F, 0x83, 0x70, 0x67, 0x0C, 0x71, 0x87, 0x78, 0x70, 0x1D, 0x31, 0x98,
  0x4C, 0x07, 0x80, 0xE0, 0x1C, 0x07, 0x01, 0xA0, 0xD8, 0xCB, 0xC0, 0xFF,
  0xF8, 0xCE, 0x18, 0x83, 0x00, 0x60, 0x0C, 0x01, 0x80, 0x30, 0x06, 0x00,
  0xC0, 0x18, 0x07, 0x80, 0xF0, 0xEC, 0x09, 0x81, 0x30, 0x26, 0x04, 0xC0,
  0x98, 0x13, 0x02, 0x60, 0x4C, 0x08, 0xC2, 0x0F, 0x80, 0xF8, 0x77, 0x02,
  0x30, 0x23, 0x04, 0x18, 0x41, 0x84, 0x0C, 0x80, 0xC8, 0x07, 0x00, 0x70,
  0x02, 0x00, 0x20, 0xFB, 0xE7, 0xB0, 0xC0, 0x8C, 0x20, 0x86, 0x18, 0x41,
  0x8C, 0x40, 0xCB, 0x20, 0x65, 0x90, 0x1A, 0x70, 0x0E, 0x38, 0x03, 0x1C,
  0x01, 0x04, 0x00, 0x82, 0x00, 0xFC, 0xF9, 0x83, 0x06, 0x10, 0x19, 0x00,
  0xD0, 0x03, 0x00, 0x1C, 0x01, 0x30, 0x11, 0xC1, 0x86, 0x08, 0x19, 0xE3,
  0xF0, 0xF8, 0xF6, 0x06, 0x30, 0x41, 0x88, 0x1D, 0x00, 0xD0, 0x06, 0x00,
  0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0xF0, 0x3F, 0xCC, 0x11, 0x06, 0x01,
  0x80, 0x70, 0x0C, 0x03, 0x00, 0xE0, 0x38, 0x06, 0x05, 0xC1, 0x7F, 0xE0,
  0xFB, 0x6D, 0xB6, 0xDB, 0x6D, 0xB8, 0x82, 0x10, 0x82, 0x10, 0x86, 0x10,
  0x86, 0x10, 0xED, 0xB6, 0xDB, 0x6D, 0xB6, 0xF8, 0x18, 0x1C, 0x34, 0x26,
  0x62, 0x42, 0xC1, 0xFF, 0x80, 0x84, 0x20, 0x79, 0x98, 0x30, 0xE6, 0xD9,
  0xB3, 0x3F, 0x20, 0x70, 0x18, 0x0C, 0x06, 0x03, 0x71, 0xCC, 0xC3, 0x61,
  0xB0, 0xD8, 0x6C, 0x63, 0xE0, 0x3C, 0xCF, 0x06, 0x0C, 0x18, 0x18, 0x9E,
  0x01, 0x03, 0x80, 0xC0, 0x60, 0x31, 0xD9, 0x9D, 0x86, 0xC3, 0x61, 0xB0,
  0xCC, 0x63, 0xF0, 0x3C, 0x46, 0xFE, 0xC0, 0xC0, 0xE1, 0x62, 0x3C, 0x1E,
  0x41, 0x83, 0x06, 0x1E, 0x18, 0x30, 0x60, 0xC1, 0x83, 0x0F, 0x00, 0x3C,
  0x19, 0xF6, 0x31, 0x8C, 0x1E, 0x08, 0x04, 0x01, 0xFC, 0x40, 0xB0, 0x2E,
  0x11, 0xF8, 0x20, 0x70, 0x18, 0x0C, 0x06, 0x03, 0x71, 0xCC, 0xC6, 0x63,
  0x31, 0x98, 0xCC, 0x6F, 0x78, 0x60, 0x02, 0xE6, 0x66, 0x66, 0xF0, 0x18,
  0x00, 0x33, 0x8C, 0x63, 0x18, 0xC6, 0x31, 0x8B, 0x80, 0x20, 0x70, 0x18,
  0x0C, 0x06, 0x03, 0x3D, 0x88, 0xD8, 0x78, 0x36, 0x19, 0x8C, 0x6F, 0x78,
  0x2E, 0x66, 0x66, 0x66, 0x66, 0x66, 0xF0, 0xEE, 0x71, 0xCE, 0x66, 0x31,
  0x98, 0xC6, 0x63, 0x19, 0x8C, 0x66, 0x31, 0xBD, 0xEF, 0xEE, 0x39, 0x98,
  0xCC, 0x66, 0x33, 0x19, 0x8D, 0xEF, 0x3E, 0x31, 0xB0, 0x78, 0x3C, 0x1E,
  0x0D, 0x8C, 0x7C, 0xEE, 0x39, 0x98, 0x6C, 0x36, 0x1B, 0x0D, 0x8C, 0xFC,
  0x60, 0x30, 0x18, 0x1E, 0x00, 0x3D, 0x31, 0xB0, 0xD8, 0x6C, 0x36, 0x1B,
  0x8C, 0xFE, 0x03, 0x01, 0x80, 0xC0, 0xF0, 0x6D, 0xC6, 0x18, 0x61, 0x86,
  0x3C, 0x76, 0x38, 0x58, 0x3E, 0x38, 0xFE, 0x27, 0x98, 0xC6, 0x31, 0x8C,
  0x38, 0xE7, 0x31, 0x98, 0xCC, 0x66, 0x33, 0x19, 0x8C, 0x7F, 0xF3, 0x61,
  0x22, 0x32, 0x14, 0x1C, 0x08, 0x08, 0xEF, 0x36, 0x61, 0x62, 0x22, 0x32,
  0x35, 0x41, 0x9C, 0x18, 0x81, 0x08, 0xF7, 0x12, 0x0E, 0x03, 0x01, 0xC1,
  0x21, 0x09, 0xCF, 0xF3, 0x61, 0x62, 0x32, 0x34, 0x14, 0x1C, 0x08, 0x08,
  0x08, 0x10, 0xE0, 0xFD, 0x18, 0x60, 0x83, 0x0C, 0x70, 0xFE, 0x19, 0x8C,
  0x63, 0x18, 0xC4, 0x61, 0x8C, 0x63, 0x18, 0xC3, 0xFF, 0xF0, 0xC3, 0x18,
  0xC6, 0x31, 0x84, 0x33, 0x18, 0xC6, 0x31, 0x98, 0x70, 0x24, 0xC1, 0xC0 };

const GFXglyph FreeSerif9pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,   5,    0,    1 },   // 0x20 ' '
  {     0,   2,  12,   6,    2,  -11 },   // 0x21 '!'
  {     3,   5,   4,   7,    1,  -11 },   // 0x22 '"'
  {     6,   9,  12,   9,    0,  -11 },   // 0x23 '#'
  {    20,   8,  14,   9,    1,  -12 },   // 0x24 '$'
  {    34,  13,  12,  15,    1,  -11 },   // 0x25 '%'
  {    54,  13,  13,  14,    1,  -12 },   // 0x26 '&'
  {    76,   2,   4,   4,    1,  -11 },   // 0x27 '''
  {    77,   5,  15,   6,    1,  -11 },   // 0x28 '('
  {    87,   5,  15,   6,    0,  -11 },   // 0x29 ')'
  {    97,   6,   8,   9,    3,  -11 },   // 0x2A '*'
  {   103,   9,   9,  10,    0,   -8 },   // 0x2B '+'
  {   114,   2,   3,   4,    2,    0 },   // 0x2C ','
  {   115,   4,   1,   6,    1,   -3 },   // 0x2D '-'
  {   116,   2,   2,   5,    1,   -1 },   // 0x2E '.'
  {   117,   5,  12,   5,    0,  -11 },   // 0x2F '/'
  {   125,   9,  13,   9,    0,  -12 },   // 0x30 '0'
  {   140,   5,  13,   9,    2,  -12 },   // 0x31 '1'
  {   149,   8,  12,   9,    1,  -11 },   // 0x32 '2'
  {   161,   8,  12,   9,    0,  -11 },   // 0x33 '3'
  {   173,   7,  12,   9,    1,  -11 },   // 0x34 '4'
  {   184,   8,  12,   9,    0,  -11 },   // 0x35 '5'
  {   196,   8,  13,   9,    1,  -12 },   // 0x36 '6'
  {   209,   8,  12,   9,    0,  -11 },   // 0x37 '7'
  {   221,   7,  13,   9,    1,  -12 },   // 0x38 '8'
  {   233,   8,  14,   9,    1,  -12 },   // 0x39 '9'
  {   247,   2,   8,   5,    1,   -7 },   // 0x3A ':'
  {   249,   3,  10,   5,    1,   -7 },   // 0x3B ';'
  {   253,   9,   9,  10,    1,   -8 },   // 0x3C '<'
  {   264,   9,   5,  10,    1,   -6 },   // 0x3D '='
  {   270,  10,   9,  10,    0,   -8 },   // 0x3E '>'
  {   282,   7,  13,   8,    1,  -12 },   // 0x3F '?'
  {   294,  12,  13,  16,    2,  -12 },   // 0x40 '@'
  {   314,  13,  12,  13,    0,  -11 },   // 0x41 'A'
  {   334,  11,  12,  11,    0,  -11 },   // 0x42 'B'
  {   351,  11,  12,  12,    1,  -11 },   // 0x43 'C'
  {   368,  12,  12,  13,    0,  -11 },   // 0x44 'D'
  {   386,  10,  12,  11,    1,  -11 },   // 0x45 'E'
  {   401,   9,  12,  10,    1,  -11 },   // 0x46 'F'
  {   415,  12,  12,  13,    1,  -11 },   // 0x47 'G'
  {   433,  11,  12,  13,    1,  -11 },   // 0x48 'H'
  {   450,   4,  12,   6,    1,  -11 },   // 0x49 'I'
  {   456,   6,  12,   7,    0,  -11 },   // 0x4A 'J'
  {   465,  12,  12,  13,    1,  -11 },   // 0x4B 'K'
  {   483,  10,  12,  11,    1,  -11 },   // 0x4C 'L'
  {   498,  15,  12,  16,    0,  -11 },   // 0x4D 'M'
  {   521,  11,  12,  13,    1,  -11 },   // 0x4E 'N'
  {   538,  11,  13,  13,    1,  -12 },   // 0x4F 'O'
  {   556,   9,  12,  10,    1,  -11 },   // 0x50 'P'
  {   570,  11,  16,  13,    1,  -12 },   // 0x51 'Q'
  {   592,  11,  12,  12,    1,  -11 },   // 0x52 'R'
  {   609,   9,  12,  10,    0,  -11 },   // 0x53 'S'
  {   623,  11,  12,  11,    0,  -11 },   // 0x54 'T'
  {   640,  11,  12,  13,    1,  -11 },   // 0x55 'U'
  {   657,  12,  12,  13,    0,  -11 },   // 0x56 'V'
  {   675,  17,  12,  17,    0,  -11 },   // 0x57 'W'
  {   701,  13,  12,  13,    0,  -11 },   // 0x58 'X'
  {   721,  12,  12,  13,    0,  -11 },   // 0x59 'Y'
  {   739,  11,  12,  11,    0,  -11 },   // 0x5A 'Z'
  {   756,   3,  15,   6,    2,  -11 },   // 0x5B '['
  {   762,   5,  12,   5,    0,  -11 },   // 0x5C '\'
  {   770,   3,  15,   6,    1,  -11 },   // 0x5D ']'
  {   776,   8,   7,   8,    0,  -11 },   // 0x5E '^'
  {   783,   9,   1,   9,    0,    2 },   // 0x5F '_'
  {   785,   4,   3,   5,    0,  -11 },   // 0x60 '`'
  {   787,   7,   8,   8,    1,   -7 },   // 0x61 'a'
  {   794,   9,  13,   9,    0,  -12 },   // 0x62 'b'
  {   809,   7,   8,   8,    0,   -7 },   // 0x63 'c'
  {   816,   9,  13,   9,    0,  -12 },   // 0x64 'd'
  {   831,   8,   8,   8,    0,   -7 },   // 0x65 'e'
  {   839,   7,  13,   7,    1,  -12 },   // 0x66 'f'
  {   851,  10,  12,   8,    0,   -7 },   // 0x67 'g'
  {   866,   9,  13,   9,    0,  -12 },   // 0x68 'h'
  {   881,   4,  11,   5,    1,  -10 },   // 0x69 'i'
  {   887,   5,  15,   6,    0,  -10 },   // 0x6A 'j'
  {   897,   9,  13,   9,    1,  -12 },   // 0x6B 'k'
  {   912,   4,  13,   5,    1,  -12 },   // 0x6C 'l'
  {   919,  14,   8,  14,    0,   -7 },   // 0x6D 'm'
  {   933,   9,   8,   9,    0,   -7 },   // 0x6E 'n'
  {   942,   9,   8,   9,    0,   -7 },   // 0x6F 'o'
  {   951,   9,  12,   9,    0,   -7 },   // 0x70 'p'
  {   965,   9,  12,   9,    0,   -7 },   // 0x71 'q'
  {   979,   6,   8,   6,    0,   -7 },   // 0x72 'r'
  {   985,   6,   8,   7,    1,   -7 },   // 0x73 's'
  {   991,   5,   9,   5,    0,   -8 },   // 0x74 't'
  {   997,   9,   8,   9,    0,   -7 },   // 0x75 'u'
  {  1006,   8,   8,   8,    0,   -7 },   // 0x76 'v'
  {  1014,  12,   8,  12,    0,   -7 },   // 0x77 'w'
  {  1026,   9,   8,   9,    0,   -7 },   // 0x78 'x'
  {  1035,   8,  12,   8,    0,   -7 },   // 0x79 'y'
  {  1047,   7,   8,   7,    1,   -7 },   // 0x7A 'z'
  {  1054,   5,  16,   9,    1,  -12 },   // 0x7B '{'
  {  1064,   1,  12,   4,    1,  -11 },   // 0x7C '|'
  {  1066,   5,  16,   9,    3,  -11 },   // 0x7D '}'
  {  1076,   9,   3,   9,    0,   -5 } }; // 0x7E '~'

const GFXfont FreeSerif9pt7b PROGMEM = {
  (uint8_t  *)FreeSerif9pt7bBitmaps,
  (GFXglyph *)FreeSerif9pt7bGlyphs,
  0x20, 0x7E, 22 };

// Approx. 1752 bytes
