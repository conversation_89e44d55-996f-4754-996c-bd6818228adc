/*
 * Simple Web Server Header
 * Uses built-in ESP32 WebServer instead of ESPAsyncWebServer
 * Compatible with all ESP32 core versions
 */

#ifndef SIMPLE_WEB_SERVER_H
#define SIMPLE_WEB_SERVER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include "config.h"
#include "employee_db.h"
#include "data_manager.h"

class SimpleWebServer {
private:
  WebServer* server;
  EmployeeDB* employeeDB;
  DataManager* dataManager;
  
  bool initialized;
  bool serverRunning;
  int serverPort;
  
  // Statistics
  unsigned long totalRequests;
  unsigned long apiRequests;
  unsigned long webRequests;
  
  // Private methods
  void setupRoutes();
  void handleRoot();
  void handleAPI();
  void handleEmployees();
  void handleStatus();
  void handleNotFound();
  
  // Utility methods
  String generateStatusJSON();
  String generateEmployeesJSON();
  void sendJSONResponse(String json, int code = 200);
  void sendErrorResponse(String message, int code = 400);
  
public:
  SimpleWebServer();
  ~SimpleWebServer();
  
  // Initialization
  bool begin();
  bool begin(int port);
  bool begin(EmployeeDB* empDB, DataManager* dataMgr);
  void stop();
  bool isRunning() const { return serverRunning; }
  
  // Server management
  void handleClient();
  void setPort(int port) { serverPort = port; }
  int getPort() const { return serverPort; }
  
  // Configuration
  void setEmployeeDB(EmployeeDB* empDB) { employeeDB = empDB; }
  void setDataManager(DataManager* dataMgr) { dataManager = dataMgr; }
  
  // Statistics
  unsigned long getTotalRequests() const { return totalRequests; }
  unsigned long getAPIRequests() const { return apiRequests; }
  unsigned long getWebRequests() const { return webRequests; }
  void resetStatistics();
  
  // Status
  String getServerInfo();
  void printServerStatus();
};

#endif // SIMPLE_WEB_SERVER_H
