






Started logging.

<<<
Content-Length: 3239

{"jsonrpc":"2.0","id":0,"method":"initialize","params":{"processId":21376,"clientInfo":{"name":"vscode","version":"1.50.0"},"rootPath":"c:\\Users\\<USER>\\Desktop\\ESP32-2432S028例子\\arduino\\Source code\\3_4-3_Flash_PNG\\Flash_PNG","rootUri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG","capabilities":{"workspace":{"applyEdit":true,"workspaceEdit":{"documentChanges":true,"resourceOperations":["create","rename","delete"],"failureHandling":"textOnlyTransactional"},"didChangeConfiguration":{"dynamicRegistration":true},"didChangeWatchedFiles":{"dynamicRegistration":true},"symbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]}},"executeCommand":{"dynamicRegistration":true},"configuration":true,"workspaceFolders":true},"textDocument":{"publishDiagnostics":{"relatedInformation":true,"versionSupport":false,"tagSupport":{"valueSet":[1,2]}},"synchronization":{"dynamicRegistration":true,"willSave":true,"willSaveWaitUntil":true,"didSave":true},"completion":{"dynamicRegistration":true,"contextSupport":true,"completionItem":{"snippetSupport":true,"commitCharactersSupport":true,"documentationFormat":["markdown","plaintext"],"deprecatedSupport":true,"preselectSupport":true,"tagSupport":{"valueSet":[1]}},"completionItemKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]}},"hover":{"dynamicRegistration":true,"contentFormat":["markdown","plaintext"]},"signatureHelp":{"dynamicRegistration":true,"signatureInformation":{"documentationFormat":["markdown","plaintext"],"parameterInformation":{"labelOffsetSupport":true}},"contextSupport":true},"definition":{"dynamicRegistration":true,"linkSupport":true},"references":{"dynamicRegistration":true},"documentHighlight":{"dynamicRegistration":true},"documentSymbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"hierarchicalDocumentSymbolSupport":true},"codeAction":{"dynamicRegistration":true,"isPreferredSupport":true,"codeActionLiteralSupport":{"codeActionKind":{"valueSet":["","quickfix","refactor","refactor.extract","refactor.inline","refactor.rewrite","source","source.organizeImports"]}}},"codeLens":{"dynamicRegistration":true},"formatting":{"dynamicRegistration":true},"rangeFormatting":{"dynamicRegistration":true},"onTypeFormatting":{"dynamicRegistration":true},"rename":{"dynamicRegistration":true,"prepareSupport":true},"documentLink":{"dynamicRegistration":true,"tooltipSupport":true},"typeDefinition":{"dynamicRegistration":true,"linkSupport":true},"implementation":{"dynamicRegistration":true,"linkSupport":true},"colorProvider":{"dynamicRegistration":true},"foldingRange":{"dynamicRegistration":true,"rangeLimit":5000,"lineFoldingOnly":true},"declaration":{"dynamicRegistration":true,"linkSupport":true},"selectionRange":{"dynamicRegistration":true}},"window":{"workDoneProgress":true}},"initializationOptions":{},"trace":"off","workspaceFolders":[{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG","name":"Flash_PNG"}]}}
>>>
Content-Length: 598

{"id":0,"result":{"capabilities":{"textDocumentSync":2,"hoverProvider":true,"completionProvider":{"triggerCharacters":[".","\u003e",":"]},"signatureHelpProvider":{"triggerCharacters":["(",","]},"definitionProvider":true,"documentHighlightProvider":true,"documentSymbolProvider":true,"workspaceSymbolProvider":true,"codeActionProvider":true,"documentFormattingProvider":true,"documentRangeFormattingProvider":true,"documentOnTypeFormattingProvider":{"firstTriggerCharacter":"\n"},"renameProvider":false,"executeCommandProvider":{"commands":["clangd.applyFix","clangd.applyTweak"]}}},"jsonrpc":"2.0"}
<<<
Content-Length: 52

{"jsonrpc":"2.0","method":"initialized","params":{}}Content-Length: 2837

{"jsonrpc":"2.0","method":"textDocument/didOpen","params":{"textDocument":{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG/Flash_PNG.ino","languageId":"cpp","version":1,"text":"\n// This example renders a png file that is stored in a FLASH array\n// using the PNGdec library (available via library manager).\n\n// Include the PNG decoder library\n#include <PNGdec.h>\n#include \"panda_png.h\" // Image is stored here in an 8 bit array\n\nPNG png; // PNG decoder inatance\n\n#define MAX_IMAGE_WDITH 240 // Adjust for your images\n\nint16_t xpos = 0;\nint16_t ypos = 0;\n\n// Include the TFT library https://github.com/Bodmer/TFT_eSPI\n#include \"SPI.h\"\n#include <TFT_eSPI.h>              // Hardware-specific library\nTFT_eSPI tft = TFT_eSPI();         // Invoke custom library\n\n//====================================================================================\n//                                    Setup\n//====================================================================================\nvoid setup()\n{\n  Serial.begin(115200);\n  Serial.println(\"\\n\\n Using the PNGdec library\");\n\n  // Initialise the TFT\n  tft.begin();\n  tft.fillScreen(TFT_BLACK);\n\n  Serial.println(\"\\r\\nInitialisation done.\");\n}\n\n//====================================================================================\n//                                    Loop\n//====================================================================================\nvoid loop()\n{\n  int16_t rc = png.openFLASH((uint8_t *)panda_png, sizeof(panda_png), pngDraw);\n  if (rc == PNG_SUCCESS) {\n    Serial.println(\"Successfully png file\");\n    Serial.printf(\"image specs: (%d x %d), %d bpp, pixel type: %d\\n\", png.getWidth(), png.getHeight(), png.getBpp(), png.getPixelType());\n    tft.startWrite();\n    uint32_t dt = millis();\n    rc = png.decode(NULL, 0);\n    Serial.print(millis() - dt); Serial.println(\"ms\");\n    tft.endWrite();\n    // png.close(); // not needed for memory->memory decode\n  }\n  delay(3000);\n  tft.fillScreen(random(0x10000));\n}\n\n\n//=========================================v==========================================\n//                                      pngDraw\n//====================================================================================\n// This next function will be called during decoding of the png file to\n// render each image line to the TFT.  If you use a different TFT library\n// you will need to adapt this function to suit.\n// Callback function to draw pixels to the display\nvoid pngDraw(PNGDRAW *pDraw) {\n  uint16_t lineBuffer[MAX_IMAGE_WDITH];\n  png.getLineAsRGB565(pDraw, lineBuffer, PNG_RGB565_BIG_ENDIAN, 0xffffffff);\n  tft.pushImage(xpos, ypos + pDraw->y, pDraw->iWidth, 1, lineBuffer);\n}\n"}}}






Started logging.

<<<
Content-Length: 3239

{"jsonrpc":"2.0","id":0,"method":"initialize","params":{"processId":20392,"clientInfo":{"name":"vscode","version":"1.50.0"},"rootPath":"c:\\Users\\<USER>\\Desktop\\ESP32-2432S028例子\\arduino\\Source code\\3_4-3_Flash_PNG\\Flash_PNG","rootUri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG","capabilities":{"workspace":{"applyEdit":true,"workspaceEdit":{"documentChanges":true,"resourceOperations":["create","rename","delete"],"failureHandling":"textOnlyTransactional"},"didChangeConfiguration":{"dynamicRegistration":true},"didChangeWatchedFiles":{"dynamicRegistration":true},"symbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]}},"executeCommand":{"dynamicRegistration":true},"configuration":true,"workspaceFolders":true},"textDocument":{"publishDiagnostics":{"relatedInformation":true,"versionSupport":false,"tagSupport":{"valueSet":[1,2]}},"synchronization":{"dynamicRegistration":true,"willSave":true,"willSaveWaitUntil":true,"didSave":true},"completion":{"dynamicRegistration":true,"contextSupport":true,"completionItem":{"snippetSupport":true,"commitCharactersSupport":true,"documentationFormat":["markdown","plaintext"],"deprecatedSupport":true,"preselectSupport":true,"tagSupport":{"valueSet":[1]}},"completionItemKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]}},"hover":{"dynamicRegistration":true,"contentFormat":["markdown","plaintext"]},"signatureHelp":{"dynamicRegistration":true,"signatureInformation":{"documentationFormat":["markdown","plaintext"],"parameterInformation":{"labelOffsetSupport":true}},"contextSupport":true},"definition":{"dynamicRegistration":true,"linkSupport":true},"references":{"dynamicRegistration":true},"documentHighlight":{"dynamicRegistration":true},"documentSymbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"hierarchicalDocumentSymbolSupport":true},"codeAction":{"dynamicRegistration":true,"isPreferredSupport":true,"codeActionLiteralSupport":{"codeActionKind":{"valueSet":["","quickfix","refactor","refactor.extract","refactor.inline","refactor.rewrite","source","source.organizeImports"]}}},"codeLens":{"dynamicRegistration":true},"formatting":{"dynamicRegistration":true},"rangeFormatting":{"dynamicRegistration":true},"onTypeFormatting":{"dynamicRegistration":true},"rename":{"dynamicRegistration":true,"prepareSupport":true},"documentLink":{"dynamicRegistration":true,"tooltipSupport":true},"typeDefinition":{"dynamicRegistration":true,"linkSupport":true},"implementation":{"dynamicRegistration":true,"linkSupport":true},"colorProvider":{"dynamicRegistration":true},"foldingRange":{"dynamicRegistration":true,"rangeLimit":5000,"lineFoldingOnly":true},"declaration":{"dynamicRegistration":true,"linkSupport":true},"selectionRange":{"dynamicRegistration":true}},"window":{"workDoneProgress":true}},"initializationOptions":{},"trace":"off","workspaceFolders":[{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG","name":"Flash_PNG"}]}}
>>>
Content-Length: 598

{"id":0,"result":{"capabilities":{"textDocumentSync":2,"hoverProvider":true,"completionProvider":{"triggerCharacters":[".","\u003e",":"]},"signatureHelpProvider":{"triggerCharacters":["(",","]},"definitionProvider":true,"documentHighlightProvider":true,"documentSymbolProvider":true,"workspaceSymbolProvider":true,"codeActionProvider":true,"documentFormattingProvider":true,"documentRangeFormattingProvider":true,"documentOnTypeFormattingProvider":{"firstTriggerCharacter":"\n"},"renameProvider":false,"executeCommandProvider":{"commands":["clangd.applyFix","clangd.applyTweak"]}}},"jsonrpc":"2.0"}
<<<
Content-Length: 52

{"jsonrpc":"2.0","method":"initialized","params":{}}Content-Length: 2837

{"jsonrpc":"2.0","method":"textDocument/didOpen","params":{"textDocument":{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG/Flash_PNG.ino","languageId":"cpp","version":1,"text":"\n// This example renders a png file that is stored in a FLASH array\n// using the PNGdec library (available via library manager).\n\n// Include the PNG decoder library\n#include <PNGdec.h>\n#include \"panda_png.h\" // Image is stored here in an 8 bit array\n\nPNG png; // PNG decoder inatance\n\n#define MAX_IMAGE_WDITH 240 // Adjust for your images\n\nint16_t xpos = 0;\nint16_t ypos = 0;\n\n// Include the TFT library https://github.com/Bodmer/TFT_eSPI\n#include \"SPI.h\"\n#include <TFT_eSPI.h>              // Hardware-specific library\nTFT_eSPI tft = TFT_eSPI();         // Invoke custom library\n\n//====================================================================================\n//                                    Setup\n//====================================================================================\nvoid setup()\n{\n  Serial.begin(115200);\n  Serial.println(\"\\n\\n Using the PNGdec library\");\n\n  // Initialise the TFT\n  tft.begin();\n  tft.fillScreen(TFT_BLACK);\n\n  Serial.println(\"\\r\\nInitialisation done.\");\n}\n\n//====================================================================================\n//                                    Loop\n//====================================================================================\nvoid loop()\n{\n  int16_t rc = png.openFLASH((uint8_t *)panda_png, sizeof(panda_png), pngDraw);\n  if (rc == PNG_SUCCESS) {\n    Serial.println(\"Successfully png file\");\n    Serial.printf(\"image specs: (%d x %d), %d bpp, pixel type: %d\\n\", png.getWidth(), png.getHeight(), png.getBpp(), png.getPixelType());\n    tft.startWrite();\n    uint32_t dt = millis();\n    rc = png.decode(NULL, 0);\n    Serial.print(millis() - dt); Serial.println(\"ms\");\n    tft.endWrite();\n    // png.close(); // not needed for memory->memory decode\n  }\n  delay(3000);\n  tft.fillScreen(random(0x10000));\n}\n\n\n//=========================================v==========================================\n//                                      pngDraw\n//====================================================================================\n// This next function will be called during decoding of the png file to\n// render each image line to the TFT.  If you use a different TFT library\n// you will need to adapt this function to suit.\n// Callback function to draw pixels to the display\nvoid pngDraw(PNGDRAW *pDraw) {\n  uint16_t lineBuffer[MAX_IMAGE_WDITH];\n  png.getLineAsRGB565(pDraw, lineBuffer, PNG_RGB565_BIG_ENDIAN, 0xffffffff);\n  tft.pushImage(xpos, ypos + pDraw->y, pDraw->iWidth, 1, lineBuffer);\n}\n"}}}Content-Length: 695420

{"jsonrpc":"2.0","method":"textDocument/didOpen","params":{"textDocument":{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG/panda_png.h","languageId":"cpp","version":1,"text":"#include <pgmspace.h>\nconst uint8_t panda_png[] PROGMEM = {\n0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, \n0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x01, 0x0E, 0x08, 0x02, 0x00, 0x00, 0x00, 0x38, 0xD8, 0x0D, \n0x18, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x1B, 0xAF, 0x00, 0x00, 0x1B, \n0xAF, 0x01, 0x5E, 0x1A, 0x91, 0x1C, 0x00, 0x00, 0x20, 0x00, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, \n0x54, 0xBC, 0x4D, 0x93, 0x25, 0x59, 0x72, 0x1D, 0x76, 0x8E, 0xFB, 0xBD, 0x11, 0xF1, 0x5E, 0x66, \n0xD6, 0x67, 0x57, 0x55, 0x77, 0xF5, 0xD7, 0x7C, 0xF6, 0x4C, 0xCF, 0xF4, 0xCC, 0x08, 0x84, 0x48, \n0x60, 0x00, 0x92, 0x03, 0xD1, 0x00, 0x62, 0x23, 0x83, 0xD1, 0x4C, 0x1B, 0x2D, 0xA4, 0x3F, 0xA1, \n0xB5, 0x7E, 0x82, 0xFE, 0x97, 0x76, 0x5A, 0x88, 0x32, 0x2E, 0x08, 0x88, 0xA4, 0x34, 0x00, 0xC9, \n0xC1, 0x4C, 0x4F, 0x77, 0x57, 0x65, 0xE6, 0x8B, 0x88, 0x7