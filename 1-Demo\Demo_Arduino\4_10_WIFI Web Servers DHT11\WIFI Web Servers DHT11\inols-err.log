






Started logging.
2022/06/27 13:59:18 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_10_WIFI Web Servers DHT11\WIFI Web Servers DHT11\inols.log
2022/06/27 13:59:18 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/27 13:59:18 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server102612787
2022/06/27 13:59:18 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server102612787\sketch
2022/06/27 13:59:18 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/27 13:59:18 IDE --> initialize 0 [93m locked[0m
2022/06/27 13:59:18 IDE --> initialize 0 [93m unlocked[0m
2022/06/27 13:59:18 INIT--- initializing workbench
2022/06/27 13:59:18 INIT--- [93m locked[0m
2022/06/27 13:59:18 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/27 13:59:18     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_10_WIFI%20Web%20Servers%20DHT11/WIFI%20Web%20Servers%20DHT11)
2022/06/27 13:59:18 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\972759286 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server102612787 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\4_10_WIFI Web Servers DHT11\WIFI Web Servers DHT11
2022/06/27 13:59:18 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/27 13:59:20 INIT--- initializing workbench (done)
2022/06/27 13:59:20 INIT--- [93m unlocked[0m
2022/06/27 13:59:20 IDE --> initialized notif1 [93m read-locked[0m
2022/06/27 13:59:20 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/27 13:59:20 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/27 13:59:20 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/27 13:59:20 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/27 13:59:20 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/27 13:59:20 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m
