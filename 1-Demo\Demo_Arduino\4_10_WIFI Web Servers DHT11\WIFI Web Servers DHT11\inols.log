






Started logging.

<<<
Content-Length: 3351

{"jsonrpc":"2.0","id":0,"method":"initialize","params":{"processId":10900,"clientInfo":{"name":"vscode","version":"1.50.0"},"rootPath":"c:\\Users\\<USER>\\Desktop\\ESP32-2432S028例子\\arduino\\Source code\\4_10_WIFI Web Servers DHT11\\WIFI Web Servers DHT11","rootUri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_10_WIFI%20Web%20Servers%20DHT11/WIFI%20Web%20Servers%20DHT11","capabilities":{"workspace":{"applyEdit":true,"workspaceEdit":{"documentChanges":true,"resourceOperations":["create","rename","delete"],"failureHandling":"textOnlyTransactional"},"didChangeConfiguration":{"dynamicRegistration":true},"didChangeWatchedFiles":{"dynamicRegistration":true},"symbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]}},"executeCommand":{"dynamicRegistration":true},"configuration":true,"workspaceFolders":true},"textDocument":{"publishDiagnostics":{"relatedInformation":true,"versionSupport":false,"tagSupport":{"valueSet":[1,2]}},"synchronization":{"dynamicRegistration":true,"willSave":true,"willSaveWaitUntil":true,"didSave":true},"completion":{"dynamicRegistration":true,"contextSupport":true,"completionItem":{"snippetSupport":true,"commitCharactersSupport":true,"documentationFormat":["markdown","plaintext"],"deprecatedSupport":true,"preselectSupport":true,"tagSupport":{"valueSet":[1]}},"completionItemKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]}},"hover":{"dynamicRegistration":true,"contentFormat":["markdown","plaintext"]},"signatureHelp":{"dynamicRegistration":true,"signatureInformation":{"documentationFormat":["markdown","plaintext"],"parameterInformation":{"labelOffsetSupport":true}},"contextSupport":true},"definition":{"dynamicRegistration":true,"linkSupport":true},"references":{"dynamicRegistration":true},"documentHighlight":{"dynamicRegistration":true},"documentSymbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"hierarchicalDocumentSymbolSupport":true},"codeAction":{"dynamicRegistration":true,"isPreferredSupport":true,"codeActionLiteralSupport":{"codeActionKind":{"valueSet":["","quickfix","refactor","refactor.extract","refactor.inline","refactor.rewrite","source","source.organizeImports"]}}},"codeLens":{"dynamicRegistration":true},"formatting":{"dynamicRegistration":true},"rangeFormatting":{"dynamicRegistration":true},"onTypeFormatting":{"dynamicRegistration":true},"rename":{"dynamicRegistration":true,"prepareSupport":true},"documentLink":{"dynamicRegistration":true,"tooltipSupport":true},"typeDefinition":{"dynamicRegistration":true,"linkSupport":true},"implementation":{"dynamicRegistration":true,"linkSupport":true},"colorProvider":{"dynamicRegistration":true},"foldingRange":{"dynamicRegistration":true,"rangeLimit":5000,"lineFoldingOnly":true},"declaration":{"dynamicRegistration":true,"linkSupport":true},"selectionRange":{"dynamicRegistration":true}},"window":{"workDoneProgress":true}},"initializationOptions":{},"trace":"off","workspaceFolders":[{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_10_WIFI%20Web%20Servers%20DHT11/WIFI%20Web%20Servers%20DHT11","name":"WIFI Web Servers DHT11"}]}}
>>>
Content-Length: 598

{"id":0,"result":{"capabilities":{"textDocumentSync":2,"hoverProvider":true,"completionProvider":{"triggerCharacters":[".","\u003e",":"]},"signatureHelpProvider":{"triggerCharacters":["(",","]},"definitionProvider":true,"documentHighlightProvider":true,"documentSymbolProvider":true,"workspaceSymbolProvider":true,"codeActionProvider":true,"documentFormattingProvider":true,"documentRangeFormattingProvider":true,"documentOnTypeFormattingProvider":{"firstTriggerCharacter":"\n"},"renameProvider":false,"executeCommandProvider":{"commands":["clangd.applyFix","clangd.applyTweak"]}}},"jsonrpc":"2.0"}
<<<
Content-Length: 52

{"jsonrpc":"2.0","method":"initialized","params":{}}Content-Length: 4998

{"jsonrpc":"2.0","method":"textDocument/didOpen","params":{"textDocument":{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_10_WIFI%20Web%20Servers%20DHT11/WIFI%20Web%20Servers%20DHT11/WIFI%20Web%20Servers%20DHT11.ino","languageId":"cpp","version":1,"text":"/********************************************\n －－－－湖南创乐博智能科技有限公司－－－－\n  文件名：42.WIFI Web Servers DTH11.ino\n  版本：V2.0\n  author: zhulin\n  说明：WIFI Web Server DHT11温湿度传感器实验\n ********************************************/\n#include <WiFi.h>\n#include <ESPAsyncWebServer.h>\n#include <Adafruit_Sensor.h>\n#include <DHT.h>\n\n// 输入自己的WIFI账号和密码\nconst char* ssid = \"zhulin_Home\";\nconst char* password = \"zn6271239\";\n\n#define DHTPIN 17     // 连接DHT传感器的数字引脚\n\n// 取消对使用中的传感器类型的注释:\n#define DHTTYPE    DHT11     // DHT 11\n\n\nDHT dht(DHTPIN, DHTTYPE);\n\n// 在端口80上创建AsyncWebServer对象\nAsyncWebServer server(80);\n\nString readDHTTemperature() {\n  // 传感器读数也可能高达2秒“旧”(这是一个非常慢的传感器)\n  // 读取温度为摄氏度(默认值)\n  float t = dht.readTemperature();\n  // 读取温度为华氏度(isFahrenheit = true)\n  // float t = dht.readTemperature(true);\n  // 检查是否有任何读取失败并提前退出(再试一次)。\n  if (isnan(t)) {    \n    Serial.println(\"Failed to read from DHT sensor!\");\n    return \"--\";\n  }\n  else {\n    Serial.println(t);\n    return String(t);\n  }\n}\n\nString readDHTHumidity() {\n  // 传感器读数也可能高达2秒“旧”(这是一个非常慢的传感器)\n  float h = dht.readHumidity();\n  if (isnan(h)) {\n    Serial.println(\"Failed to read from DHT sensor!\");\n    return \"--\";\n  }\n  else {\n    Serial.println(h);\n    return String(h);\n  }\n}\n\nconst char index_html[] PROGMEM = R\"rawliteral(\n<!DOCTYPE HTML><html>\n<head>\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  <link rel=\"stylesheet\" href=\"https://use.fontawesome.com/releases/v5.7.2/css/all.css\" integrity=\"sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr\" crossorigin=\"anonymous\">\n  <style>\n    html {\n     font-family: Arial;\n     display: inline-block;\n     margin: 0px auto;\n     text-align: center;\n    }\n    h2 { font-size: 3.0rem; }\n    p { font-size: 3.0rem; }\n    .units { font-size: 1.2rem; }\n    .dht-labels{\n      font-size: 1.5rem;\n      vertical-align:middle;\n      padding-bottom: 15px;\n    }\n  </style>\n</head>\n<body>\n  <h2>ESP32 DHT11 Server</h2>\n  <p>\n    <i class=\"fas fa-thermometer-half\" style=\"color:#059e8a;\"></i> \n    <span class=\"dht-labels\">Temperature</span> \n    <span id=\"temperature\">%TEMPERATURE%</span>\n    <sup class=\"units\">&deg;C</sup>\n  </p>\n  <p>\n    <i class=\"fas fa-tint\" style=\"color:#00add6;\"></i> \n    <span class=\"dht-labels\">Humidity</span>\n    <span id=\"humidity\">%HUMIDITY%</span>\n    <sup class=\"units\">&percnt;</sup>\n  </p>\n</body>\n<script>\nsetInterval(function ( ) {\n  var xhttp = new XMLHttpRequest();\n  xhttp.onreadystatechange = function() {\n    if (this.readyState == 4 && this.status == 200) {\n      document.getElementById(\"temperature\").innerHTML = this.responseText;\n    }\n  };\n  xhttp.open(\"GET\", \"/temperature\", true);\n  xhttp.send();\n}, 10000 ) ;\n\nsetInterval(function ( ) {\n  var xhttp = new XMLHttpRequest();\n  xhttp.onreadystatechange = function() {\n    if (this.readyState == 4 && this.status == 200) {\n      document.getElementById(\"humidity\").innerHTML = this.responseText;\n    }\n  };\n  xhttp.open(\"GET\", \"/humidity\", true);\n  xhttp.send();\n}, 10000 ) ;\n</script>\n</html>)rawliteral\";\n\n// 用DHT值替换占位符\nString processor(const String& var){\n  //Serial.println(var);\n  if(var == \"TEMPERATURE\"){\n    return readDHTTemperature();\n  }\n  else if(var == \"HUMIDITY\"){\n    return readDHTHumidity();\n  }\n  return String();\n}\n\nvoid setup(){\n  // 用于调试的串口\n  Serial.begin(115200);\n\n  dht.begin();\n  \n  // 连接到无线网络\n  WiFi.begin(ssid, password);\n  while (WiFi.status() != WL_CONNECTED) {\n    delay(1000);\n    Serial.println(\"Connecting to WiFi..\");\n  }\n\n  // 打印ESP32本地IP地址\n  Serial.println(WiFi.localIP());\n\n  // 路由的根/网页\n  server.on(\"/\", HTTP_GET, [](AsyncWebServerRequest *request){\n    request->send_P(200, \"text/html\", index_html, processor);\n  });\n  server.on(\"/temperature\", HTTP_GET, [](AsyncWebServerRequest *request){\n    request->send_P(200, \"text/plain\", readDHTTemperature().c_str());\n  });\n  server.on(\"/humidity\", HTTP_GET, [](AsyncWebServerRequest *request){\n    request->send_P(200, \"text/plain\", readDHTHumidity().c_str());\n  });\n\n  // 启动服务器\n  server.begin();\n}\n \nvoid loop(){\n  \n}\n"}}}Content-Length: 388

{"jsonrpc":"2.0","id":1,"method":"textDocument/codeAction","params":{"textDocument":{"uri":"file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/4_10_WIFI%20Web%20Servers%20DHT11/WIFI%20Web%20Servers%20DHT11/WIFI%20Web%20Servers%20DHT11.ino"},"range":{"start":{"line":0,"character":0},"end":{"line":0,"character":0}},"context":{"diagnostics":[]}}}