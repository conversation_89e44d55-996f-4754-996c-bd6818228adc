/*
 * Time Manager Header - Minimal Version
 */

#ifndef TIME_MANAGER_H
#define TIME_MANAGER_H

#include <WiFi.h>
#include <WiFiUdp.h>
#include <NTPClient.h>
#include <time.h>
#include "config.h"

class TimeManager {
private:
  WiFiUDP ntpUDP;
  NTPClient* timeClient;
  String ntpServer;
  String timezone;
  long timezoneOffset;
  bool ntpEnabled;
  bool timeSet;
  unsigned long lastNTPUpdate;
  
public:
  TimeManager();
  ~TimeManager();
  
  bool begin();
  bool begin(String ntpServerAddr, String timezoneStr);
  void reset();
  
  bool enableNTP();
  void disableNTP();
  bool updateFromNTP();
  
  String getCurrentTime();
  String getCurrentDate();
  String getCurrentDateTime();
  String getCurrentTimestamp();
  time_t getCurrentEpoch();
  
  void setTimezone(String tz);
  String getTimezone() const { return timezone; }
  
  void setNTPServer(String server);
  String getNTPServer() const { return ntpServer; }
  
  bool setTime(int hour, int minute, int second);
  bool setDate(int year, int month, int day);
  bool setDateTime(int year, int month, int day, int hour, int minute, int second);
  
  bool isTimeSet() const { return timeSet; }
  bool isTimeValid();
  bool needsNTPUpdate();
  
  void update();
  void forceNTPUpdate();
  
  unsigned long getUptime();
  String getUptimeString();
  
  void printTimeInfo();
};

#endif // TIME_MANAGER_H
