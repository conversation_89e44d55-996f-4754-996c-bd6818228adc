/*
 * RFID Handler Header - Minimal Version
 */

#ifndef RFID_HANDLER_H
#define RFID_HANDLER_H

#include <MFRC522.h>
#include <SPI.h>
#include "config.h"

class RFIDHandler {
private:
  MFRC522 mfrc522;
  bool initialized;
  String lastCardUID;
  unsigned long lastReadTime;
  
public:
  RFIDHandler();
  ~RFIDHandler();
  
  bool begin();
  void reset();
  bool isInitialized() const { return initialized; }
  
  bool isCardPresent();
  String readCardUID();
  
  void enableReader();
  void disableReader();
  
  bool testReader();
  void printReaderInfo();
  String getReaderVersion();
};

#endif // RFID_HANDLER_H
