/*
 * Web Server Manager Header
 * Handles web interface and API endpoints
 */

#ifndef WEB_SERVER_H
#define WEB_SERVER_H

#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include "config.h"
#include "employee_db.h"
#include "data_manager.h"

class WebServerManager {
private:
  AsyncWebServer* server;
  EmployeeDB* employeeDB;
  DataManager* dataManager;
  
  bool initialized;
  bool serverRunning;
  int serverPort;
  
  // Authentication
  String adminUsername;
  String adminPassword;
  bool authEnabled;
  
  // Statistics
  unsigned long totalRequests;
  unsigned long apiRequests;
  unsigned long webRequests;
  
  // Private methods
  void setupRoutes();
  void setupAPIRoutes();
  void setupWebRoutes();
  
  // Authentication helpers
  bool authenticate(AsyncWebServerRequest* request);
  String generateAuthToken();
  bool validateAuthToken(String token);
  
  // API handlers
  void handleGetEmployees(AsyncWebServerRequest* request);
  void handleAddEmployee(AsyncWebServerRequest* request);
  void handleUpdateEmployee(AsyncWebServerRequest* request);
  void handleDeleteEmployee(AsyncWebServerRequest* request);
  void handleGetAttendance(AsyncWebServerRequest* request);
  void handleGetReports(AsyncWebServerRequest* request);
  void handleSystemStatus(AsyncWebServerRequest* request);
  void handleSystemConfig(AsyncWebServerRequest* request);
  
  // Web page handlers
  void handleRoot(AsyncWebServerRequest* request);
  void handleLogin(AsyncWebServerRequest* request);
  void handleDashboard(AsyncWebServerRequest* request);
  void handleEmployeeManagement(AsyncWebServerRequest* request);
  void handleReports(AsyncWebServerRequest* request);
  void handleSettings(AsyncWebServerRequest* request);
  
  // Utility methods
  String generateEmployeeJSON(const Employee& emp);
  String generateAttendanceJSON(const std::vector<AttendanceRecord>& records);
  String generateSystemStatusJSON();
  void sendJSONResponse(AsyncWebServerRequest* request, String json, int code = 200);
  void sendErrorResponse(AsyncWebServerRequest* request, String message, int code = 400);
  
public:
  WebServerManager();
  ~WebServerManager();
  
  // Initialization
  bool begin();
  bool begin(int port);
  bool begin(EmployeeDB* empDB, DataManager* dataMgr);
  void stop();
  bool isRunning() const { return serverRunning; }
  
  // Server management
  void handleClient();
  void setPort(int port) { serverPort = port; }
  int getPort() const { return serverPort; }
  
  // Authentication
  void enableAuth(String username, String password);
  void disableAuth();
  bool isAuthEnabled() const { return authEnabled; }
  
  // Statistics
  unsigned long getTotalRequests() const { return totalRequests; }
  unsigned long getAPIRequests() const { return apiRequests; }
  unsigned long getWebRequests() const { return webRequests; }
  void resetStatistics();
  
  // Configuration
  void setEmployeeDB(EmployeeDB* empDB) { employeeDB = empDB; }
  void setDataManager(DataManager* dataMgr) { dataManager = dataMgr; }
  
  // Status
  String getServerInfo();
  void printServerStatus();
};

#endif // WEB_SERVER_H
