/*
 * Time Manager Header
 * Handles NTP synchronization and time operations
 */

#ifndef TIME_MANAGER_H
#define TIME_MANAGER_H

#include <WiFi.h>
#include <WiFiUdp.h>
#include <NTPClient.h>
#include <time.h>
#include "config.h"

class TimeManager {
private:
  WiFiUDP ntpUDP;
  NTPClient* timeClient;
  
  // Time settings
  String ntpServer;
  String timezone;
  long timezoneOffset;
  bool ntpEnabled;
  bool timeSet;
  
  // Update tracking
  unsigned long lastNTPUpdate;
  unsigned long lastTimeUpdate;
  unsigned long ntpUpdateInterval;
  
  // Time formatting
  String timeFormat;
  String dateFormat;
  bool use24HourFormat;
  
  // Private methods
  void parseTimezone(String tz);
  long calculateTimezoneOffset(String tz);
  void setSystemTime(unsigned long epochTime);
  String formatTime(time_t rawTime, String format);
  
public:
  TimeManager();
  ~TimeManager();
  
  // Initialization
  bool begin();
  bool begin(String ntpServerAddr, String timezoneStr);
  void reset();
  
  // NTP operations
  bool enableNTP();
  void disableNTP();
  bool updateFromNTP();
  bool isNTPEnabled() const { return ntpEnabled; }
  unsigned long getLastNTPUpdate() const { return lastNTPUpdate; }
  
  // Time getting
  String getCurrentTime();
  String getCurrentDate();
  String getCurrentDateTime();
  String getCurrentTimestamp(); // ISO 8601 format
  time_t getCurrentEpoch();
  
  // Time formatting
  String formatTime(String format);
  String formatDate(String format);
  String formatDateTime(String format);
  void setTimeFormat(String format) { timeFormat = format; }
  void setDateFormat(String format) { dateFormat = format; }
  void set24HourFormat(bool enabled) { use24HourFormat = enabled; }
  
  // Timezone management
  void setTimezone(String tz);
  String getTimezone() const { return timezone; }
  void setTimezoneOffset(long offset) { timezoneOffset = offset; }
  long getTimezoneOffset() const { return timezoneOffset; }
  
  // NTP server management
  void setNTPServer(String server);
  String getNTPServer() const { return ntpServer; }
  void setUpdateInterval(unsigned long interval) { ntpUpdateInterval = interval; }
  
  // Manual time setting
  bool setTime(int hour, int minute, int second);
  bool setDate(int year, int month, int day);
  bool setDateTime(int year, int month, int day, int hour, int minute, int second);
  
  // Time validation
  bool isTimeSet() const { return timeSet; }
  bool isTimeValid();
  bool needsNTPUpdate();
  
  // Update operations
  void update();
  void forceNTPUpdate();
  
  // Utility functions
  String getTimezoneName();
  bool isDaylightSaving();
  int getDayOfWeek(); // 0 = Sunday, 1 = Monday, etc.
  String getDayName();
  String getMonthName();
  
  // Time calculations
  unsigned long getUptime(); // System uptime in seconds
  String getUptimeString();
  long getTimeDifference(String timestamp1, String timestamp2); // In seconds
  bool isWorkingHours(String shiftStart, String shiftEnd);
  
  // Debugging
  void printTimeInfo();
  String getStatusString();
};

#endif // TIME_MANAGER_H
