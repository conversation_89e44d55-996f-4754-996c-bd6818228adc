const uint8_t FreeMonoBoldOblique18pt7bBitmaps[] PROGMEM = {
  0x0F, 0x07, 0xC7, 0xE3, 0xF1, 0xF0, 0xF8, 0xFC, 0x7C, 0x3E, 0x1F, 0x0F,
  0x07, 0x87, 0xC3, 0xC1, 0xE0, 0x60, 0x00, 0x38, 0x3E, 0x1F, 0x0F, 0x83,
  0x80, 0xF8, 0xFF, 0x0E, 0xF1, 0xEF, 0x1E, 0xE1, 0xCE, 0x1C, 0xC1, 0xCC,
  0x18, 0xC1, 0x88, 0x18, 0x00, 0xE3, 0x80, 0x79, 0xE0, 0x1C, 0x70, 0x07,
  0x1C, 0x03, 0xCF, 0x00, 0xF3, 0xC0, 0x38, 0xE0, 0x7F, 0xFF, 0x3F, 0xFF,
  0xCF, 0xFF, 0xF3, 0xFF, 0xF8, 0x3C, 0xF0, 0x0F, 0x3C, 0x03, 0x8E, 0x0F,
  0xFF, 0xE3, 0xFF, 0xFC, 0xFF, 0xFF, 0x3F, 0xFF, 0x83, 0xCF, 0x00, 0xF3,
  0xC0, 0x38, 0xE0, 0x1E, 0x78, 0x07, 0x9E, 0x01, 0xC7, 0x00, 0x71, 0xC0,
  0x00, 0x00, 0x38, 0x00, 0x0E, 0x00, 0x07, 0x80, 0x03, 0xF0, 0x03, 0xFF,
  0x81, 0xFF, 0xF0, 0xFF, 0xF8, 0x3C, 0x1E, 0x1E, 0x07, 0x87, 0x80, 0x01,
  0xF0, 0x00, 0x7F, 0xC0, 0x0F, 0xFC, 0x01, 0xFF, 0x80, 0x07, 0xF0, 0x00,
  0x3C, 0x70, 0x0F, 0x3C, 0x03, 0xCF, 0x83, 0xE3, 0xFF, 0xF8, 0xFF, 0xFC,
  0x3F, 0xFE, 0x0C, 0xFE, 0x00, 0x1C, 0x00, 0x07, 0x00, 0x03, 0xC0, 0x00,
  0xF0, 0x00, 0x18, 0x00, 0x03, 0xC0, 0x0F, 0xE0, 0x1C, 0x70, 0x30, 0x30,
  0x30, 0x30, 0x30, 0x70, 0x38, 0xE0, 0x1F, 0xC3, 0x0F, 0x1F, 0x01, 0xFC,
  0x0F, 0xE0, 0x7F, 0x00, 0xF8, 0xF0, 0x83, 0xF8, 0x07, 0x1C, 0x0E, 0x0C,
  0x0C, 0x0C, 0x0C, 0x1C, 0x0E, 0x38, 0x07, 0xF0, 0x03, 0xC0, 0x00, 0x7A,
  0x01, 0xFF, 0x03, 0xFF, 0x07, 0xFE, 0x0F, 0x9C, 0x0F, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x07, 0x80, 0x1F, 0x80, 0x3F, 0xC0, 0x7F, 0xCF, 0x79, 0xFF,
  0xF1, 0xFE, 0xF1, 0xFC, 0xF0, 0xF8, 0xFF, 0xFE, 0xFF, 0xFE, 0x7F, 0xFE,
  0x1F, 0xBC, 0x7B, 0xFD, 0xEF, 0x73, 0x9C, 0xC6, 0x00, 0x01, 0xC0, 0xF0,
  0x3C, 0x1E, 0x0F, 0x03, 0xC1, 0xE0, 0x70, 0x3C, 0x0F, 0x07, 0x81, 0xE0,
  0x78, 0x3C, 0x0F, 0x03, 0xC0, 0xF0, 0x3C, 0x0F, 0x03, 0xC0, 0xF0, 0x3E,
  0x07, 0x81, 0xE0, 0x7C, 0x1F, 0x03, 0x80, 0x07, 0x03, 0xC0, 0xF8, 0x3E,
  0x07, 0x81, 0xF0, 0x3C, 0x0F, 0x03, 0xC0, 0xF0, 0x3C, 0x0F, 0x03, 0xC0,
  0xF0, 0x78, 0x1E, 0x07, 0x81, 0xC0, 0xF0, 0x3C, 0x1E, 0x07, 0x83, 0xC1,
  0xE0, 0x78, 0x3C, 0x0E, 0x00, 0x00, 0xC0, 0x03, 0xC0, 0x07, 0x00, 0x0E,
  0x02, 0x3C, 0x0F, 0xFF, 0xFF, 0xFF, 0xBF, 0xFE, 0x1F, 0xF0, 0x1F, 0x80,
  0x7F, 0x81, 0xEF, 0x07, 0x8F, 0x0F, 0x1E, 0x08, 0x10, 0x00, 0x00, 0x70,
  0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xE0, 0x00, 0x38, 0x00,
  0x1E, 0x03, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x0F,
  0x00, 0x03, 0xC0, 0x00, 0xE0, 0x00, 0x38, 0x00, 0x0E, 0x00, 0x07, 0x80,
  0x01, 0xC0, 0x00, 0x70, 0x00, 0x0F, 0x87, 0x87, 0x83, 0x83, 0xC1, 0xC1,
  0xC0, 0xC0, 0xE0, 0x60, 0x00, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFE, 0x77, 0xFF, 0xF7, 0x00, 0x00, 0x00, 0x38, 0x00, 0x03, 0xC0,
  0x00, 0x1C, 0x00, 0x01, 0xE0, 0x00, 0x1E, 0x00, 0x01, 0xE0, 0x00, 0x0F,
  0x00, 0x00, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x07, 0x80, 0x00,
  0x78, 0x00, 0x03, 0xC0, 0x00, 0x3C, 0x00, 0x03, 0xC0, 0x00, 0x1C, 0x00,
  0x01, 0xE0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0xF0,
  0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x07, 0x80, 0x00, 0x78, 0x00, 0x03,
  0xC0, 0x00, 0x3C, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0xFC, 0x01, 0xFF,
  0x01, 0xFF, 0xC1, 0xFF, 0xE1, 0xF1, 0xF9, 0xE0, 0x7C, 0xF0, 0x1E, 0xF0,
  0x0F, 0x78, 0x07, 0xB8, 0x03, 0x9C, 0x03, 0xDE, 0x01, 0xCF, 0x00, 0xE7,
  0x00, 0x73, 0xC0, 0x79, 0xE0, 0x3C, 0xF0, 0x1C, 0x78, 0x1E, 0x3E, 0x1E,
  0x0F, 0xFF, 0x07, 0xFF, 0x01, 0xFF, 0x00, 0x7E, 0x00, 0x00, 0x7C, 0x03,
  0xF8, 0x0F, 0xE0, 0x7F, 0xC0, 0xF7, 0x81, 0x8F, 0x00, 0x1C, 0x00, 0x38,
  0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07, 0x00, 0x0E, 0x00, 0x3C, 0x00,
  0x78, 0x00, 0xF0, 0x01, 0xC0, 0x03, 0x81, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xEF, 0xFF, 0xC0, 0x00, 0x1F, 0x00, 0x07, 0xFC, 0x00, 0xFF, 0xE0, 0x1F,
  0xFF, 0x03, 0xC1, 0xF0, 0x78, 0x0F, 0x07, 0x80, 0xF0, 0x70, 0x0F, 0x00,
  0x01, 0xE0, 0x00, 0x3E, 0x00, 0x07, 0xC0, 0x00, 0xF8, 0x00, 0x3F, 0x00,
  0x07, 0xE0, 0x01, 0xFC, 0x00, 0x3F, 0x80, 0x07, 0xE0, 0x01, 0xF8, 0x00,
  0x3F, 0x03, 0x87, 0xFF, 0xF8, 0x7F, 0xFF, 0x87, 0xFF, 0xF8, 0xFF, 0xFF,
  0x00, 0x00, 0xFE, 0x00, 0xFF, 0xC0, 0x7F, 0xF8, 0x3F, 0xFF, 0x0E, 0x07,
  0xC0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x1F, 0x00, 0x07, 0x80, 0x1F, 0xC0,
  0x0F, 0xE0, 0x03, 0xF0, 0x00, 0xFF, 0x00, 0x03, 0xE0, 0x00, 0x78, 0x00,
  0x1E, 0x00, 0x07, 0x80, 0x03, 0xC0, 0x03, 0xF1, 0xFF, 0xF8, 0xFF, 0xFC,
  0x3F, 0xFE, 0x03, 0xFE, 0x00, 0x00, 0x1F, 0x00, 0x3F, 0x00, 0x7F, 0x00,
  0xFE, 0x00, 0xFE, 0x01, 0xEE, 0x03, 0xDE, 0x07, 0x9E, 0x0F, 0x1C, 0x1E,
  0x1C, 0x3C, 0x3C, 0x78, 0x3C, 0xFF, 0xFE, 0xFF, 0xFE, 0xFF, 0xFE, 0xFF,
  0xFC, 0x00, 0x70, 0x03, 0xFC, 0x07, 0xFC, 0x07, 0xFC, 0x07, 0xF8, 0x07,
  0xFF, 0xC1, 0xFF, 0xF0, 0x7F, 0xFC, 0x3F, 0xFE, 0x0F, 0x00, 0x03, 0xC0,
  0x00, 0xE0, 0x00, 0x3B, 0xE0, 0x1F, 0xFE, 0x07, 0xFF, 0xC1, 0xFF, 0xF8,
  0x78, 0x3E, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00,
  0x0F, 0x18, 0x0F, 0xCF, 0xFF, 0xE3, 0xFF, 0xF0, 0x7F, 0xF8, 0x07, 0xF0,
  0x00, 0x00, 0x0F, 0xC0, 0x0F, 0xFC, 0x03, 0xFF, 0x81, 0xFF, 0xE0, 0x7F,
  0x00, 0x1F, 0x80, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x9F,
  0x01, 0xEF, 0xF0, 0x3F, 0xFF, 0x0F, 0xFF, 0xF1, 0xFC, 0x3E, 0x3E, 0x03,
  0xC7, 0x80, 0x78, 0xF0, 0x0F, 0x1E, 0x03, 0xC3, 0xE0, 0xF8, 0x7F, 0xFE,
  0x07, 0xFF, 0x80, 0x7F, 0xE0, 0x07, 0xF0, 0x00, 0x7F, 0xFF, 0x7F, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x0E, 0x00, 0x1E, 0x00, 0x1C, 0x00, 0x3C,
  0x00, 0x78, 0x00, 0x70, 0x00, 0xF0, 0x00, 0xE0, 0x01, 0xE0, 0x01, 0xC0,
  0x03, 0xC0, 0x07, 0x80, 0x07, 0x80, 0x0F, 0x00, 0x0E, 0x00, 0x1E, 0x00,
  0x1C, 0x00, 0x1C, 0x00, 0x00, 0x7E, 0x00, 0x3F, 0xF0, 0x0F, 0xFF, 0x03,
  0xFF, 0xF0, 0xF8, 0x3E, 0x3E, 0x03, 0xC7, 0x80, 0x78, 0xF0, 0x0F, 0x1E,
  0x03, 0xC3, 0xE0, 0xF0, 0x3F, 0xFC, 0x03, 0xFF, 0x00, 0xFF, 0xE0, 0x7F,
  0xFE, 0x1F, 0x83, 0xE3, 0xC0, 0x3C, 0xF0, 0x07, 0x9E, 0x01, 0xF3, 0xE0,
  0x7C, 0x7F, 0xFF, 0x87, 0xFF, 0xE0, 0x7F, 0xF0, 0x03, 0xF8, 0x00, 0x00,
  0x7E, 0x00, 0x7F, 0xC0, 0x3F, 0xF8, 0x1F, 0xFE, 0x0F, 0x87, 0xC3, 0xC0,
  0xF1, 0xE0, 0x3C, 0x78, 0x0F, 0x1E, 0x03, 0xC7, 0x81, 0xF1, 0xF1, 0xFC,
  0x7F, 0xFE, 0x0F, 0xFF, 0x81, 0xFD, 0xE0, 0x3E, 0xF0, 0x00, 0x7C, 0x00,
  0x3E, 0x00, 0x1F, 0x00, 0x1F, 0x81, 0xFF, 0xC0, 0xFF, 0xE0, 0x3F, 0xE0,
  0x07, 0xE0, 0x00, 0x1C, 0x7C, 0xF9, 0xF1, 0xC0, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x8F, 0x9F, 0x3E, 0x38, 0x01, 0xC0, 0x7C, 0x0F, 0x81, 0xF0, 0x3C,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xC0, 0xF0, 0x1E,
  0x07, 0x80, 0xE0, 0x38, 0x07, 0x01, 0xC0, 0x30, 0x0E, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0xFC, 0x00, 0xFE, 0x00, 0xFE, 0x00,
  0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x00, 0xFE, 0x00, 0x0F, 0xE0, 0x00, 0xFE,
  0x00, 0x1F, 0xC0, 0x01, 0xFC, 0x00, 0x1F, 0xC0, 0x01, 0xF0, 0x00, 0x38,
  0x3F, 0xFF, 0xEF, 0xFF, 0xFD, 0xFF, 0xFF, 0x9F, 0xFF, 0xE0, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0xFF, 0xF7, 0xFF, 0xFE, 0xFF, 0xFF, 0xDF, 0xFF, 0xF0,
  0x00, 0x00, 0x03, 0x80, 0x00, 0xFC, 0x00, 0x0F, 0xE0, 0x00, 0x7F, 0x00,
  0x07, 0xF0, 0x00, 0x3F, 0x80, 0x01, 0xFC, 0x00, 0x1F, 0xC0, 0x0F, 0xE0,
  0x07, 0xF0, 0x07, 0xF8, 0x03, 0xF8, 0x01, 0xFC, 0x00, 0x3E, 0x00, 0x07,
  0x00, 0x00, 0x07, 0xE0, 0xFF, 0xC7, 0xFF, 0xBF, 0xFF, 0xF0, 0x7F, 0x80,
  0xFE, 0x03, 0xC0, 0x0F, 0x00, 0x78, 0x0F, 0xE1, 0xFE, 0x0F, 0xF0, 0x7E,
  0x01, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x70, 0x03, 0xE0, 0x0F, 0x80, 0x3E,
  0x00, 0x70, 0x00, 0x00, 0x3E, 0x00, 0x3F, 0xE0, 0x1F, 0xF8, 0x0F, 0x0F,
  0x07, 0x01, 0xC3, 0x80, 0x71, 0xE0, 0x1C, 0x70, 0x0E, 0x18, 0x0F, 0x8E,
  0x1F, 0xE3, 0x8F, 0xF0, 0xE7, 0x9C, 0x33, 0xC7, 0x1C, 0xE1, 0xC7, 0x38,
  0x71, 0xCF, 0x18, 0x73, 0xFE, 0x38, 0x7F, 0xCE, 0x0F, 0xF3, 0x80, 0x00,
  0xE0, 0x00, 0x38, 0x00, 0x0F, 0x00, 0x01, 0xE0, 0xC0, 0x7F, 0xF0, 0x0F,
  0xF8, 0x01, 0xF8, 0x00, 0x01, 0xFF, 0x80, 0x07, 0xFE, 0x00, 0x1F, 0xF8,
  0x00, 0x7F, 0xE0, 0x00, 0x3F, 0xC0, 0x00, 0xFF, 0x00, 0x07, 0xBC, 0x00,
  0x1C, 0xF0, 0x00, 0xF3, 0xC0, 0x07, 0x87, 0x80, 0x1E, 0x1E, 0x00, 0xF0,
  0x78, 0x07, 0xFF, 0xE0, 0x1F, 0xFF, 0x80, 0xFF, 0xFF, 0x07, 0xFF, 0xFC,
  0x1E, 0x00, 0xF1, 0xFE, 0x1F, 0xFF, 0xF8, 0x7F, 0xFF, 0xE1, 0xFF, 0xFF,
  0x07, 0xF8, 0x0F, 0xFF, 0xC0, 0x7F, 0xFF, 0x87, 0xFF, 0xFC, 0x1F, 0xFF,
  0xF0, 0x38, 0x0F, 0x81, 0xC0, 0x3C, 0x1E, 0x01, 0xE0, 0xF0, 0x3E, 0x07,
  0xFF, 0xE0, 0x3F, 0xFE, 0x03, 0xFF, 0xF8, 0x1F, 0xFF, 0xE0, 0xE0, 0x1F,
  0x87, 0x00, 0x3C, 0x38, 0x01, 0xE3, 0xC0, 0x0F, 0x1E, 0x00, 0xF3, 0xFF,
  0xFF, 0xBF, 0xFF, 0xF9, 0xFF, 0xFF, 0x8F, 0xFF, 0xF0, 0x00, 0x00, 0x7F,
  0x30, 0x0F, 0xFF, 0xC1, 0xFF, 0xFE, 0x1F, 0xFF, 0xF1, 0xF8, 0x3F, 0x1F,
  0x00, 0x78, 0xF0, 0x03, 0xCF, 0x80, 0x1C, 0x78, 0x00, 0x03, 0xC0, 0x00,
  0x3C, 0x00, 0x01, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x03, 0xC0,
  0x00, 0x1F, 0x00, 0x38, 0x7E, 0x07, 0xC3, 0xFF, 0xFC, 0x0F, 0xFF, 0xC0,
  0x3F, 0xFC, 0x00, 0x7F, 0x80, 0x00, 0x0F, 0xFF, 0x80, 0x7F, 0xFE, 0x07,
  0xFF, 0xF8, 0x1F, 0xFF, 0xE0, 0x78, 0x1F, 0x03, 0x80, 0x7C, 0x1C, 0x01,
  0xE1, 0xE0, 0x0F, 0x0F, 0x00, 0x78, 0x70, 0x03, 0xC3, 0x80, 0x1E, 0x1C,
  0x00, 0xF1, 0xE0, 0x0F, 0x0F, 0x00, 0x78, 0x70, 0x07, 0xC3, 0x80, 0x7C,
  0x3C, 0x07, 0xC3, 0xFF, 0xFC, 0x3F, 0xFF, 0xC1, 0xFF, 0xFC, 0x0F, 0xFF,
  0x80, 0x00, 0x07, 0xFF, 0xFC, 0x3F, 0xFF, 0xF0, 0xFF, 0xFF, 0xC3, 0xFF,
  0xFF, 0x03, 0xC0, 0x3C, 0x0F, 0x00, 0xE0, 0x3C, 0x73, 0x80, 0xE3, 0xCC,
  0x03, 0xFF, 0x00, 0x1F, 0xFC, 0x00, 0x7F, 0xE0, 0x01, 0xFF, 0x80, 0x07,
  0x1E, 0x00, 0x3C, 0x70, 0x00, 0xF0, 0x07, 0x03, 0xC0, 0x1C, 0x0E, 0x00,
  0xF1, 0xFF, 0xFF, 0xC7, 0xFF, 0xFE, 0x3F, 0xFF, 0xF8, 0x7F, 0xFF, 0xE0,
  0x07, 0xFF, 0xFE, 0x1F, 0xFF, 0xFC, 0x3F, 0xFF, 0xF0, 0x7F, 0xFF, 0xE0,
  0x3C, 0x01, 0xC0, 0x70, 0x07, 0x80, 0xE1, 0x8E, 0x03, 0xC7, 0x1C, 0x07,
  0xFE, 0x00, 0x0F, 0xFC, 0x00, 0x1F, 0xF8, 0x00, 0x3F, 0xF0, 0x00, 0xF1,
  0xC0, 0x01, 0xE3, 0x80, 0x03, 0x80, 0x00, 0x07, 0x00, 0x00, 0x1E, 0x00,
  0x00, 0xFF, 0xE0, 0x03, 0xFF, 0xC0, 0x07, 0xFF, 0x80, 0x0F, 0xFE, 0x00,
  0x00, 0x00, 0x3F, 0x18, 0x0F, 0xFF, 0xC0, 0xFF, 0xFE, 0x0F, 0xFF, 0xF0,
  0xFC, 0x0F, 0x0F, 0x80, 0x38, 0xF8, 0x01, 0x87, 0x80, 0x00, 0x78, 0x00,
  0x03, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x7F, 0xEF, 0x07, 0xFF, 0x78,
  0x3F, 0xFB, 0xC0, 0xFF, 0x9E, 0x00, 0x38, 0xFC, 0x03, 0xC3, 0xFF, 0xFE,
  0x1F, 0xFF, 0xE0, 0x3F, 0xFC, 0x00, 0x7F, 0x80, 0x00, 0x03, 0xF8, 0xFE,
  0x0F, 0xF3, 0xFC, 0x1F, 0xE7, 0xF8, 0x3F, 0x8F, 0xE0, 0x3C, 0x07, 0x80,
  0x70, 0x0E, 0x00, 0xE0, 0x1C, 0x03, 0xC0, 0x78, 0x07, 0x80, 0xF0, 0x0F,
  0xFF, 0xC0, 0x1F, 0xFF, 0x80, 0x3F, 0xFF, 0x00, 0xFF, 0xFE, 0x01, 0xE0,
  0x3C, 0x03, 0x80, 0x70, 0x07, 0x00, 0xE0, 0x1E, 0x03, 0xC0, 0xFF, 0x1F,
  0xE1, 0xFE, 0x7F, 0xC7, 0xFC, 0xFF, 0x87, 0xF1, 0xFE, 0x00, 0x07, 0xFF,
  0xE1, 0xFF, 0xFC, 0x3F, 0xFF, 0x87, 0xFF, 0xE0, 0x07, 0x80, 0x00, 0xE0,
  0x00, 0x1C, 0x00, 0x03, 0x80, 0x00, 0xF0, 0x00, 0x1E, 0x00, 0x03, 0x80,
  0x00, 0x70, 0x00, 0x1E, 0x00, 0x03, 0xC0, 0x00, 0x78, 0x00, 0x0E, 0x00,
  0x01, 0xC0, 0x0F, 0xFF, 0xC3, 0xFF, 0xF8, 0x7F, 0xFF, 0x07, 0xFF, 0xE0,
  0x00, 0x3F, 0xFE, 0x00, 0xFF, 0xFC, 0x01, 0xFF, 0xF8, 0x03, 0xFF, 0xE0,
  0x00, 0x1C, 0x00, 0x00, 0x38, 0x00, 0x00, 0x70, 0x00, 0x01, 0xE0, 0x00,
  0x03, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x0E, 0x00, 0x80, 0x1C, 0x03, 0x80,
  0x78, 0x0F, 0x00, 0xF0, 0x1E, 0x01, 0xC0, 0x38, 0x07, 0x80, 0x70, 0x1F,
  0x01, 0xFF, 0xFC, 0x03, 0xFF, 0xF0, 0x03, 0xFF, 0xC0, 0x00, 0xFC, 0x00,
  0x00, 0x07, 0xF8, 0xFC, 0x1F, 0xFB, 0xFC, 0x3F, 0xE7, 0xF0, 0x7F, 0xCF,
  0xE0, 0x3C, 0x1E, 0x00, 0x70, 0xF8, 0x00, 0xE3, 0xE0, 0x03, 0xCF, 0x00,
  0x07, 0xFC, 0x00, 0x0F, 0xF0, 0x00, 0x1F, 0xF0, 0x00, 0x3F, 0xF0, 0x00,
  0xF9, 0xF0, 0x01, 0xE1, 0xE0, 0x03, 0x83, 0xE0, 0x07, 0x03, 0xC0, 0x1E,
  0x07, 0x80, 0xFF, 0x8F, 0xE3, 0xFF, 0x0F, 0xC7, 0xFE, 0x1F, 0x8F, 0xF8,
  0x3E, 0x00, 0x0F, 0xFE, 0x00, 0xFF, 0xF0, 0x1F, 0xFE, 0x00, 0xFF, 0xE0,
  0x01, 0xE0, 0x00, 0x1E, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00, 0x03, 0xC0,
  0x00, 0x3C, 0x00, 0x03, 0x80, 0x00, 0x38, 0x00, 0x07, 0x80, 0x60, 0x78,
  0x0F, 0x07, 0x80, 0xF0, 0x70, 0x0E, 0x07, 0x00, 0xE7, 0xFF, 0xFE, 0xFF,
  0xFF, 0xEF, 0xFF, 0xFE, 0xFF, 0xFF, 0xC0, 0x0F, 0xC0, 0x1F, 0x87, 0xE0,
  0x0F, 0xC7, 0xF8, 0x0F, 0xE1, 0xFC, 0x0F, 0xE0, 0x7E, 0x07, 0xE0, 0x3F,
  0x07, 0xF0, 0x3F, 0xC7, 0xF8, 0x1F, 0xE3, 0xF8, 0x0E, 0xF3, 0xDC, 0x07,
  0x7B, 0xDE, 0x03, 0x9F, 0xEF, 0x03, 0xCF, 0xE7, 0x81, 0xE7, 0xE3, 0x80,
  0xE3, 0xF1, 0xC0, 0x70, 0xF1, 0xE0, 0x38, 0x70, 0xF0, 0x3C, 0x00, 0x70,
  0x3F, 0xC1, 0xFE, 0x3F, 0xE1, 0xFF, 0x1F, 0xF0, 0xFF, 0x8F, 0xF0, 0x7F,
  0x80, 0x0F, 0xC1, 0xFE, 0x1F, 0xC1, 0xFF, 0x1F, 0xC3, 0xFE, 0x1F, 0xE1,
  0xFE, 0x07, 0xE0, 0x38, 0x07, 0xF0, 0x78, 0x07, 0xF0, 0x78, 0x0F, 0xF8,
  0x70, 0x0F, 0x78, 0x70, 0x0E, 0x78, 0xF0, 0x0E, 0x7C, 0xF0, 0x1E, 0x3C,
  0xF0, 0x1E, 0x3E, 0xE0, 0x1E, 0x1E, 0xE0, 0x1C, 0x1F, 0xE0, 0x1C, 0x0F,
  0xE0, 0x3C, 0x0F, 0xE0, 0x7F, 0x87, 0xC0, 0xFF, 0x87, 0xC0, 0xFF, 0x87,
  0xC0, 0xFF, 0x03, 0xC0, 0x00, 0x7E, 0x00, 0x1F, 0xF8, 0x07, 0xFF, 0xC0,
  0xFF, 0xFE, 0x1F, 0x87, 0xE3, 0xE0, 0x1F, 0x3C, 0x01, 0xF7, 0xC0, 0x0F,
  0x78, 0x00, 0xFF, 0x00, 0x0F, 0xF0, 0x00, 0xFF, 0x00, 0x0F, 0xF0, 0x01,
  0xEF, 0x00, 0x3E, 0xF8, 0x03, 0xCF, 0x80, 0x7C, 0x7C, 0x1F, 0x87, 0xFF,
  0xF0, 0x3F, 0xFE, 0x01, 0xFF, 0x80, 0x07, 0xE0, 0x00, 0x0F, 0xFF, 0x80,
  0x7F, 0xFF, 0x07, 0xFF, 0xFC, 0x1F, 0xFF, 0xF0, 0x38, 0x0F, 0x81, 0xC0,
  0x3C, 0x1E, 0x01, 0xE0, 0xF0, 0x0F, 0x07, 0x00, 0xF0, 0x38, 0x0F, 0x83,
  0xFF, 0xF8, 0x1F, 0xFF, 0x80, 0xFF, 0xF8, 0x07, 0xFF, 0x00, 0x38, 0x00,
  0x03, 0xC0, 0x00, 0x1E, 0x00, 0x03, 0xFF, 0x80, 0x3F, 0xFC, 0x01, 0xFF,
  0xE0, 0x0F, 0xFE, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x1F, 0xF8, 0x07, 0xFF,
  0xC0, 0xFF, 0xFE, 0x1F, 0x87, 0xE3, 0xE0, 0x1F, 0x3C, 0x01, 0xF7, 0xC0,
  0x0F, 0x78, 0x00, 0xFF, 0x00, 0x0F, 0xF0, 0x00, 0xFF, 0x00, 0x0F, 0xF0,
  0x01, 0xEF, 0x00, 0x3E, 0xF8, 0x03, 0xCF, 0x80, 0x7C, 0x7C, 0x1F, 0x87,
  0xFF, 0xF0, 0x3F, 0xFE, 0x01, 0xFF, 0x80, 0x07, 0xE0, 0x01, 0xFE, 0x30,
  0x3F, 0xFF, 0x87, 0xFF, 0xF0, 0x7F, 0xFF, 0x07, 0x83, 0xC0, 0x07, 0xFF,
  0x80, 0x3F, 0xFF, 0x80, 0xFF, 0xFF, 0x03, 0xFF, 0xFE, 0x03, 0xC0, 0xF8,
  0x0E, 0x01, 0xE0, 0x38, 0x07, 0x81, 0xE0, 0x3E, 0x07, 0x83, 0xF0, 0x1F,
  0xFF, 0x80, 0x7F, 0xFC, 0x01, 0xFF, 0xC0, 0x0F, 0xFF, 0x80, 0x3C, 0x3E,
  0x00, 0xE0, 0x7C, 0x03, 0x80, 0xF0, 0x1E, 0x03, 0xE1, 0xFF, 0x07, 0xFF,
  0xFC, 0x1F, 0xFF, 0xF0, 0x3F, 0xFF, 0x80, 0xF8, 0x00, 0x7C, 0xE0, 0x7F,
  0xFC, 0x1F, 0xFF, 0x87, 0xFF, 0xE0, 0xF8, 0x7C, 0x3C, 0x07, 0x87, 0x80,
  0xE0, 0xF0, 0x00, 0x1F, 0x00, 0x03, 0xFE, 0x00, 0x3F, 0xF8, 0x03, 0xFF,
  0x80, 0x07, 0xF8, 0x40, 0x1F, 0x3C, 0x01, 0xE7, 0x80, 0x3C, 0xFC, 0x1F,
  0x1F, 0xFF, 0xE3, 0xFF, 0xF8, 0x7F, 0xFE, 0x00, 0x7E, 0x00, 0x7F, 0xFF,
  0xEF, 0xFF, 0xFD, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0x0E, 0x1F, 0xE1, 0xC3,
  0xBC, 0x78, 0x77, 0x0F, 0x1E, 0xE1, 0xC1, 0x80, 0x38, 0x00, 0x0F, 0x00,
  0x01, 0xE0, 0x00, 0x3C, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x3C, 0x00,
  0x07, 0x80, 0x0F, 0xFE, 0x03, 0xFF, 0xE0, 0x7F, 0xFC, 0x0F, 0xFF, 0x00,
  0x7F, 0x8F, 0xF3, 0xFE, 0x7F, 0xDF, 0xF7, 0xFC, 0xFF, 0x1F, 0xE3, 0xC0,
  0x3C, 0x1C, 0x01, 0xE0, 0xE0, 0x0F, 0x0F, 0x00, 0x70, 0x78, 0x03, 0x83,
  0xC0, 0x3C, 0x1C, 0x01, 0xE0, 0xE0, 0x0E, 0x0F, 0x00, 0x70, 0x78, 0x03,
  0x83, 0xC0, 0x3C, 0x1F, 0x01, 0xC0, 0xFC, 0x3E, 0x03, 0xFF, 0xE0, 0x1F,
  0xFE, 0x00, 0x7F, 0xE0, 0x00, 0xFC, 0x00, 0x00, 0x7F, 0x81, 0xFE, 0xFF,
  0x87, 0xFF, 0xFF, 0x0F, 0xFB, 0xFC, 0x1F, 0xE1, 0xC0, 0x0F, 0x03, 0xC0,
  0x1C, 0x07, 0x80, 0x78, 0x0F, 0x01, 0xE0, 0x1E, 0x03, 0x80, 0x1E, 0x0F,
  0x00, 0x3C, 0x3C, 0x00, 0x78, 0x70, 0x00, 0xF1, 0xE0, 0x01, 0xE7, 0x80,
  0x01, 0xEF, 0x00, 0x03, 0xFC, 0x00, 0x07, 0xF0, 0x00, 0x0F, 0xE0, 0x00,
  0x0F, 0x80, 0x00, 0x1E, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x7F, 0x87, 0xFF,
  0xFF, 0x1F, 0xFF, 0xF8, 0x7F, 0xFF, 0xE1, 0xFE, 0x78, 0x00, 0xF1, 0xE3,
  0xC3, 0x87, 0x8F, 0x0E, 0x1E, 0x7C, 0x78, 0x79, 0xF9, 0xC1, 0xEF, 0xEF,
  0x07, 0xBF, 0xBC, 0x1D, 0xFE, 0xE0, 0x77, 0x7F, 0x81, 0xFD, 0xFE, 0x07,
  0xE3, 0xF0, 0x3F, 0x8F, 0xC0, 0xFC, 0x3F, 0x03, 0xF0, 0xF8, 0x0F, 0x83,
  0xE0, 0x3E, 0x0F, 0x80, 0xF0, 0x3C, 0x00, 0x07, 0xE0, 0x7E, 0x0F, 0xF0,
  0xFF, 0x0F, 0xF0, 0xFE, 0x0F, 0xE0, 0xFE, 0x03, 0xC0, 0xF8, 0x01, 0xE1,
  0xE0, 0x01, 0xF3, 0xC0, 0x00, 0xF7, 0x80, 0x00, 0x7F, 0x00, 0x00, 0x7E,
  0x00, 0x00, 0x7C, 0x00, 0x00, 0xFE, 0x00, 0x01, 0xFF, 0x00, 0x03, 0xEF,
  0x00, 0x07, 0xCF, 0x80, 0x0F, 0x87, 0xC0, 0x1F, 0x03, 0xC0, 0x7F, 0x07,
  0xF0, 0xFF, 0x8F, 0xF0, 0xFF, 0x0F, 0xF0, 0xFF, 0x0F, 0xE0, 0x7E, 0x0F,
  0xEF, 0xF0, 0xFF, 0xFF, 0x0F, 0xEF, 0xE0, 0xFE, 0x3C, 0x0F, 0x01, 0xE1,
  0xE0, 0x1E, 0x3E, 0x00, 0xF7, 0xC0, 0x0F, 0xF8, 0x00, 0x7F, 0x00, 0x07,
  0xE0, 0x00, 0x3C, 0x00, 0x03, 0x80, 0x00, 0x78, 0x00, 0x07, 0x80, 0x00,
  0x78, 0x00, 0x07, 0x00, 0x07, 0xFF, 0x00, 0xFF, 0xF8, 0x0F, 0xFF, 0x00,
  0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xE0, 0xFF, 0xFC, 0x3F, 0xFF, 0x87, 0xFF,
  0xF0, 0xF0, 0x7C, 0x1C, 0x1F, 0x03, 0x87, 0xC0, 0x61, 0xF0, 0x00, 0x7C,
  0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00, 0x1F, 0x07,
  0x07, 0xC0, 0xE1, 0xF0, 0x3C, 0x7C, 0x07, 0x9F, 0xFF, 0xF3, 0xFF, 0xFC,
  0x7F, 0xFF, 0x8F, 0xFF, 0xF0, 0x07, 0xF8, 0x3F, 0xC1, 0xFE, 0x0F, 0xE0,
  0x70, 0x07, 0x80, 0x3C, 0x01, 0xC0, 0x0E, 0x00, 0xF0, 0x07, 0x80, 0x3C,
  0x01, 0xC0, 0x0E, 0x00, 0xF0, 0x07, 0x80, 0x38, 0x01, 0xC0, 0x0E, 0x00,
  0xF0, 0x07, 0x80, 0x38, 0x01, 0xC0, 0x1F, 0xE0, 0xFF, 0x07, 0xF8, 0x3F,
  0x80, 0xE0, 0x38, 0x0F, 0x03, 0xC0, 0xF0, 0x1C, 0x07, 0x81, 0xE0, 0x78,
  0x0E, 0x03, 0xC0, 0xF0, 0x3C, 0x07, 0x01, 0xE0, 0x78, 0x1E, 0x03, 0x80,
  0xF0, 0x3C, 0x0F, 0x01, 0xE0, 0x78, 0x1E, 0x03, 0x80, 0xF0, 0x3C, 0x06,
  0x07, 0xF8, 0x3F, 0xC1, 0xFC, 0x0F, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0,
  0x1C, 0x00, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0x80, 0x1C, 0x01, 0xE0, 0x0F,
  0x00, 0x78, 0x03, 0x80, 0x1C, 0x01, 0xE0, 0x0F, 0x00, 0x70, 0x03, 0x80,
  0x1C, 0x0F, 0xE0, 0xFF, 0x07, 0xF0, 0x3F, 0x80, 0x00, 0x40, 0x01, 0x80,
  0x07, 0x80, 0x3F, 0x80, 0xFF, 0x03, 0xFF, 0x0F, 0x9F, 0x3E, 0x1E, 0xF8,
  0x3F, 0xE0, 0x3F, 0x00, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xF0, 0xC3, 0xC7, 0x0E, 0x3C, 0x30, 0x00, 0xFE, 0x00,
  0x7F, 0xF0, 0x1F, 0xFF, 0x03, 0xFF, 0xE0, 0x00, 0x3C, 0x07, 0xFF, 0x83,
  0xFF, 0xF0, 0xFF, 0xFC, 0x3F, 0xFF, 0x8F, 0x80, 0xF3, 0xE0, 0x1E, 0x78,
  0x1F, 0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0xFF, 0xF8, 0xFE, 0x7E, 0x07,
  0xE0, 0x00, 0x3F, 0x80, 0x00, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x01, 0xC0,
  0x00, 0x0F, 0x00, 0x00, 0x3C, 0xFC, 0x00, 0xEF, 0xFC, 0x03, 0xFF, 0xF8,
  0x1F, 0xFF, 0xE0, 0x7E, 0x0F, 0xC1, 0xE0, 0x1F, 0x07, 0x00, 0x3C, 0x1C,
  0x00, 0xF0, 0xE0, 0x03, 0xC3, 0x80, 0x1E, 0x0F, 0x00, 0xF8, 0x3E, 0x07,
  0xC7, 0xFF, 0xFF, 0x3F, 0xFF, 0xF8, 0xFF, 0xFF, 0x81, 0xF1, 0xF8, 0x00,
  0x00, 0xFE, 0x60, 0xFF, 0xFC, 0x3F, 0xFF, 0x8F, 0xFF, 0xF3, 0xF0, 0x3C,
  0xF8, 0x03, 0x9E, 0x00, 0x67, 0x80, 0x00, 0xF0, 0x00, 0x1E, 0x00, 0x03,
  0xC0, 0x00, 0x7E, 0x01, 0xC7, 0xFF, 0xF8, 0xFF, 0xFE, 0x0F, 0xFF, 0x80,
  0x7F, 0x80, 0x00, 0x01, 0xF8, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07,
  0xE0, 0x00, 0x0F, 0x00, 0x00, 0x70, 0x07, 0xE3, 0x80, 0xFF, 0xDC, 0x0F,
  0xFF, 0xE0, 0xFF, 0xFF, 0x0F, 0xC1, 0xF0, 0xF8, 0x07, 0x87, 0x80, 0x1C,
  0x78, 0x00, 0xE3, 0xC0, 0x0F, 0x1E, 0x00, 0x70, 0xF0, 0x07, 0x87, 0xE0,
  0xFC, 0x1F, 0xFF, 0xF8, 0xFF, 0xFF, 0xC3, 0xFF, 0xFE, 0x07, 0xE3, 0xE0,
  0x00, 0xFC, 0x01, 0xFF, 0xC0, 0xFF, 0xF8, 0x7F, 0xFE, 0x3E, 0x0F, 0xCE,
  0x00, 0xF7, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xE0, 0x1E, 0xFF, 0xFF, 0x9F, 0xFF, 0xE3, 0xFF, 0xF0, 0x3F, 0xF0,
  0x00, 0x0F, 0xF0, 0x01, 0xFF, 0xC0, 0x1F, 0xFE, 0x01, 0xFF, 0xE0, 0x0F,
  0x00, 0x00, 0xF0, 0x00, 0x3F, 0xFF, 0x03, 0xFF, 0xF8, 0x1F, 0xFF, 0xC0,
  0xFF, 0xFC, 0x00, 0xF0, 0x00, 0x07, 0x80, 0x00, 0x38, 0x00, 0x01, 0xC0,
  0x00, 0x1E, 0x00, 0x00, 0xF0, 0x00, 0x07, 0x00, 0x00, 0x38, 0x00, 0x1F,
  0xFF, 0x81, 0xFF, 0xFC, 0x0F, 0xFF, 0xE0, 0x7F, 0xFE, 0x00, 0x01, 0xF9,
  0xF8, 0x3F, 0xFF, 0xC3, 0xFF, 0xFE, 0x7F, 0xFF, 0xE3, 0xE0, 0xFC, 0x3E,
  0x03, 0xE1, 0xE0, 0x0E, 0x1E, 0x00, 0x70, 0xF0, 0x03, 0x87, 0x80, 0x3C,
  0x3E, 0x03, 0xE1, 0xF8, 0x7E, 0x07, 0xFF, 0xF0, 0x3F, 0xFF, 0x80, 0xFF,
  0xFC, 0x01, 0xF9, 0xE0, 0x00, 0x0E, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x80,
  0x7F, 0xF8, 0x07, 0xFF, 0x80, 0x3F, 0xF8, 0x00, 0xFF, 0x00, 0x00, 0x0F,
  0xC0, 0x00, 0xFC, 0x00, 0x0F, 0xC0, 0x00, 0xFC, 0x00, 0x03, 0xC0, 0x00,
  0x38, 0x00, 0x03, 0x9F, 0x00, 0x7F, 0xFC, 0x07, 0xFF, 0xC0, 0x7F, 0xFE,
  0x07, 0xC3, 0xE0, 0x70, 0x1E, 0x0F, 0x01, 0xC0, 0xF0, 0x1C, 0x0E, 0x03,
  0xC0, 0xE0, 0x3C, 0x1E, 0x03, 0x81, 0xE0, 0x38, 0x7F, 0x0F, 0xFF, 0xF8,
  0xFF, 0xFF, 0x8F, 0xF7, 0xF0, 0xFE, 0x00, 0x78, 0x00, 0x78, 0x00, 0x78,
  0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xF0, 0x0F, 0xF0, 0x1F, 0xF0,
  0x0F, 0xF0, 0x00, 0xF0, 0x00, 0xE0, 0x00, 0xE0, 0x01, 0xE0, 0x01, 0xE0,
  0x01, 0xE0, 0x01, 0xC0, 0x01, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFE, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0xF8, 0x00, 0x3C, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x7F, 0xFC, 0x3F, 0xFE, 0x0F, 0xFF, 0x81, 0xFF,
  0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x07, 0x00, 0x01, 0xC0, 0x00, 0xF0,
  0x00, 0x3C, 0x00, 0x0E, 0x00, 0x03, 0x80, 0x00, 0xE0, 0x00, 0x78, 0x00,
  0x1E, 0x00, 0x07, 0x00, 0x01, 0xC0, 0x00, 0xF0, 0x00, 0x7C, 0x1F, 0xFE,
  0x0F, 0xFF, 0x03, 0xFF, 0x80, 0x7F, 0x80, 0x00, 0x07, 0xE0, 0x00, 0xFE,
  0x00, 0x0F, 0xE0, 0x00, 0x7C, 0x00, 0x01, 0xC0, 0x00, 0x3C, 0x00, 0x03,
  0xCF, 0xF0, 0x3C, 0xFF, 0x03, 0x9F, 0xF0, 0x38, 0xFE, 0x07, 0xBF, 0x00,
  0x7F, 0xC0, 0x07, 0xF8, 0x00, 0x7F, 0x00, 0x07, 0xF8, 0x00, 0xFF, 0xC0,
  0x0F, 0x7E, 0x00, 0xE3, 0xF0, 0x7E, 0x1F, 0xE7, 0xE1, 0xFE, 0xFE, 0x3F,
  0xE7, 0xE1, 0xFC, 0x03, 0xFC, 0x07, 0xFC, 0x07, 0xF8, 0x07, 0xF8, 0x00,
  0x78, 0x00, 0x78, 0x00, 0x78, 0x00, 0x70, 0x00, 0x70, 0x00, 0xF0, 0x00,
  0xF0, 0x00, 0xE0, 0x00, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01,
  0xC0, 0x01, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x1F,
  0x7C, 0x78, 0x7F, 0xFF, 0xF8, 0xFF, 0xFF, 0xF9, 0xFF, 0xFF, 0xF0, 0xF1,
  0xF1, 0xE1, 0xC3, 0x83, 0xC7, 0x87, 0x07, 0x8F, 0x0E, 0x0E, 0x1C, 0x3C,
  0x1C, 0x38, 0x78, 0x78, 0x70, 0xE0, 0xF1, 0xE1, 0xC1, 0xC7, 0xE3, 0xC3,
  0xFF, 0xCF, 0xC7, 0xFF, 0x9F, 0x9F, 0xFF, 0x3E, 0x3E, 0x0F, 0x8F, 0x80,
  0xFD, 0xFF, 0x07, 0xFF, 0xF8, 0x3F, 0xFF, 0xE0, 0x7E, 0x1F, 0x07, 0xC0,
  0x78, 0x3C, 0x03, 0x81, 0xE0, 0x1C, 0x0E, 0x01, 0xE0, 0x70, 0x0F, 0x07,
  0x80, 0x70, 0x3C, 0x03, 0x87, 0xF0, 0x3F, 0x7F, 0xC3, 0xFF, 0xFE, 0x1F,
  0xEF, 0xE0, 0xFE, 0x01, 0xFC, 0x01, 0xFF, 0x80, 0xFF, 0xF8, 0x7F, 0xFE,
  0x3E, 0x0F, 0xDF, 0x01, 0xF7, 0x80, 0x3F, 0xC0, 0x0F, 0xF0, 0x03, 0xFC,
  0x01, 0xEF, 0x80, 0xFB, 0xF0, 0x7C, 0x7F, 0xFF, 0x1F, 0xFF, 0x03, 0xFF,
  0x80, 0x3F, 0x80, 0x07, 0xC7, 0xE0, 0x1F, 0xBF, 0xF0, 0x3F, 0xFF, 0xF0,
  0x7F, 0xFF, 0xE0, 0x3F, 0x07, 0xE0, 0x78, 0x03, 0xC0, 0xE0, 0x07, 0x81,
  0xC0, 0x0F, 0x07, 0x00, 0x1E, 0x0F, 0x00, 0x78, 0x1E, 0x01, 0xF0, 0x3E,
  0x07, 0xC0, 0xFF, 0xFF, 0x81, 0xFF, 0xFE, 0x03, 0xDF, 0xF0, 0x07, 0x1F,
  0x80, 0x0E, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x78, 0x00, 0x03, 0xFE, 0x00,
  0x0F, 0xFE, 0x00, 0x1F, 0xF8, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x01, 0xF8,
  0xF8, 0x1F, 0xFF, 0xF1, 0xFF, 0xFF, 0xCF, 0xFF, 0xFE, 0x3E, 0x07, 0xC1,
  0xF0, 0x0F, 0x07, 0x80, 0x1C, 0x3C, 0x00, 0x70, 0xF0, 0x03, 0x83, 0xC0,
  0x0E, 0x0F, 0x80, 0x78, 0x3F, 0x07, 0xE0, 0x7F, 0xFF, 0x81, 0xFF, 0xFC,
  0x03, 0xFF, 0x70, 0x03, 0xF3, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x3C, 0x00,
  0x00, 0xE0, 0x00, 0x3F, 0xE0, 0x01, 0xFF, 0xC0, 0x07, 0xFF, 0x00, 0x1F,
  0xF8, 0x00, 0x0F, 0xC3, 0xC1, 0xFC, 0xFF, 0x1F, 0xFF, 0xF1, 0xFF, 0xFE,
  0x03, 0xFC, 0x00, 0x3F, 0x00, 0x03, 0xC0, 0x00, 0x78, 0x00, 0x07, 0x80,
  0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0xF0, 0x00, 0xFF, 0xFC, 0x0F, 0xFF,
  0xE0, 0xFF, 0xFC, 0x0F, 0xFF, 0xC0, 0x03, 0xF3, 0x0F, 0xFF, 0x3F, 0xFF,
  0x3F, 0xFF, 0x7C, 0x0E, 0x78, 0x00, 0x7F, 0xE0, 0x3F, 0xFC, 0x1F, 0xFF,
  0x00, 0x3F, 0x70, 0x0F, 0xF8, 0x1F, 0xFF, 0xFE, 0xFF, 0xFC, 0xFF, 0xF8,
  0x0F, 0xE0, 0x06, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x0E, 0x00, 0x0E, 0x00,
  0x7F, 0xFE, 0xFF, 0xFE, 0xFF, 0xFE, 0xFF, 0xFC, 0x1C, 0x00, 0x3C, 0x00,
  0x3C, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0x78, 0x00, 0x7C, 0x0E,
  0x7F, 0xFF, 0x7F, 0xFE, 0x3F, 0xFC, 0x0F, 0xE0, 0x7C, 0x0F, 0xFF, 0x07,
  0xFF, 0x81, 0xFF, 0xE0, 0x7E, 0x78, 0x03, 0x9E, 0x00, 0xE7, 0x80, 0x79,
  0xE0, 0x1E, 0x78, 0x07, 0x1E, 0x01, 0xC7, 0x80, 0xF1, 0xE0, 0xFC, 0x7F,
  0xFF, 0x9F, 0xFF, 0xE3, 0xFF, 0xF8, 0x3E, 0x7C, 0x7F, 0x87, 0xFF, 0xFC,
  0x7F, 0xFF, 0xE3, 0xFF, 0xFF, 0x1F, 0xE1, 0xE0, 0x3C, 0x0F, 0x03, 0xC0,
  0x78, 0x3C, 0x01, 0xE1, 0xC0, 0x0F, 0x1E, 0x00, 0x79, 0xE0, 0x03, 0xCE,
  0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x00, 0x03, 0xF0, 0x00, 0x0F, 0x80, 0x00,
  0x78, 0x00, 0x7E, 0x03, 0xF7, 0xF0, 0x3F, 0xFF, 0x81, 0xFD, 0xF8, 0x0F,
  0xE7, 0x8E, 0x1C, 0x3C, 0xF9, 0xE1, 0xE7, 0xCE, 0x0F, 0x7E, 0xF0, 0x7B,
  0xF7, 0x03, 0xFF, 0xF8, 0x1F, 0xDF, 0x80, 0xFC, 0xFC, 0x07, 0xE7, 0xE0,
  0x3E, 0x3E, 0x01, 0xF1, 0xF0, 0x0F, 0x07, 0x00, 0x0F, 0xE3, 0xF8, 0xFF,
  0x1F, 0xC7, 0xF9, 0xFE, 0x1F, 0x87, 0xF0, 0x7E, 0x7C, 0x01, 0xFF, 0xC0,
  0x07, 0xFC, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x1F, 0xF0, 0x01, 0xF7,
  0xC0, 0x1F, 0x1F, 0x03, 0xF0, 0x7C, 0x7F, 0xCF, 0xFB, 0xFE, 0x7F, 0xDF,
  0xE3, 0xFC, 0x07, 0xF0, 0x7F, 0x0F, 0xF0, 0xFF, 0x0F, 0xF0, 0xFF, 0x07,
  0xE0, 0xFE, 0x03, 0xC0, 0x78, 0x03, 0xC0, 0x78, 0x03, 0xC0, 0xF0, 0x01,
  0xE1, 0xE0, 0x01, 0xE1, 0xC0, 0x01, 0xE3, 0xC0, 0x00, 0xF7, 0x80, 0x00,
  0xFF, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7C, 0x00, 0x00,
  0x78, 0x00, 0x00, 0x70, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x7F,
  0xF0, 0x00, 0xFF, 0xF8, 0x00, 0xFF, 0xF0, 0x00, 0x7F, 0xF0, 0x00, 0x1F,
  0xFF, 0xC7, 0xFF, 0xF1, 0xFF, 0xF8, 0xFF, 0xFE, 0x3C, 0x1F, 0x0E, 0x1F,
  0x00, 0x0F, 0x80, 0x07, 0xC0, 0x07, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x00,
  0xF8, 0x3C, 0xFF, 0xFF, 0x3F, 0xFF, 0xCF, 0xFF, 0xE3, 0xFF, 0xF8, 0x00,
  0xF0, 0x1F, 0x03, 0xF0, 0x7E, 0x07, 0x80, 0x70, 0x0F, 0x00, 0xF0, 0x0E,
  0x00, 0xE0, 0x1E, 0x01, 0xC0, 0xFC, 0x0F, 0x80, 0xF8, 0x0F, 0xC0, 0x3C,
  0x03, 0xC0, 0x38, 0x03, 0x80, 0x78, 0x07, 0x80, 0x78, 0x07, 0xE0, 0x7E,
  0x03, 0xE0, 0x1C, 0x00, 0x02, 0x07, 0x07, 0x0F, 0x0F, 0x0E, 0x0E, 0x0E,
  0x1E, 0x1E, 0x1C, 0x1C, 0x1C, 0x3C, 0x3C, 0x38, 0x38, 0x38, 0x78, 0x78,
  0x70, 0x70, 0x70, 0xF0, 0xF0, 0xE0, 0xE0, 0x01, 0xC0, 0x1F, 0x00, 0xFC,
  0x07, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0, 0x1C, 0x00, 0xE0, 0x0F, 0x00,
  0x78, 0x03, 0xC0, 0x1F, 0x80, 0x7C, 0x03, 0xE0, 0x3F, 0x03, 0xC0, 0x1C,
  0x00, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0x80, 0x3C, 0x0F, 0xE0, 0x7E, 0x07,
  0xE0, 0x1E, 0x00, 0x0F, 0x00, 0x1F, 0xC0, 0x1F, 0xF0, 0xFF, 0xFC, 0xFF,
  0x3F, 0xFF, 0x0F, 0xF8, 0x03, 0xF8, 0x00, 0xF0 };

const GFXglyph FreeMonoBoldOblique18pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,  21,    0,    1 },   // 0x20 ' '
  {     0,   9,  22,  21,    9,  -21 },   // 0x21 '!'
  {    25,  12,  10,  21,    9,  -20 },   // 0x22 '"'
  {    40,  18,  25,  21,    4,  -22 },   // 0x23 '#'
  {    97,  18,  28,  21,    4,  -23 },   // 0x24 '$'
  {   160,  16,  21,  21,    5,  -20 },   // 0x25 '%'
  {   202,  16,  20,  21,    4,  -19 },   // 0x26 '&'
  {   242,   5,  10,  21,   12,  -20 },   // 0x27 '''
  {   249,  10,  27,  21,   11,  -21 },   // 0x28 '('
  {   283,  10,  27,  21,    4,  -21 },   // 0x29 ')'
  {   317,  15,  15,  21,    6,  -21 },   // 0x2A '*'
  {   346,  18,  19,  21,    4,  -18 },   // 0x2B '+'
  {   389,   9,  10,  21,    4,   -3 },   // 0x2C ','
  {   401,  18,   4,  21,    4,  -11 },   // 0x2D '-'
  {   410,   5,   5,  21,    8,   -4 },   // 0x2E '.'
  {   414,  21,  28,  21,    2,  -23 },   // 0x2F '/'
  {   488,  17,  23,  21,    5,  -22 },   // 0x30 '0'
  {   537,  15,  22,  21,    3,  -21 },   // 0x31 '1'
  {   579,  20,  23,  21,    2,  -22 },   // 0x32 '2'
  {   637,  18,  23,  21,    3,  -22 },   // 0x33 '3'
  {   689,  16,  21,  21,    4,  -20 },   // 0x34 '4'
  {   731,  18,  22,  21,    4,  -21 },   // 0x35 '5'
  {   781,  19,  23,  21,    5,  -22 },   // 0x36 '6'
  {   836,  16,  22,  21,    6,  -21 },   // 0x37 '7'
  {   880,  19,  23,  21,    3,  -22 },   // 0x38 '8'
  {   935,  18,  23,  21,    4,  -22 },   // 0x39 '9'
  {   987,   7,  16,  21,    9,  -15 },   // 0x3A ':'
  {  1001,  11,  22,  21,    4,  -15 },   // 0x3B ';'
  {  1032,  18,  16,  21,    4,  -17 },   // 0x3C '<'
  {  1068,  19,  10,  21,    3,  -14 },   // 0x3D '='
  {  1092,  19,  16,  21,    3,  -17 },   // 0x3E '>'
  {  1130,  14,  21,  21,    8,  -20 },   // 0x3F '?'
  {  1167,  18,  27,  21,    3,  -21 },   // 0x40 '@'
  {  1228,  22,  21,  21,    0,  -20 },   // 0x41 'A'
  {  1286,  21,  21,  21,    1,  -20 },   // 0x42 'B'
  {  1342,  21,  21,  21,    2,  -20 },   // 0x43 'C'
  {  1398,  21,  21,  21,    1,  -20 },   // 0x44 'D'
  {  1454,  22,  21,  21,    0,  -20 },   // 0x45 'E'
  {  1512,  23,  21,  21,    0,  -20 },   // 0x46 'F'
  {  1573,  21,  21,  21,    2,  -20 },   // 0x47 'G'
  {  1629,  23,  21,  21,    0,  -20 },   // 0x48 'H'
  {  1690,  19,  21,  21,    2,  -20 },   // 0x49 'I'
  {  1740,  23,  21,  21,    0,  -20 },   // 0x4A 'J'
  {  1801,  23,  21,  21,    0,  -20 },   // 0x4B 'K'
  {  1862,  20,  21,  21,    1,  -20 },   // 0x4C 'L'
  {  1915,  25,  21,  21,    0,  -20 },   // 0x4D 'M'
  {  1981,  24,  21,  21,    1,  -20 },   // 0x4E 'N'
  {  2044,  20,  21,  21,    2,  -20 },   // 0x4F 'O'
  {  2097,  21,  21,  21,    1,  -20 },   // 0x50 'P'
  {  2153,  20,  26,  21,    2,  -20 },   // 0x51 'Q'
  {  2218,  22,  21,  21,    0,  -20 },   // 0x52 'R'
  {  2276,  19,  21,  21,    3,  -20 },   // 0x53 'S'
  {  2326,  19,  21,  21,    3,  -20 },   // 0x54 'T'
  {  2376,  21,  21,  21,    3,  -20 },   // 0x55 'U'
  {  2432,  23,  21,  21,    1,  -20 },   // 0x56 'V'
  {  2493,  22,  21,  21,    2,  -20 },   // 0x57 'W'
  {  2551,  24,  21,  21,    0,  -20 },   // 0x58 'X'
  {  2614,  20,  21,  21,    3,  -20 },   // 0x59 'Y'
  {  2667,  19,  21,  21,    2,  -20 },   // 0x5A 'Z'
  {  2717,  13,  27,  21,    8,  -21 },   // 0x5B '['
  {  2761,  10,  28,  21,    8,  -23 },   // 0x5C '\'
  {  2796,  13,  27,  21,    4,  -21 },   // 0x5D ']'
  {  2840,  15,  11,  21,    6,  -21 },   // 0x5E '^'
  {  2861,  21,   4,  21,   -1,    4 },   // 0x5F '_'
  {  2872,   6,   6,  21,   10,  -22 },   // 0x60 '`'
  {  2877,  19,  16,  21,    2,  -15 },   // 0x61 'a'
  {  2915,  22,  22,  21,    0,  -21 },   // 0x62 'b'
  {  2976,  19,  16,  21,    3,  -15 },   // 0x63 'c'
  {  3014,  21,  22,  21,    3,  -21 },   // 0x64 'd'
  {  3072,  18,  16,  21,    3,  -15 },   // 0x65 'e'
  {  3108,  21,  22,  21,    3,  -21 },   // 0x66 'f'
  {  3166,  21,  23,  21,    2,  -15 },   // 0x67 'g'
  {  3227,  20,  22,  21,    1,  -21 },   // 0x68 'h'
  {  3282,  16,  22,  21,    3,  -21 },   // 0x69 'i'
  {  3326,  18,  29,  21,    2,  -21 },   // 0x6A 'j'
  {  3392,  20,  22,  21,    1,  -21 },   // 0x6B 'k'
  {  3447,  16,  22,  21,    3,  -21 },   // 0x6C 'l'
  {  3491,  23,  16,  21,    0,  -15 },   // 0x6D 'm'
  {  3537,  21,  16,  21,    1,  -15 },   // 0x6E 'n'
  {  3579,  18,  16,  21,    3,  -15 },   // 0x6F 'o'
  {  3615,  23,  23,  21,   -1,  -15 },   // 0x70 'p'
  {  3682,  22,  23,  21,    2,  -15 },   // 0x71 'q'
  {  3746,  20,  16,  21,    2,  -15 },   // 0x72 'r'
  {  3786,  16,  16,  21,    4,  -15 },   // 0x73 's'
  {  3818,  16,  21,  21,    4,  -20 },   // 0x74 't'
  {  3860,  18,  16,  21,    3,  -15 },   // 0x75 'u'
  {  3896,  21,  16,  21,    2,  -15 },   // 0x76 'v'
  {  3938,  21,  16,  21,    3,  -15 },   // 0x77 'w'
  {  3980,  21,  16,  21,    1,  -15 },   // 0x78 'x'
  {  4022,  24,  23,  21,   -1,  -15 },   // 0x79 'y'
  {  4091,  18,  16,  21,    3,  -15 },   // 0x7A 'z'
  {  4127,  12,  27,  21,    8,  -21 },   // 0x7B '{'
  {  4168,   8,  27,  21,    8,  -21 },   // 0x7C '|'
  {  4195,  13,  27,  21,    4,  -21 },   // 0x7D '}'
  {  4239,  17,   8,  21,    4,  -13 } }; // 0x7E '~'

const GFXfont FreeMonoBoldOblique18pt7b PROGMEM = {
  (uint8_t  *)FreeMonoBoldOblique18pt7bBitmaps,
  (GFXglyph *)FreeMonoBoldOblique18pt7bGlyphs,
  0x20, 0x7E, 35 };

// Approx. 4928 bytes
