/*
 * RFID Employee Punch Clock System - WITH WEB SERVER
 * ESP32-2432S028R (CYD) + MFRC522
 * 
 * This version includes a simple web server that's compatible with all ESP32 versions
 * Uses built-in WebServer instead of ESPAsyncWebServer
 * 
 * Author: AI Assistant
 * Date: 2025-01-17
 * Version: 1.0.1
 */

#include "config.h"
#include "display_manager.h"
#include "rfid_handler.h"
#include "time_manager.h"
#include "data_manager.h"
#include "audio_manager.h"
#include "led_manager.h"
#include "employee_db.h"
#include "simple_web_server.h"  // Using simple web server instead

// Global objects
DisplayManager display;
RFIDHandler rfid;
TimeManager timeManager;
DataManager dataManager;
AudioManager audio;
LEDManager ledManager;
EmployeeDB employeeDB;
SimpleWebServer webServer;  // Simple web server

// System state variables
bool systemInitialized = false;
unsigned long lastActivity = 0;
unsigned long lastRFIDCheck = 0;
unsigned long lastTimeUpdate = 0;
unsigned long lastBrightnessUpdate = 0;

// Current system state
enum SystemState {
  STATE_INITIALIZING,
  STATE_READY,
  STATE_SCANNING,
  STATE_PROCESSING,
  STATE_ADMIN,
  STATE_SETTINGS,
  STATE_ERROR
};

SystemState currentState = STATE_INITIALIZING;
String lastEmployee = "";
String lastAction = "";

void setup() {
  Serial.begin(115200);
  Serial.println("\n=== RFID Punch Clock System Starting ===");
  Serial.println("Version with Simple Web Server");
  
  // Initialize system components
  if (!initializeSystem()) {
    Serial.println("System initialization failed!");
    currentState = STATE_ERROR;
    return;
  }
  
  Serial.println("System initialized successfully!");
  currentState = STATE_READY;
  systemInitialized = true;
  
  // Show welcome screen
  display.showWelcomeScreen();
  audio.playStartupSound();
  ledManager.setStatus(LED_READY);
  
  delay(2000);
  
  // Switch to main screen
  display.showMainScreen();
  lastActivity = millis();
}

void loop() {
  if (!systemInitialized) {
    handleErrorState();
    return;
  }
  
  // Update system components
  updateSystem();
  
  // Handle RFID scanning
  handleRFIDScanning();
  
  // Handle touch input
  handleTouchInput();
  
  // Update display
  updateDisplay();
  
  // Handle timeouts and power management
  handleTimeouts();
  
  // Small delay to prevent overwhelming the system
  delay(50);
}

bool initializeSystem() {
  Serial.println("Initializing system components...");
  
  // Initialize display first (for error messages)
  if (!display.begin()) {
    Serial.println("Display initialization failed!");
    return false;
  }
  
  // Initialize data manager (SD card, SPIFFS)
  if (!dataManager.begin()) {
    Serial.println("Data manager initialization failed!");
    display.showError("SD Card Error");
    return false;
  }
  
  // Initialize RFID handler
  if (!rfid.begin()) {
    Serial.println("RFID initialization failed!");
    display.showError("RFID Error");
    return false;
  }
  
  // Initialize audio manager
  if (!audio.begin()) {
    Serial.println("Audio initialization failed!");
    // Non-critical, continue
  }
  
  // Initialize LED manager
  if (!ledManager.begin()) {
    Serial.println("LED initialization failed!");
    // Non-critical, continue
  }
  
  // Initialize employee database
  if (!employeeDB.begin(&dataManager)) {
    Serial.println("Employee DB initialization failed!");
    display.showError("Database Error");
    return false;
  }
  
  // Initialize time manager
  if (!timeManager.begin()) {
    Serial.println("Time manager initialization failed!");
    // Non-critical for offline operation
  }
  
  // Initialize simple web server (if WiFi available)
  if (WiFi.status() == WL_CONNECTED) {
    if (!webServer.begin(&employeeDB, &dataManager)) {
      Serial.println("Web server initialization failed!");
      // Non-critical, continue without web interface
    } else {
      Serial.println("Web server started successfully!");
    }
  } else {
    Serial.println("WiFi not connected - web server disabled");
  }
  
  return true;
}

void updateSystem() {
  unsigned long currentTime = millis();
  
  // Update time display every second
  if (currentTime - lastTimeUpdate >= 1000) {
    timeManager.update();
    lastTimeUpdate = currentTime;
  }
  
  // Update brightness every 5 seconds
  if (currentTime - lastBrightnessUpdate >= 5000) {
    display.updateBrightness();
    lastBrightnessUpdate = currentTime;
  }
  
  // Update web server
  webServer.handleClient();
  
  // Update LED animations
  ledManager.update();
}

void handleRFIDScanning() {
  unsigned long currentTime = millis();
  
  // Check RFID every 100ms when in ready state
  if (currentState == STATE_READY && currentTime - lastRFIDCheck >= 100) {
    if (rfid.isCardPresent()) {
      String cardUID = rfid.readCardUID();
      if (cardUID.length() > 0) {
        processRFIDCard(cardUID);
      }
    }
    lastRFIDCheck = currentTime;
  }
}

void processRFIDCard(String cardUID) {
  Serial.println("RFID Card detected: " + cardUID);
  
  currentState = STATE_PROCESSING;
  ledManager.setStatus(LED_SCANNING);
  display.showProcessing();
  
  // Look up employee
  Employee* employee = employeeDB.findByRFID(cardUID);
  
  if (employee == nullptr) {
    // Unknown card
    Serial.println("Unknown RFID card");
    audio.playErrorSound();
    ledManager.setStatus(LED_ERROR);
    display.showError("Unknown Card");
    delay(2000);
    currentState = STATE_READY;
    display.showMainScreen();
    return;
  }
  
  if (!employee->active) {
    // Inactive employee
    Serial.println("Inactive employee: " + employee->name);
    audio.playErrorSound();
    ledManager.setStatus(LED_ERROR);
    display.showError("Inactive Employee");
    delay(2000);
    currentState = STATE_READY;
    display.showMainScreen();
    return;
  }
  
  // Process punch in/out
  String action = (employee->lastAction == "punch_out") ? "punch_in" : "punch_out";
  String timestamp = timeManager.getCurrentTimestamp();
  
  // Log attendance
  AttendanceRecord record;
  record.employeeId = employee->id;
  record.timestamp = timestamp;
  record.action = action;
  record.location = "Main Terminal";
  record.synced = false;
  
  if (dataManager.logAttendance(record)) {
    // Update employee record
    employee->lastAction = action;
    employee->lastTimestamp = timestamp;
    employeeDB.updateEmployee(*employee);
    
    // Show success
    audio.playPunchSound(action == "punch_in");
    ledManager.setStatus(LED_SUCCESS);
    display.showPunchSuccess(employee->name, action, timestamp);
    
    // Update last activity
    lastEmployee = employee->name;
    lastAction = action;
    lastActivity = millis();
    
    Serial.println("Punch recorded: " + employee->name + " - " + action);
    
    delay(3000);
  } else {
    // Database error
    audio.playErrorSound();
    ledManager.setStatus(LED_ERROR);
    display.showError("Database Error");
    delay(2000);
  }
  
  currentState = STATE_READY;
  display.showMainScreen();
  ledManager.setStatus(LED_READY);
}

void handleTouchInput() {
  if (display.isTouched()) {
    TouchPoint touch = display.getTouchPoint();
    lastActivity = millis();
    
    switch (currentState) {
      case STATE_READY:
        handleMainScreenTouch(touch);
        break;
      case STATE_ADMIN:
        handleAdminScreenTouch(touch);
        break;
      case STATE_SETTINGS:
        handleSettingsScreenTouch(touch);
        break;
      default:
        break;
    }
  }
}

void handleMainScreenTouch(TouchPoint touch) {
  // Check for admin button (bottom right corner)
  if (touch.x > 180 && touch.y > 280) {
    audio.playClickSound();
    currentState = STATE_ADMIN;
    display.showAdminLogin();
  }
}

void handleAdminScreenTouch(TouchPoint touch) {
  // Handle admin interface touches
  // Implementation will be added in Phase 3
}

void handleSettingsScreenTouch(TouchPoint touch) {
  // Handle settings interface touches
  // Implementation will be added in Phase 3
}

void updateDisplay() {
  switch (currentState) {
    case STATE_READY:
      display.updateMainScreen(timeManager.getCurrentTime(), lastEmployee, lastAction);
      break;
    case STATE_ADMIN:
      // Admin screen updates
      break;
    case STATE_SETTINGS:
      // Settings screen updates
      break;
    default:
      break;
  }
}

void handleTimeouts() {
  unsigned long currentTime = millis();
  
  // Auto-return to main screen after 30 seconds of inactivity
  if (currentTime - lastActivity > 30000) {
    if (currentState != STATE_READY) {
      currentState = STATE_READY;
      display.showMainScreen();
      ledManager.setStatus(LED_READY);
    }
  }
  
  // Enter sleep mode after 5 minutes of inactivity
  if (currentTime - lastActivity > 300000) {
    enterSleepMode();
  }
}

void handleErrorState() {
  static unsigned long lastErrorBlink = 0;
  unsigned long currentTime = millis();
  
  if (currentTime - lastErrorBlink > 1000) {
    ledManager.setStatus(LED_ERROR);
    lastErrorBlink = currentTime;
  }
  
  // Try to reinitialize every 10 seconds
  static unsigned long lastRetry = 0;
  if (currentTime - lastRetry > 10000) {
    Serial.println("Attempting system recovery...");
    if (initializeSystem()) {
      systemInitialized = true;
      currentState = STATE_READY;
      display.showMainScreen();
    }
    lastRetry = currentTime;
  }
}

void enterSleepMode() {
  Serial.println("Entering sleep mode...");
  display.turnOffBacklight();
  ledManager.setStatus(LED_OFF);
  
  // Configure wake-up sources
  esp_sleep_enable_ext0_wakeup(GPIO_NUM_36, 0); // Touch interrupt
  esp_sleep_enable_timer_wakeup(30 * 1000000);  // 30 seconds
  
  esp_light_sleep_start();
  
  // Wake up
  Serial.println("Waking up from sleep...");
  display.turnOnBacklight();
  ledManager.setStatus(LED_READY);
  lastActivity = millis();
}
