/*
 * Audio Manager Header - Minimal Version
 */

#ifndef AUDIO_MANAGER_H
#define AUDIO_MANAGER_H

#include <Arduino.h>
#include "config.h"

class AudioManager {
private:
  bool initialized;
  bool enabled;
  uint8_t volume;
  
public:
  AudioManager();
  ~AudioManager();
  
  bool begin();
  void reset();
  bool isInitialized() const { return initialized; }
  
  void enable() { enabled = true; }
  void disable() { enabled = false; }
  bool isEnabled() const { return enabled; }
  
  void setVolume(uint8_t vol);
  uint8_t getVolume() const { return volume; }
  
  void playStartupSound();
  void playSuccessSound();
  void playErrorSound();
  void playWarningSound();
  void playClickSound();
  void playPunchSound(bool punchIn);
  void playBeep(int frequency = SOUND_SUCCESS, int duration = SOUND_DURATION_SHORT);
  
  void testSpeaker();
};

#endif // AUDIO_MANAGER_H
