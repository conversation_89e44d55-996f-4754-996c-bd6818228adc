/*
 * Employee Database Implementation - Minimal Version
 */

#include "employee_db.h"

EmployeeDB::EmployeeDB() : 
  dataManager(nullptr),
  initialized(false),
  dataLoaded(false),
  totalEmployees(0),
  activeEmployees(0),
  lastSave(0) {
}

EmployeeDB::~EmployeeDB() {
  if (initialized && dataLoaded) {
    save();
  }
}

bool EmployeeDB::begin() {
  Serial.println("Initializing employee database...");
  
  initialized = true;
  
  // Load existing data
  if (loadFromFile()) {
    updateStatistics();
    Serial.println("Employee database loaded successfully");
    Serial.println("Total employees: " + String(totalEmployees));
    Serial.println("Active employees: " + String(activeEmployees));
  } else {
    Serial.println("No existing employee data found - starting fresh");
    
    // Create sample employee for testing
    Employee sampleEmployee;
    sampleEmployee.id = 1;
    sampleEmployee.name = "Admin User";
    sampleEmployee.rfidUID = "00:00:00:00";
    sampleEmployee.department = "Administration";
    sampleEmployee.shiftStart = "09:00";
    sampleEmployee.shiftEnd = "17:00";
    sampleEmployee.active = true;
    sampleEmployee.lastAction = "punch_out";
    sampleEmployee.lastTimestamp = "2024-01-01T00:00:00Z";
    sampleEmployee.pin = "0000";
    sampleEmployee.accessLevel = 4; // Super admin
    
    addEmployee(sampleEmployee);
    save();
  }
  
  Serial.println("Employee database initialized");
  return true;
}

bool EmployeeDB::begin(DataManager* dm) {
  dataManager = dm;
  return begin();
}

void EmployeeDB::reset() {
  employees.clear();
  totalEmployees = 0;
  activeEmployees = 0;
  dataLoaded = false;
  lastSave = 0;
}

bool EmployeeDB::addEmployee(const Employee& employee) {
  if (!validateEmployee(employee)) {
    Serial.println("Invalid employee data");
    return false;
  }
  
  // Check for duplicate ID
  if (findById(employee.id) != nullptr) {
    Serial.println("Employee ID already exists: " + String(employee.id));
    return false;
  }
  
  // Check for duplicate RFID
  if (employee.rfidUID.length() > 0 && isRFIDRegistered(employee.rfidUID)) {
    Serial.println("RFID already registered: " + employee.rfidUID);
    return false;
  }
  
  employees.push_back(employee);
  updateStatistics();
  
  Serial.println("Employee added: " + employee.name + " (ID: " + String(employee.id) + ")");
  return true;
}

bool EmployeeDB::updateEmployee(const Employee& employee) {
  Employee* existing = findById(employee.id);
  if (existing == nullptr) {
    Serial.println("Employee not found: " + String(employee.id));
    return false;
  }
  
  if (!validateEmployee(employee)) {
    Serial.println("Invalid employee data");
    return false;
  }
  
  *existing = employee;
  updateStatistics();
  
  Serial.println("Employee updated: " + employee.name + " (ID: " + String(employee.id) + ")");
  return true;
}

bool EmployeeDB::deleteEmployee(int employeeId) {
  for (auto it = employees.begin(); it != employees.end(); ++it) {
    if (it->id == employeeId) {
      Serial.println("Employee deleted: " + it->name + " (ID: " + String(employeeId) + ")");
      employees.erase(it);
      updateStatistics();
      return true;
    }
  }
  
  Serial.println("Employee not found for deletion: " + String(employeeId));
  return false;
}

Employee* EmployeeDB::findById(int employeeId) {
  for (auto& emp : employees) {
    if (emp.id == employeeId) {
      return &emp;
    }
  }
  return nullptr;
}

Employee* EmployeeDB::findByRFID(const String& rfidUID) {
  for (auto& emp : employees) {
    if (emp.rfidUID.equals(rfidUID) && emp.active) {
      return &emp;
    }
  }
  return nullptr;
}

Employee* EmployeeDB::findByName(const String& name) {
  for (auto& emp : employees) {
    if (emp.name.equalsIgnoreCase(name)) {
      return &emp;
    }
  }
  return nullptr;
}

std::vector<Employee> EmployeeDB::getAllEmployees() {
  return employees;
}

std::vector<Employee> EmployeeDB::getActiveEmployees() {
  std::vector<Employee> result;
  
  for (const auto& emp : employees) {
    if (emp.active) {
      result.push_back(emp);
    }
  }
  
  return result;
}

bool EmployeeDB::isRFIDRegistered(const String& rfidUID) {
  return findByRFID(rfidUID) != nullptr;
}

bool EmployeeDB::registerRFID(int employeeId, const String& rfidUID) {
  Employee* emp = findById(employeeId);
  if (emp == nullptr) {
    return false;
  }
  
  if (isRFIDRegistered(rfidUID)) {
    return false;
  }
  
  emp->rfidUID = rfidUID;
  Serial.println("RFID registered for " + emp->name + ": " + rfidUID);
  return true;
}

bool EmployeeDB::unregisterRFID(int employeeId) {
  Employee* emp = findById(employeeId);
  if (emp == nullptr) {
    return false;
  }
  
  emp->rfidUID = "";
  Serial.println("RFID unregistered for " + emp->name);
  return true;
}

bool EmployeeDB::save() {
  if (!initialized) return false;
  
  DynamicJsonDocument doc(JSON_BUFFER_SIZE * 4);
  JsonArray empArray = doc.createNestedArray("employees");
  
  for (const auto& emp : employees) {
    JsonObject empObj = empArray.createNestedObject();
    empObj["id"] = emp.id;
    empObj["name"] = emp.name;
    empObj["rfidUID"] = emp.rfidUID;
    empObj["department"] = emp.department;
    empObj["shiftStart"] = emp.shiftStart;
    empObj["shiftEnd"] = emp.shiftEnd;
    empObj["active"] = emp.active;
    empObj["lastAction"] = emp.lastAction;
    empObj["lastTimestamp"] = emp.lastTimestamp;
    empObj["pin"] = emp.pin;
    empObj["accessLevel"] = emp.accessLevel;
  }
  
  bool success = false;
  if (dataManager) {
    success = dataManager->writeJSON(EMPLOYEES_FILE, doc);
  } else {
    // Fallback to direct file write
    String jsonString;
    serializeJson(doc, jsonString);
    
    fs::File file = SD.open(EMPLOYEES_FILE, FILE_WRITE);
    if (file) {
      file.print(jsonString);
      file.close();
      success = true;
    }
  }
  
  if (success) {
    lastSave = millis();
    Serial.println("Employee database saved");
  } else {
    Serial.println("Failed to save employee database");
  }
  
  return success;
}

bool EmployeeDB::load() {
  return loadFromFile();
}

void EmployeeDB::updateStatistics() {
  totalEmployees = employees.size();
  activeEmployees = 0;
  
  for (const auto& emp : employees) {
    if (emp.active) {
      activeEmployees++;
    }
  }
}

void EmployeeDB::printEmployeeList() {
  Serial.println("=== Employee List ===");
  Serial.println("Total: " + String(totalEmployees) + ", Active: " + String(activeEmployees));
  Serial.println("ID\tName\t\tDepartment\tActive\tRFID");
  
  for (const auto& emp : employees) {
    Serial.print(emp.id);
    Serial.print("\t");
    Serial.print(emp.name);
    Serial.print("\t\t");
    Serial.print(emp.department);
    Serial.print("\t");
    Serial.print(emp.active ? "Yes" : "No");
    Serial.print("\t");
    Serial.println(emp.rfidUID);
  }
  
  Serial.println("====================");
}

// Private methods

bool EmployeeDB::loadFromFile() {
  DynamicJsonDocument doc(JSON_BUFFER_SIZE * 4);
  
  bool success = false;
  if (dataManager) {
    success = dataManager->readJSON(EMPLOYEES_FILE, doc);
  } else {
    // Fallback to direct file read
    fs::File file = SD.open(EMPLOYEES_FILE, FILE_READ);
    if (file) {
      String content = file.readString();
      file.close();
      
      DeserializationError error = deserializeJson(doc, content);
      success = (error == DeserializationError::Ok);
    }
  }
  
  if (!success) {
    Serial.println("No employee data file found or failed to parse");
    return false;
  }
  
  employees.clear();
  
  JsonArray empArray = doc["employees"];
  for (JsonObject empObj : empArray) {
    Employee emp;
    emp.id = empObj["id"];
    emp.name = empObj["name"].as<String>();
    emp.rfidUID = empObj["rfidUID"].as<String>();
    emp.department = empObj["department"].as<String>();
    emp.shiftStart = empObj["shiftStart"].as<String>();
    emp.shiftEnd = empObj["shiftEnd"].as<String>();
    emp.active = empObj["active"];
    emp.lastAction = empObj["lastAction"].as<String>();
    emp.lastTimestamp = empObj["lastTimestamp"].as<String>();
    emp.pin = empObj["pin"].as<String>();
    emp.accessLevel = empObj["accessLevel"];
    
    employees.push_back(emp);
  }
  
  dataLoaded = true;
  return true;
}

int EmployeeDB::getNextEmployeeId() {
  int maxId = 0;
  for (const auto& emp : employees) {
    if (emp.id > maxId) {
      maxId = emp.id;
    }
  }
  return maxId + 1;
}

bool EmployeeDB::validateEmployee(const Employee& emp) {
  if (emp.id <= 0) return false;
  if (emp.name.length() == 0 || emp.name.length() > 50) return false;
  if (emp.department.length() == 0) return false;
  if (emp.pin.length() != 4) return false;
  if (emp.accessLevel < 1 || emp.accessLevel > 4) return false;
  
  return true;
}

bool EmployeeDB::activateEmployee(int employeeId) {
  Employee* emp = findById(employeeId);
  if (emp == nullptr) {
    return false;
  }
  
  emp->active = true;
  updateStatistics();
  
  Serial.println("Employee activated: " + emp->name);
  return true;
}

bool EmployeeDB::deactivateEmployee(int employeeId) {
  Employee* emp = findById(employeeId);
  if (emp == nullptr) {
    return false;
  }
  
  emp->active = false;
  updateStatistics();
  
  Serial.println("Employee deactivated: " + emp->name);
  return true;
}
