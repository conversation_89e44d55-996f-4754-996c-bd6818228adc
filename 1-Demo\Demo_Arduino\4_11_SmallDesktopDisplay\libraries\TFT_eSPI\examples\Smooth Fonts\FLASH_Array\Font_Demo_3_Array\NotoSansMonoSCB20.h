/* The font vlw file can be converted to a byte array using:

   https://tomeko.net/online_tools/file_to_hex.php?lang=en

   Paste the byte array into a sketch tab and add two lines
   at the start with a unique font name:

                const uint8_t  fontName[] PROGMEM = {

   At the end add:

        };

   See example below. Include the tab in the main sketch, e.g.:

        #include "NotoSansMonoSCB20.h"
*/


const uint8_t  NotoSansMonoSCB20[] PROGMEM = {
0x00, 0x00, 0x00, 0x5E, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x07, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x23, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 
0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x27, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 
0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x2B, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2D, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x05, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x2F, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x35, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x37, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3A, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x05, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x43, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x47, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4D, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4E, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4F, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x51, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x53, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x54, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x56, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5A, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5B, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5C, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5E, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5F, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0B, 
0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 
0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x00, 0x00, 0x00, 0x0D, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 
0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x65, 0x00, 0x00, 0x00, 0x0D, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x67, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6B, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6C, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6D, 0x00, 0x00, 0x00, 0x0C, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6E, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0A, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6F, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 
0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 
0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x00, 0x00, 0x00, 0x11, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x73, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x75, 0x00, 0x00, 0x00, 0x0C, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7A, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x7B, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0B, 
0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7C, 
0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0x00, 0x00, 0x00, 0x13, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x48, 0x4A, 0x4A, 0x13, 0x00, 0xF2, 0xFF, 0xFF, 0x3F, 0x00, 0xEE, 0xFF, 0xFF, 0x1B, 0x00, 
0xD4, 0xFF, 0xFF, 0x0A, 0x00, 0xB6, 0xFF, 0xFF, 0x04, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 0xAE, 
0xFF, 0xF8, 0x00, 0x00, 0xA7, 0xFF, 0xF4, 0x00, 0x00, 0xA1, 0xFF, 0xEE, 0x00, 0x00, 0x9D, 0xFF, 
0xE5, 0x00, 0x00, 0x30, 0x50, 0x3B, 0x00, 0x00, 0x00, 0x2C, 0x04, 0x00, 0x00, 0xC1, 0xFF, 0xE9, 
0x13, 0x02, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0xBF, 0xFF, 0xE9, 0x13, 0x00, 0x00, 0x30, 0x04, 0x00, 
0x35, 0x4A, 0x48, 0x00, 0x33, 0x4A, 0x4A, 0xAC, 0xFF, 0xF4, 0x00, 0xAA, 0xFF, 0xF6, 0xA3, 0xFF, 
0xE9, 0x00, 0x9F, 0xFF, 0xEE, 0x99, 0xFF, 0xB8, 0x00, 0x92, 0xFF, 0xC1, 0x6C, 0xFF, 0xB0, 0x00, 
0x61, 0xFF, 0xB0, 0x55, 0xEB, 0x99, 0x00, 0x52, 0xEB, 0x99, 0x00, 0x00, 0x00, 0x02, 0x4A, 0x44, 
0x00, 0x1D, 0x4A, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x2C, 0xFF, 0xB2, 0x00, 0x92, 0xFF, 0x55, 0x00, 
0x00, 0x00, 0x00, 0x52, 0xFF, 0x9B, 0x00, 0xAE, 0xFF, 0x39, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 
0x63, 0x00, 0xDF, 0xFF, 0x02, 0x00, 0x06, 0x57, 0x57, 0xC3, 0xFF, 0x8A, 0x57, 0xFC, 0xF6, 0x57, 
0x55, 0x13, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x0C, 0x9B, 0x9B, 0xFF, 
0xFA, 0x9B, 0xBD, 0xFF, 0xD6, 0x9B, 0x96, 0x00, 0x00, 0x0A, 0xFF, 0xD2, 0x00, 0x70, 0xFF, 0x61, 
0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xA7, 0x00, 0xA1, 0xFF, 0x4A, 0x00, 0x00, 0xA5, 0xF2, 0xF6, 
0xFF, 0xFA, 0xF2, 0xFA, 0xFF, 0xF6, 0xF2, 0x59, 0xAA, 0xF8, 0xFC, 0xFF, 0xFA, 0xF8, 0xFF, 0xFF, 
0xF8, 0xF8, 0x5B, 0x00, 0x00, 0xAE, 0xFF, 0x3D, 0x0A, 0xFF, 0xDA, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xD6, 0xFF, 0x06, 0x4A, 0xFF, 0xAC, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0xF2, 0x00, 0x5D, 0xFF, 
0x8C, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xFF, 0xB8, 0x00, 0x96, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x39, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF2, 0x50, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x4C, 0xA5, 0xFA, 0xC9, 0xA1, 0x5B, 0x08, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x77, 0x4A, 0xFF, 0xFF, 0x92, 0xF6, 0x92, 0x94, 0xDF, 0x22, 0x92, 0xFF, 0xC7, 0x00, 
0xF2, 0x50, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xF0, 0x1D, 0xF2, 0x50, 0x00, 0x00, 0x00, 0x26, 0xF6, 
0xFF, 0xF6, 0xFA, 0x66, 0x00, 0x00, 0x00, 0x00, 0x41, 0xE5, 0xFF, 0xFF, 0xFF, 0xBF, 0x2C, 0x00, 
0x00, 0x00, 0x08, 0x6E, 0xFA, 0xFF, 0xFF, 0xF8, 0x41, 0x00, 0x00, 0x00, 0x00, 0xF2, 0x8A, 0xE1, 
0xFF, 0xC3, 0x00, 0x00, 0x00, 0x00, 0xF2, 0x50, 0x5D, 0xFF, 0xF2, 0x4C, 0x6E, 0x11, 0x00, 0xF2, 
0x52, 0xAA, 0xFF, 0xC1, 0x63, 0xFF, 0xFC, 0xDD, 0xFA, 0xFC, 0xFF, 0xFF, 0x4A, 0x39, 0xCE, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE1, 0x57, 0x00, 0x00, 0x00, 0x06, 0x39, 0xF6, 0x63, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xF2, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x22, 0x5B, 0x55, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0xFF, 
0xFF, 0xD4, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC3, 0xFF, 0x57, 0x9D, 0xFF, 0x6A, 0x00, 
0x24, 0xF8, 0xDF, 0x08, 0x00, 0xFA, 0xEE, 0x00, 0x4A, 0xFF, 0x9F, 0x00, 0xBF, 0xFF, 0x48, 0x00, 
0x00, 0xF8, 0xF0, 0x00, 0x4C, 0xFF, 0x9F, 0x59, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0x68, 
0xA7, 0xFF, 0x77, 0xE9, 0xF2, 0x19, 0x00, 0x00, 0x00, 0x33, 0xF2, 0xFF, 0xFF, 0xC7, 0x9F, 0xFF, 
0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x4E, 0x41, 0x39, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x02, 0xD4, 0xFF, 0x39, 0x6E, 0x9B, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7B, 0xFF, 0x8E, 0xB8, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x22, 0xF6, 0xE5, 0x3F, 0xFF, 
0xBD, 0x0A, 0xD4, 0xFF, 0x17, 0x00, 0x00, 0xB6, 0xFF, 0x50, 0x55, 0xFF, 0x94, 0x00, 0xA3, 0xFF, 
0x48, 0x00, 0x57, 0xFF, 0xB4, 0x00, 0x50, 0xFF, 0x99, 0x00, 0xAA, 0xFF, 0x41, 0x00, 0x9B, 0xB4, 
0x1F, 0x00, 0x19, 0xFC, 0xE7, 0x72, 0xF4, 0xF0, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 
0xFA, 0xFF, 0xF2, 0x4E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x30, 0x02, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x5B, 0x59, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0xFF, 
0xFF, 0xFF, 0xF6, 0x41, 0x00, 0x00, 0x00, 0x00, 0x17, 0xFC, 0xFF, 0x94, 0xB6, 0xFF, 0xCE, 0x00, 
0x00, 0x00, 0x00, 0x4E, 0xFF, 0xFA, 0x00, 0x4A, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x48, 0xFF, 
0xFF, 0x11, 0x6E, 0xFF, 0xE1, 0x00, 0x00, 0x00, 0x00, 0x06, 0xF8, 0xFF, 0x9D, 0xEE, 0xFF, 0x7B, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0xFF, 0xBF, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x79, 0xFF, 0xFF, 0xEE, 0x0C, 0x00, 0x7B, 0x94, 0x4A, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xFF, 0x6C, 
0x02, 0xFA, 0xFF, 0x55, 0x0C, 0xF2, 0xFF, 0xB0, 0xA1, 0xFF, 0xF4, 0x6C, 0xFF, 0xFF, 0x1B, 0x55, 
0xFF, 0xFF, 0x1D, 0x11, 0xE9, 0xFF, 0xFC, 0xFF, 0xC7, 0x00, 0x5F, 0xFF, 0xFF, 0x02, 0x00, 0x52, 
0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x52, 0xFF, 0xFF, 0x88, 0x08, 0x57, 0xFA, 0xFF, 0xFF, 0x2C, 0x00, 
0x0A, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x06, 0x00, 0x3B, 0xE1, 0xFF, 0xFF, 
0xFA, 0x92, 0x6A, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x00, 0x1F, 0x2E, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x35, 0x4A, 0x48, 0xAC, 0xFF, 0xF4, 0xA3, 0xFF, 0xE9, 0x99, 0xFF, 0xB8, 0x6C, 0xFF, 0xB0, 
0x55, 0xEB, 0x99, 0x00, 0x00, 0x00, 0x30, 0x4A, 0x30, 0x00, 0x00, 0x22, 0xF4, 0xFF, 0x35, 0x00, 
0x00, 0xAC, 0xFF, 0xAA, 0x00, 0x00, 0x28, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x8A, 0xFF, 0xD8, 0x00, 
0x00, 0x00, 0xD8, 0xFF, 0x90, 0x00, 0x00, 0x0A, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x48, 0xFF, 0xFF, 
0x37, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0x0A, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x55, 
0xFF, 0xFF, 0x08, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x17, 0xFF, 0xFF, 0x52, 0x00, 
0x00, 0x00, 0xE7, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xD8, 0x00, 0x00, 0x00, 0x35, 0xFF, 
0xFF, 0x35, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x2A, 0xF8, 0xFF, 0x30, 0x00, 
0x00, 0x00, 0x37, 0x50, 0x33, 0x33, 0x50, 0x37, 0x00, 0x00, 0x00, 0x2E, 0xFF, 0xF8, 0x2A, 0x00, 
0x00, 0x00, 0xA7, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x33, 0xFF, 0xFF, 0x37, 0x00, 0x00, 0x00, 0xD8, 
0xFF, 0x96, 0x00, 0x00, 0x00, 0x8E, 0xFF, 0xE7, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0x17, 0x00, 
0x00, 0x35, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x0A, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x06, 0xFF, 0xFF, 
0x57, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0x52, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x57, 
0xFF, 0xFF, 0x08, 0x00, 0x00, 0x92, 0xFF, 0xD8, 0x00, 0x00, 0x00, 0xD8, 0xFF, 0x88, 0x00, 0x00, 
0x35, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0xAC, 0xFF, 0xB0, 0x00, 0x00, 0x39, 0xFF, 0xF4, 0x22, 0x00, 
0x00, 0x30, 0x4A, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x13, 0x0C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x63, 0xFF, 0xB2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xA3, 0x00, 
0x00, 0x00, 0x3F, 0xB2, 0x66, 0x68, 0xFF, 0x9D, 0x50, 0x9F, 0x77, 0x74, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB6, 0x2E, 0x57, 0x7B, 0xFA, 0xFF, 0xFF, 0xA1, 0x5D, 0x4A, 0x00, 0x00, 0x7D, 
0xFF, 0xD2, 0xFF, 0xC1, 0x02, 0x00, 0x00, 0x4A, 0xFF, 0xF8, 0x19, 0xD0, 0xFF, 0x94, 0x00, 0x00, 
0x28, 0xC5, 0x88, 0x00, 0x46, 0xE3, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x08, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x55, 0xE5, 0x9D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xAE, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x04, 0x06, 0x06, 0x61, 
0xFF, 0xB0, 0x06, 0x06, 0x06, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0xF8, 
0xF8, 0xFA, 0xFF, 0xFC, 0xF8, 0xF8, 0xF8, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x5D, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xAE, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xA7, 0x72, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xA7, 0xA7, 0x37, 
0x00, 0x7F, 0xFF, 0xFF, 0x2A, 0x00, 0xA7, 0xFF, 0xEB, 0x00, 0x00, 0xD6, 0xFF, 0xA3, 0x00, 0x02, 
0xFC, 0xFF, 0x4C, 0x00, 0x3B, 0xF8, 0xDF, 0x02, 0x00, 0x24, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x39, 
0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x39, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0x59, 0x00, 0x00, 
0x2C, 0x04, 0x00, 0x00, 0xC1, 0xFF, 0xE9, 0x13, 0x02, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0xBF, 0xFF, 
0xE9, 0x13, 0x00, 0x00, 0x30, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4C, 0x57, 0x28, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, 
0xD4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE1, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 
0xFF, 0xFC, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0C, 0xFA, 0xFF, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xFF, 0xEE, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xC7, 0xFF, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xFF, 0xFF, 0x30, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x88, 0xFF, 0xD2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xE7, 0xFF, 0x70, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x4C, 0xFF, 0xFA, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xB0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xFC, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x06, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x50, 0x63, 0x5B, 0x1F, 0x00, 0x00, 
0x00, 0x00, 0x1F, 0xDD, 0xFF, 0xFF, 0xFF, 0xFC, 0x68, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xFF, 0xF0, 
0xFC, 0xFF, 0xFF, 0x33, 0x00, 0x3F, 0xFF, 0xFF, 0x8C, 0x00, 0x33, 0xFF, 0xFF, 0xA3, 0x00, 0x8C, 
0xFF, 0xFF, 0x1B, 0x00, 0x74, 0xFF, 0xFF, 0xF6, 0x00, 0xAE, 0xFF, 0xF4, 0x00, 0x17, 0xF2, 0xFF, 
0xFF, 0xFF, 0x19, 0xBF, 0xFF, 0xE3, 0x00, 0x9D, 0xFF, 0xDF, 0xFF, 0xFF, 0x48, 0xEB, 0xFF, 0xBB, 
0x30, 0xFF, 0xDF, 0x68, 0xFF, 0xFF, 0x4A, 0xDD, 0xFF, 0xC7, 0xCE, 0xFF, 0x4E, 0x61, 0xFF, 0xFF, 
0x4A, 0xB6, 0xFF, 0xFF, 0xFF, 0xB8, 0x00, 0x6E, 0xFF, 0xFF, 0x44, 0xA7, 0xFF, 0xFF, 0xFA, 0x24, 
0x00, 0x9B, 0xFF, 0xFF, 0x0E, 0x70, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0xC5, 0xFF, 0xE7, 0x00, 0x24, 
0xFF, 0xFF, 0xB4, 0x17, 0x5F, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xF0, 0x1B, 0x00, 0x00, 0x04, 0x92, 0xFC, 0xFF, 0xFF, 0xD8, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x37, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x4A, 0x4A, 0x1D, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x6E, 0xE7, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x52, 0xDA, 0xFF, 0xFF, 0xFF, 
0xFF, 0x63, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 0xEB, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x30, 
0xEB, 0x6E, 0x4C, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x4A, 0xFF, 0xFF, 0x63, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x4A, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0x63, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 
0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x1B, 0x7B, 0xFF, 0xFF, 0x8E, 0x2C, 0x04, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x0C, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x1B, 
0x57, 0x63, 0x5D, 0x2A, 0x00, 0x00, 0x00, 0x04, 0x90, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00, 
0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xF4, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 0x17, 0xE7, 0x96, 0x15, 0x00, 
0x4A, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xFF, 0xF8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x02, 0xF8, 0xFF, 0xDD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5B, 0xFF, 
0xFF, 0x8C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xEB, 0xFF, 0xE9, 0x13, 0x00, 0x00, 0x00, 0x00, 
0x08, 0xCC, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x66, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x8E, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xFF, 0x88, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xD8, 0x57, 0x57, 0x57, 0x57, 0x57, 0x19, 0xBB, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0xBB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x28, 0x59, 0x63, 0x5B, 0x24, 0x00, 0x00, 0x00, 0x2E, 0xC5, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 0x46, 0xFF, 0xFF, 0xFA, 0xEE, 0xFF, 0xFF, 0xFF, 0x5F, 
0x00, 0x00, 0x8A, 0x59, 0x02, 0x00, 0x44, 0xFF, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF8, 0xFF, 0xA5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 
0x00, 0x44, 0x66, 0x9F, 0xF4, 0xFF, 0xAA, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xFF, 0x9F, 
0x0A, 0x00, 0x00, 0x00, 0x00, 0x7B, 0xB6, 0xE3, 0xFF, 0xFF, 0xE9, 0x33, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x41, 0xFC, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0xFF, 0xFF, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE1, 0xFF, 0xFC, 0x00, 0x90, 0x8E, 0x4C, 0x1F, 0x39, 
0x9B, 0xFF, 0xFF, 0xC7, 0x00, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x3D, 0x00, 0x5D, 
0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0xC5, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x06, 0x2E, 0x35, 0x06, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x4A, 0x41, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xD4, 0xFF, 
0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0xFF, 0xE5, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x15, 0xEE, 0xFA, 0xCC, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0xFF, 0x88, 
0xB4, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFF, 0xEE, 0x0E, 0xB8, 0xFF, 0xE5, 0x00, 0x00, 
0x00, 0x00, 0xCE, 0xFF, 0x70, 0x00, 0xBB, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xD8, 0x02, 
0x00, 0xBB, 0xFF, 0xE5, 0x00, 0x00, 0x0C, 0xE9, 0xFF, 0x8E, 0x44, 0x44, 0xCC, 0xFF, 0xEE, 0x44, 
0x26, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x4A, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xE5, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xBB, 0xFF, 0xE5, 0x00, 0x00, 0x00, 0x37, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 
0x02, 0x00, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0xF4, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x06, 0x00, 0xFC, 0xFF, 0xA1, 0x0C, 0x0C, 0x0C, 0x0C, 0x00, 0x08, 0xFF, 0xFF, 0x8E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xFF, 0xFF, 0x61, 0x04, 0x02, 0x00, 0x00, 0x00, 0x41, 0xFF, 
0xFF, 0xF8, 0xFF, 0xFC, 0xBD, 0x26, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x19, 
0x00, 0x4E, 0x50, 0x44, 0x4E, 0xD0, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xFF, 
0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x28, 0xFF, 0xFF, 0xB0, 0x8C, 0x94, 0x52, 0x44, 0x4E, 0xD0, 0xFF, 0xFF, 0x74, 0x94, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xCE, 0x08, 0x55, 0xDD, 0xFF, 0xFF, 0xFF, 0xF8, 0x92, 0x0E, 0x00, 0x00, 
0x00, 0x06, 0x3B, 0x24, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0x57, 0x5D, 0x5B, 
0x17, 0x00, 0x00, 0x00, 0x37, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x35, 0xF6, 0xFF, 
0xFF, 0xF4, 0xBD, 0xE7, 0x4A, 0x00, 0x00, 0xD6, 0xFF, 0xF6, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x41, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8E, 0xFF, 0xFC, 0x0E, 0x17, 0x4A, 
0x28, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xDF, 0x6E, 0xFC, 0xFF, 0xFF, 0xBD, 0x0C, 0x00, 0xD0, 0xFF, 
0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x92, 0x00, 0xEB, 0xFF, 0xFF, 0x9B, 0x04, 0x28, 0xF2, 0xFF, 
0xF6, 0x00, 0xD6, 0xFF, 0xFC, 0x08, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x19, 0xB2, 0xFF, 0xFA, 0x00, 
0x00, 0x00, 0x9D, 0xFF, 0xFF, 0x37, 0x88, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x06, 
0x2C, 0xFF, 0xFF, 0xC3, 0x48, 0x6C, 0xFF, 0xFF, 0xCC, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x88, 0xFA, 0xFF, 0xFF, 0xE9, 0x59, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x39, 0x26, 0x00, 0x00, 0x00, 0x00, 0x46, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 
0x4A, 0x17, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0xF2, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0xD0, 0xFF, 0xE7, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0xFF, 0xFF, 0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 
0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xFA, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x74, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDD, 0xFF, 0xEB, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0xFF, 0xFF, 0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 
0xFF, 0xFF, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x83, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xE9, 0xFF, 0xEE, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x50, 0x5D, 0x57, 0x28, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 
0x02, 0x00, 0x1B, 0xF8, 0xFF, 0xFF, 0xC3, 0xF0, 0xFF, 0xFF, 0x7B, 0x00, 0x57, 0xFF, 0xFF, 0x70, 
0x00, 0x15, 0xFA, 0xFF, 0xB2, 0x00, 0x57, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0xF0, 0xFF, 0xB0, 0x00, 
0x1B, 0xFC, 0xFF, 0xBF, 0x08, 0x5F, 0xFF, 0xFF, 0x74, 0x00, 0x00, 0x77, 0xFF, 0xFF, 0xEB, 0xFF, 
0xFF, 0xBD, 0x04, 0x00, 0x00, 0x00, 0xB6, 0xFF, 0xFF, 0xFF, 0xDA, 0x08, 0x00, 0x00, 0x00, 0x9F, 
0xFF, 0xFF, 0xEB, 0xFF, 0xFF, 0xCE, 0x15, 0x00, 0x5B, 0xFF, 0xFF, 0x9D, 0x00, 0x6C, 0xFF, 0xFF, 
0xB2, 0x00, 0xAE, 0xFF, 0xF6, 0x06, 0x00, 0x00, 0x9D, 0xFF, 0xFF, 0x15, 0xB6, 0xFF, 0xEB, 0x00, 
0x00, 0x00, 0x70, 0xFF, 0xFF, 0x39, 0xA7, 0xFF, 0xFF, 0x63, 0x02, 0x2A, 0xDD, 0xFF, 0xFA, 0x04, 
0x35, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0x00, 0x00, 0x3F, 0xCE, 0xFF, 0xFF, 0xFF, 
0xE7, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3B, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x15, 0x59, 0x6E, 0x5B, 0x15, 0x00, 0x00, 0x00, 0x00, 0x50, 0xF4, 0xFF, 0xFF, 0xFF, 0xF4, 0x4E, 
0x00, 0x00, 0x22, 0xF6, 0xFF, 0xFF, 0xF6, 0xFF, 0xFF, 0xFA, 0x2A, 0x00, 0x8E, 0xFF, 0xFF, 0x77, 
0x00, 0x3F, 0xFF, 0xFF, 0xA1, 0x00, 0xB4, 0xFF, 0xFC, 0x02, 0x00, 0x00, 0xBD, 0xFF, 0xF6, 0x00, 
0xC9, 0xFF, 0xF4, 0x00, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 0x15, 0xB4, 0xFF, 0xFC, 0x0C, 0x00, 0x00, 
0xCC, 0xFF, 0xFF, 0x41, 0x8C, 0xFF, 0xFF, 0xAA, 0x4A, 0x96, 0xFF, 0xFF, 0xFF, 0x44, 0x1F, 0xF2, 
0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0x1D, 0x00, 0x39, 0xC9, 0xFA, 0xE5, 0x72, 0x92, 0xFF, 
0xFC, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xCC, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x44, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x30, 0x08, 0x19, 0x6A, 0xEE, 0xFF, 0xEB, 0x0E, 0x00, 
0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x46, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFC, 0xB2, 
0x2E, 0x00, 0x00, 0x00, 0x00, 0x11, 0x3D, 0x24, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x00, 0x00, 0xAC, 0xFF, 0xD6, 0x0C, 0x02, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0xD2, 0xFF, 
0xF2, 0x1B, 0x00, 0x0C, 0x4C, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0x04, 0x00, 0x00, 0xC1, 0xFF, 0xE9, 0x13, 
0x02, 0xFF, 0xFF, 0xFF, 0x4A, 0x00, 0xBF, 0xFF, 0xE9, 0x13, 0x00, 0x00, 0x30, 0x04, 0x00, 0x00, 
0x00, 0x02, 0x02, 0x00, 0x00, 0x57, 0xFA, 0xFA, 0x46, 0x00, 0xAC, 0xFF, 0xFF, 0xA1, 0x00, 0x7B, 
0xFF, 0xFF, 0x6A, 0x00, 0x00, 0x3F, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xA7, 0xA7, 
0x37, 0x00, 0x7F, 0xFF, 0xFF, 0x2A, 0x00, 0xA7, 0xFF, 0xE9, 0x00, 0x00, 0xD6, 0xFF, 0x9D, 0x00, 
0x02, 0xFC, 0xFF, 0x4A, 0x00, 0x3B, 0xF8, 0xDF, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x13, 0x83, 0x92, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x7F, 0xF4, 0xFF, 0x9B, 0x00, 0x00, 0x0C, 0x7D, 
0xF0, 0xFF, 0xFF, 0xBB, 0x33, 0x08, 0x74, 0xEB, 0xFF, 0xFF, 0xB0, 0x33, 0x00, 0x00, 0xD6, 0xFF, 
0xFF, 0xAC, 0x2C, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0xE7, 0x77, 0x0C, 0x00, 0x00, 0x00, 
0x00, 0x39, 0xB8, 0xFF, 0xFF, 0xF4, 0x85, 0x17, 0x00, 0x00, 0x00, 0x00, 0x33, 0xB2, 0xFF, 0xFF, 
0xFA, 0x68, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xB0, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x2A, 0x59, 0x2A, 0x3D, 0x3D, 0x3D, 0x3D, 0x3D, 0x3D, 0x3D, 0x3D, 0xAE, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2E, 
0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xA5, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xAE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x44, 0x63, 0x63, 0x63, 0x63, 0x63, 0x63, 0x63, 0x63, 0x92, 0x83, 0x11, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xF2, 0x7D, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x33, 0xBF, 
0xFF, 0xFF, 0xF0, 0x7D, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x33, 0xB2, 0xFF, 0xFF, 0xEB, 0x77, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x2E, 0xAC, 0xFF, 0xFF, 0xD6, 0x00, 0x00, 0x00, 0x0E, 0x77, 0xE7, 0xFF, 
0xFF, 0xAE, 0x00, 0x17, 0x85, 0xF4, 0xFF, 0xFF, 0xB8, 0x37, 0x00, 0x68, 0xFA, 0xFF, 0xFF, 0xB4, 
0x33, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xAE, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x2A, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0x57, 0x63, 0x5D, 0x26, 0x00, 0x00, 0x1F, 
0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x92, 0x00, 0x0E, 0xF4, 0xFF, 0xF8, 0xE9, 0xFC, 0xFF, 0xFF, 
0x55, 0x00, 0x68, 0x4C, 0x02, 0x00, 0x2E, 0xFF, 0xFF, 0x9F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6C, 0xFF, 0xFF, 0x66, 0x00, 0x00, 0x00, 0x00, 
0x66, 0xFF, 0xFF, 0xC5, 0x04, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0xC1, 0x11, 0x00, 0x00, 0x00, 
0x00, 0xE7, 0xFF, 0xC1, 0x06, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x50, 0x50, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x22, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3D, 0xFC, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 
0xB6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xFC, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x13, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x46, 0x48, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0A, 0x94, 0xFA, 0xFF, 0xFF, 0xFC, 0x9D, 0x0C, 0x00, 0x00, 0x00, 0x08, 
0xC7, 0xFF, 0xB2, 0x52, 0x4E, 0xA3, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0x8E, 0x00, 0x00, 
0x00, 0x00, 0x99, 0xFF, 0x5D, 0x00, 0x19, 0xFC, 0xD8, 0x02, 0x06, 0x50, 0x57, 0x28, 0x15, 0xFC, 
0xC5, 0x00, 0x72, 0xFF, 0x6A, 0x15, 0xD8, 0xFF, 0xFF, 0xFF, 0x59, 0xC1, 0xFC, 0x04, 0xAE, 0xFF, 
0x24, 0x96, 0xFF, 0x85, 0xB0, 0xFF, 0x55, 0xA3, 0xFF, 0x39, 0xE5, 0xF8, 0x00, 0xF4, 0xEB, 0x00, 
0x96, 0xFF, 0x4C, 0x9D, 0xFF, 0x48, 0xFA, 0xEE, 0x0A, 0xFF, 0xB0, 0x00, 0x9D, 0xFF, 0x48, 0xA1, 
0xFF, 0x46, 0xFF, 0xEB, 0x0A, 0xFF, 0xB0, 0x00, 0xAE, 0xFF, 0x44, 0xAC, 0xFF, 0x13, 0xF6, 0xF4, 
0x00, 0xFA, 0xDA, 0x02, 0xE7, 0xFF, 0x4A, 0xDF, 0xDD, 0x00, 0xCC, 0xFF, 0x0A, 0xA1, 0xFF, 0xF6, 
0xF2, 0xFA, 0xF6, 0xFF, 0x72, 0x00, 0x90, 0xFF, 0x57, 0x15, 0xA5, 0xC1, 0x46, 0x5D, 0xBF, 0x8C, 
0x00, 0x00, 0x33, 0xFF, 0xCE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9F, 
0xFF, 0xA7, 0x0E, 0x00, 0x00, 0x02, 0x55, 0x08, 0x00, 0x00, 0x00, 0x06, 0xAC, 0xFF, 0xFA, 0xCC, 
0xD4, 0xFA, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x9D, 0xB2, 0xB2, 0x96, 0x3F, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0x50, 0x50, 0x50, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x63, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xE9, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEB, 0xFF, 0xBF, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0x00, 0x00, 
0x28, 0xFF, 0xFF, 0x4E, 0xFC, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0x0E, 0xC9, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xDA, 0x00, 0x99, 0xFF, 0xEB, 0x00, 0x00, 0x00, 
0x00, 0xEB, 0xFF, 0xA3, 0x00, 0x57, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0x28, 0xFF, 0xFF, 0x5F, 0x00, 
0x22, 0xFF, 0xFF, 0x66, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0xF6, 0xF2, 0xF2, 0xFF, 0xFF, 0xA7, 0x00, 
0x00, 0xA7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x00, 0x00, 0xEB, 0xFF, 0xCC, 0x63, 
0x63, 0x63, 0xA1, 0xFF, 0xFF, 0x2A, 0x28, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 0x00, 0x48, 0xFF, 0xFF, 
0x68, 0x63, 0xFF, 0xFF, 0x4C, 0x00, 0x00, 0x00, 0x04, 0xFC, 0xFF, 0xA7, 0xA7, 0xFF, 0xFC, 0x06, 
0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0xEE, 0x2C, 0x4A, 0x4A, 0x4A, 0x4A, 0x46, 0x0E, 0x00, 0x00, 
0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x1B, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xD4, 0x00, 0x9B, 0xFF, 0xFF, 0x00, 0x04, 0x33, 0xE7, 0xFF, 0xFF, 0x2A, 0x9B, 
0xFF, 0xFF, 0x00, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 0x44, 0x9B, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0xBD, 
0xFF, 0xFC, 0x06, 0x9B, 0xFF, 0xFF, 0xA1, 0xA1, 0xC3, 0xFF, 0xFF, 0x88, 0x00, 0x9B, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0x02, 0x00, 0x9B, 0xFF, 0xFF, 0xBB, 0xBB, 0xEE, 0xFF, 0xFF, 0xC1, 
0x02, 0x9B, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 0x4E, 0x9B, 0xFF, 0xFF, 0x00, 0x00, 
0x00, 0x59, 0xFF, 0xFF, 0x7F, 0x9B, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x79, 0xFF, 0xFF, 0x72, 0x9B, 
0xFF, 0xFF, 0x57, 0x57, 0x77, 0xF0, 0xFF, 0xFF, 0x39, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xA5, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xD4, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x4C, 0x61, 0x74, 0x57, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x55, 0xE7, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x66, 0x00, 0x00, 0x61, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x26, 0x00, 0x1D, 
0xF6, 0xFF, 0xFF, 0x72, 0x08, 0x00, 0x2A, 0x6E, 0x00, 0x00, 0x88, 0xFF, 0xFF, 0x79, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xD6, 0xFF, 0xFC, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0xFC, 0xFF, 0xCE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0xFF, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE1, 0xFF, 0xFC, 0x0C, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x28, 0xFC, 0xFF, 0xFF, 0xA1, 0x50, 0x4C, 0x6A, 0xC3, 0x06, 0x00, 0x00, 0x72, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x55, 0xD6, 0xFF, 0xFF, 0xFF, 0xFC, 
0xBD, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x44, 0x30, 0x02, 0x00, 0x00, 0x11, 0x4A, 0x4A, 
0x4A, 0x4A, 0x48, 0x15, 0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 
0x2C, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x35, 0x00, 0x3D, 0xFF, 
0xFF, 0xA1, 0x13, 0x41, 0xA3, 0xFF, 0xFF, 0xD6, 0x00, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 
0xB8, 0xFF, 0xFF, 0x44, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0x8A, 0x3D, 
0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 0xA7, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 
0x00, 0x08, 0xFF, 0xFF, 0xAE, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xAC, 
0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0x9F, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 
0x00, 0x00, 0x77, 0xFF, 0xFF, 0x61, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x15, 0xE7, 0xFF, 0xFA, 
0x17, 0x3D, 0xFF, 0xFF, 0xBF, 0x5F, 0x8A, 0xE7, 0xFF, 0xFF, 0x8C, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xB6, 0x04, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0xBF, 0x63, 0x00, 
0x00, 0x00, 0x2C, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0xFF, 0xFF, 
0x46, 0x3D, 0x3D, 0x3D, 0x3D, 0x3D, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 
0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x9F, 0x9B, 0x9B, 0x9B, 0x9B, 
0x6A, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x9B, 0xFF, 0xFF, 0xEB, 0xEB, 0xEB, 
0xEB, 0xEB, 0xA1, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 
0xFF, 0x6C, 0x63, 0x63, 0x63, 0x63, 0x63, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x2C, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 
0x4A, 0x4A, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0xFF, 0xFF, 0x46, 0x3D, 0x3D, 0x3D, 0x3D, 0x3D, 0x9B, 0xFF, 0xFF, 
0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 
0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAE, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x9B, 0xFF, 0xFF, 0x6C, 0x63, 0x63, 
0x63, 0x63, 0x44, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 
0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x26, 0x57, 0x63, 0x5D, 0x44, 0x02, 0x00, 0x00, 0x00, 0x13, 0xAC, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x2C, 0x00, 0x08, 0xD2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 
0x02, 0x00, 0x83, 0xFF, 0xFF, 0xD4, 0x3B, 0x00, 0x02, 0x39, 0x5D, 0x00, 0x02, 0xEB, 0xFF, 0xF6, 
0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x5B, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 
0x50, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 0xFF, 0x5D, 0x63, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0xF2, 0xFF, 
0xFF, 0xFF, 0x5D, 0x5B, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0x4C, 0x55, 0xFF, 0xFF, 0x5D, 0x3B, 0xFF, 
0xFF, 0xAA, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x5D, 0x02, 0xF0, 0xFF, 0xFA, 0x24, 0x00, 0x00, 
0x06, 0xFF, 0xFF, 0x5D, 0x00, 0x81, 0xFF, 0xFF, 0xE3, 0x61, 0x46, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 
0x06, 0xC3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x06, 0x83, 0xE9, 0xFF, 
0xFF, 0xFF, 0xFC, 0xBF, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x3D, 0x1D, 0x02, 0x00, 0x00, 
0x11, 0x4A, 0x4A, 0x2C, 0x00, 0x00, 0x00, 0x17, 0x4A, 0x4A, 0x1B, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 
0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 
0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 
0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 
0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0xDD, 0xA7, 0xA7, 0xA7, 0xC3, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0xE3, 0xBB, 0xBB, 0xBB, 0xD0, 
0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 
0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 
0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 
0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 
0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x1B, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x30, 0x5D, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x41, 0xC5, 0xF2, 0xFC, 0xFF, 0xFF, 0xF6, 0xD2, 0x74, 
0x00, 0x00, 0x00, 0xAE, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xF2, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 
0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xAE, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xAE, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xF2, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x3D, 0xC5, 0xFF, 0xF6, 0x44, 0x13, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xA7, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x2A, 0x4A, 0x4A, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 
0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x94, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0x41, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB8, 0xFF, 0xFF, 0x11, 0x5D, 0x59, 0x48, 0x48, 0x8A, 0xFF, 0xFF, 0xDA, 
0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 0x00, 0x83, 0xFC, 0xFF, 0xFF, 0xFF, 0xE1, 
0x5B, 0x00, 0x00, 0x00, 0x02, 0x22, 0x3D, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x11, 0x4A, 0x4A, 0x2C, 
0x00, 0x00, 0x00, 0x19, 0x4A, 0x4A, 0x33, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0xC5, 0xFF, 
0xFF, 0x3F, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0x9B, 0x00, 0x3D, 0xFF, 0xFF, 
0x9B, 0x00, 0x17, 0xEE, 0xFF, 0xE5, 0x0E, 0x00, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0xA7, 0xFF, 0xFF, 
0x50, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0x9B, 0x4A, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x3D, 0xFF, 
0xFF, 0x9D, 0xDD, 0xFF, 0xEE, 0x17, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xF4, 0xFF, 0xFF, 0xD2, 
0x00, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x3D, 
0xFF, 0xFF, 0xFA, 0x7B, 0xFF, 0xFF, 0xD6, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0x9D, 0x00, 0xA5, 
0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x28, 0xFF, 0xFF, 0xDD, 0x02, 0x00, 
0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0x6C, 0x00, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 
0x00, 0x26, 0xFF, 0xFF, 0xE3, 0x06, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 
0x72, 0x19, 0x4A, 0x4A, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 
0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 
0x9D, 0x63, 0x63, 0x63, 0x63, 0x63, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x17, 0x4A, 0x4A, 0x4A, 0x2C, 0x00, 0x13, 0x4A, 
0x4A, 0x4A, 0x2C, 0x50, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0x9B, 0x50, 0xFF, 
0xFF, 0xFF, 0xB8, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0x9B, 0x50, 0xFF, 0xD4, 0xFF, 0xF2, 0x00, 0xA1, 
0xFF, 0xDF, 0xFF, 0x9B, 0x50, 0xFF, 0xB4, 0xF8, 0xFC, 0x02, 0xB2, 0xFF, 0xC1, 0xFF, 0x9B, 0x50, 
0xFF, 0xDD, 0xC7, 0xFF, 0x22, 0xE1, 0xFA, 0xA3, 0xFF, 0x9B, 0x50, 0xFF, 0xF0, 0xA1, 0xFF, 0x4C, 
0xFC, 0xD0, 0xAC, 0xFF, 0x9B, 0x50, 0xFF, 0xFA, 0x6A, 0xFF, 0x7F, 0xFF, 0xA7, 0xB4, 0xFF, 0x9B, 
0x50, 0xFF, 0xFF, 0x4C, 0xFF, 0xDD, 0xFF, 0x81, 0xC9, 0xFF, 0x9B, 0x50, 0xFF, 0xFF, 0x17, 0xFF, 
0xFF, 0xFF, 0x50, 0xEB, 0xFF, 0x9B, 0x50, 0xFF, 0xFF, 0x0C, 0xF2, 0xFF, 0xFF, 0x2A, 0xF0, 0xFF, 
0x9B, 0x50, 0xFF, 0xFF, 0x13, 0x70, 0x9B, 0x9B, 0x02, 0xF2, 0xFF, 0x9B, 0x50, 0xFF, 0xFF, 0x13, 
0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0x9B, 0x50, 0xFF, 0xFF, 0x13, 0x00, 0x00, 0x00, 0x00, 0xF2, 
0xFF, 0x9B, 0x50, 0xFF, 0xFF, 0x13, 0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0x9B, 0x11, 0x4A, 0x4A, 
0x4A, 0x35, 0x00, 0x00, 0x04, 0x4A, 0x4A, 0x1B, 0x3D, 0xFF, 0xFF, 0xFF, 0xF6, 0x02, 0x00, 0x0C, 
0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x0C, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 
0xFF, 0xE7, 0xFF, 0x88, 0x00, 0x0C, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0xB2, 0xFF, 0xCE, 0x00, 
0x0C, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x70, 0xFF, 0xFF, 0x17, 0x0C, 0xFF, 0xFF, 0x5D, 0x3D, 
0xFF, 0xFF, 0x50, 0xDD, 0xFF, 0x5B, 0x0C, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x55, 0x94, 0xFF, 
0xA7, 0x0C, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x57, 0x4E, 0xFF, 0xF2, 0x08, 0xFF, 0xFF, 0x5D, 
0x3D, 0xFF, 0xFF, 0x57, 0x04, 0xFA, 0xFF, 0x3B, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x57, 0x00, 
0xB4, 0xFF, 0x81, 0xFA, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x57, 0x00, 0x6E, 0xFF, 0xC5, 0xF4, 0xFF, 
0x5D, 0x3D, 0xFF, 0xFF, 0x57, 0x00, 0x28, 0xFF, 0xFC, 0xF8, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x57, 
0x00, 0x00, 0xDD, 0xFF, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x99, 0xFF, 0xFF, 
0xFF, 0x5D, 0x00, 0x00, 0x00, 0x17, 0x5B, 0x81, 0x5D, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 
0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x3B, 0xFF, 0xFF, 0xFF, 0xF4, 0xFF, 0xFF, 
0xFF, 0x7F, 0x00, 0x00, 0xC3, 0xFF, 0xFF, 0x5B, 0x00, 0x24, 0xE9, 0xFF, 0xF6, 0x0A, 0x15, 0xFF, 
0xFF, 0xC3, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0x52, 0x50, 0xFF, 0xFF, 0x88, 0x00, 0x00, 0x00, 
0x4A, 0xFF, 0xFF, 0x94, 0x5F, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0x13, 0xFF, 0xFF, 0xA5, 0x63, 
0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFF, 0xAE, 0x63, 0xFF, 0xFF, 0x57, 0x00, 0x00, 
0x00, 0x0C, 0xFF, 0xFF, 0xAC, 0x59, 0xFF, 0xFF, 0x5F, 0x00, 0x00, 0x00, 0x26, 0xFF, 0xFF, 0xA5, 
0x41, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0x7F, 0x02, 0xFA, 0xFF, 0xDF, 0x02, 
0x00, 0x00, 0xA3, 0xFF, 0xFF, 0x41, 0x00, 0x96, 0xFF, 0xFF, 0xAC, 0x4C, 0x7D, 0xFF, 0xFF, 0xDA, 
0x00, 0x00, 0x15, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x22, 0xB8, 
0xFF, 0xFF, 0xFF, 0xD6, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x33, 0x0C, 0x00, 0x00, 
0x00, 0x00, 0x2C, 0x4A, 0x4A, 0x4A, 0x4A, 0x48, 0x13, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xB8, 0x19, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x00, 
0x9B, 0xFF, 0xFF, 0x19, 0x0E, 0x46, 0xDA, 0xFF, 0xFF, 0x44, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 
0x59, 0xFF, 0xFF, 0x74, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x4C, 0xFF, 0xFF, 0x85, 0x9B, 0xFF, 
0xFF, 0x0C, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0x55, 0x9B, 0xFF, 0xFF, 0x99, 0x94, 0xB0, 0xFF, 0xFF, 
0xF6, 0x15, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x9B, 0xFF, 0xFF, 0xF2, 
0xF2, 0xE7, 0xAC, 0x41, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 
0xFF, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0x5B, 0x81, 0x5D, 0x2E, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x3B, 
0xFF, 0xFF, 0xFF, 0xF4, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0xC3, 0xFF, 0xFF, 0x5B, 0x00, 0x24, 
0xE9, 0xFF, 0xF6, 0x0A, 0x15, 0xFF, 0xFF, 0xC3, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0x52, 0x50, 
0xFF, 0xFF, 0x88, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0x94, 0x5F, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 
0x00, 0x13, 0xFF, 0xFF, 0xA5, 0x63, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFF, 0xAE, 
0x63, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xAE, 0x59, 0xFF, 0xFF, 0x5F, 0x00, 
0x00, 0x00, 0x26, 0xFF, 0xFF, 0xA3, 0x41, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 
0x7F, 0x02, 0xFA, 0xFF, 0xDF, 0x02, 0x00, 0x00, 0xA3, 0xFF, 0xFF, 0x44, 0x00, 0x96, 0xFF, 0xFF, 
0xAC, 0x4C, 0x7D, 0xFF, 0xFF, 0xDF, 0x00, 0x00, 0x15, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x46, 0x00, 0x00, 0x00, 0x22, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x06, 0x30, 0xD4, 0xFF, 0xF0, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 0xFF, 
0xFF, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xEB, 0xFF, 0xE9, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x5F, 0x9B, 0x9B, 0x35, 0x2C, 0x4A, 0x4A, 0x4A, 0x4A, 0x2C, 0x00, 
0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE9, 0x70, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x00, 0x9B, 0xFF, 0xFF, 0x4C, 0x1F, 0x6E, 0xFF, 0xFF, 0xEE, 
0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x0C, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0xAC, 0xFF, 0xFF, 0x0A, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x30, 0xF4, 0xFF, 0xE7, 0x00, 0x9B, 
0xFF, 0xFF, 0xEE, 0xEB, 0xFF, 0xFF, 0xFF, 0x68, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 
0x72, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xB6, 0xCE, 0xFF, 0xFA, 0x1F, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 
0x44, 0x28, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0xAC, 0xFF, 0xFC, 0x28, 
0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x2E, 0xFF, 0xFF, 0xAC, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0x2E, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x33, 0xFF, 0xFF, 0xBB, 0x00, 
0x00, 0x06, 0x4E, 0x61, 0x61, 0x50, 0x1D, 0x00, 0x00, 0x00, 0x4E, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xB6, 0x00, 0x26, 0xFA, 0xFF, 0xFF, 0xF8, 0xF4, 0xFF, 0xFF, 0x8E, 0x00, 0x7D, 0xFF, 0xFF, 
0x90, 0x02, 0x00, 0x17, 0x70, 0x26, 0x00, 0x9B, 0xFF, 0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x6E, 0xFF, 0xFF, 0x9D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xEB, 0xFF, 0xFF, 0xD4, 
0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xE5, 0xFF, 0xFF, 0xFF, 0xCE, 0x2E, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x8A, 0xFA, 0xFF, 0xFF, 0xFC, 0x4E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xB0, 0xFF, 
0xFF, 0xEB, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC5, 0xFF, 0xFF, 0x39, 0x11, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAA, 0xFF, 0xFF, 0x39, 0xB4, 0xBD, 0x66, 0x46, 0x46, 0x70, 0xF8, 0xFF, 0xEE, 
0x02, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x63, 0x00, 0x5F, 0xD0, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD0, 0x52, 0x00, 0x00, 0x00, 0x00, 0x04, 0x2C, 0x35, 0x06, 0x00, 0x00, 0x00, 0x00, 0x1B, 
0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x33, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAE, 
0x17, 0x3D, 0x3D, 0x3D, 0xC5, 0xFF, 0xFF, 0x3D, 0x3D, 0x3D, 0x2A, 0x00, 0x00, 0x00, 0x00, 0xB4, 
0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xB4, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 
0x00, 0x00, 0x00, 0x00, 0x11, 0x4A, 0x4A, 0x2C, 0x00, 0x00, 0x00, 0x17, 0x4A, 0x4A, 0x1B, 0x3D, 
0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 
0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 
0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 
0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 
0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 
0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x5D, 0x3D, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x50, 0xFF, 
0xFF, 0x5D, 0x33, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x59, 0xFF, 0xFF, 0x5D, 0x08, 0xFF, 0xFF, 
0xC9, 0x00, 0x00, 0x00, 0x88, 0xFF, 0xFF, 0x52, 0x00, 0xD4, 0xFF, 0xFF, 0x8C, 0x46, 0x66, 0xF2, 
0xFF, 0xFA, 0x17, 0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x81, 0x00, 0x00, 0x00, 
0x4E, 0xD6, 0xFF, 0xFF, 0xFF, 0xEB, 0x74, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x3D, 0x17, 
0x00, 0x00, 0x00, 0x00, 0x35, 0x4A, 0x4A, 0x02, 0x00, 0x00, 0x00, 0x00, 0x3B, 0x4A, 0x4A, 0x94, 
0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x02, 0xF8, 0xFF, 0xD4, 0x50, 0xFF, 0xFF, 0x72, 0x00, 0x00, 
0x00, 0x30, 0xFF, 0xFF, 0x92, 0x04, 0xFC, 0xFF, 0xAC, 0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0x4C, 
0x00, 0xBF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFA, 0x04, 0x00, 0x7D, 0xFF, 0xFF, 0x26, 
0x00, 0x00, 0xDF, 0xFF, 0xBF, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0x59, 0x00, 0x11, 0xFF, 0xFF, 0x7B, 
0x00, 0x00, 0x00, 0xF4, 0xFF, 0xA1, 0x00, 0x52, 0xFF, 0xFF, 0x37, 0x00, 0x00, 0x00, 0xA7, 0xFF, 
0xD6, 0x00, 0x8C, 0xFF, 0xF4, 0x00, 0x00, 0x00, 0x00, 0x63, 0xFF, 0xFF, 0x11, 0xB8, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x22, 0xFF, 0xFF, 0x4C, 0xF8, 0xFF, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xDA, 0xFF, 0x8C, 0xFF, 0xFF, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xF4, 0xFF, 0xDA, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0x96, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0A, 0xFC, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x35, 0x4A, 0x2E, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1B, 0x4A, 0x4A, 0xB2, 0xFF, 0xAC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xAA, 
0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x92, 0xFF, 0xF6, 0xA3, 0xFF, 0xE3, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x9D, 0xFF, 0xEE, 0x99, 0xFF, 0xF0, 0x00, 0xA5, 0xF2, 0xF0, 0x00, 0xA5, 0xFF, 0xD8, 
0x7F, 0xFF, 0xF8, 0x00, 0xDA, 0xFF, 0xFF, 0x19, 0xAC, 0xFF, 0xB4, 0x61, 0xFF, 0xFF, 0x02, 0xFC, 
0xFF, 0xFF, 0x4A, 0xB2, 0xFF, 0xAE, 0x59, 0xFF, 0xFF, 0x24, 0xFF, 0xD8, 0xFF, 0x5D, 0xB4, 0xFF, 
0xA5, 0x50, 0xFF, 0xFF, 0x55, 0xFF, 0x7B, 0xFF, 0x94, 0xB4, 0xFF, 0x9D, 0x4A, 0xFF, 0xFF, 0x63, 
0xFF, 0x52, 0xFF, 0xAE, 0xB4, 0xFF, 0x96, 0x41, 0xFF, 0xFF, 0xA1, 0xFF, 0x2C, 0xF2, 0xD4, 0xB4, 
0xFF, 0x72, 0x0E, 0xFF, 0xFF, 0xB0, 0xFF, 0x02, 0xC3, 0xF8, 0xB4, 0xFF, 0x5D, 0x08, 0xFF, 0xFF, 
0xD4, 0xF2, 0x00, 0xA7, 0xFF, 0xD2, 0xFF, 0x55, 0x00, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x8C, 0xFF, 
0xFF, 0xFF, 0x4C, 0x00, 0xF6, 0xFF, 0xFF, 0xA7, 0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0x48, 0x19, 0x4A, 
0x4A, 0x2A, 0x00, 0x00, 0x00, 0x19, 0x4A, 0x4A, 0x2C, 0x11, 0xF0, 0xFF, 0xD8, 0x00, 0x00, 0x00, 
0x9D, 0xFF, 0xFF, 0x39, 0x00, 0x7D, 0xFF, 0xFF, 0x4A, 0x00, 0x15, 0xFA, 0xFF, 0xBB, 0x00, 0x00, 
0x0C, 0xE9, 0xFF, 0xC1, 0x00, 0x7F, 0xFF, 0xFF, 0x30, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0x37, 
0xE9, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x06, 0xE5, 0xFF, 0xEE, 0xFF, 0xFF, 0x28, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xF6, 
0xFF, 0xFF, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0x7D, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xC9, 0xFF, 0xFF, 0xFF, 0xF2, 0x13, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 
0xFF, 0x6C, 0xFC, 0xFF, 0x88, 0x00, 0x00, 0x00, 0x00, 0xD4, 0xFF, 0xD4, 0x00, 0xA5, 0xFF, 0xFA, 
0x1B, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x61, 0x00, 0x2E, 0xFF, 0xFF, 0x94, 0x00, 0x02, 0xDD, 0xFF, 
0xE3, 0x02, 0x00, 0x00, 0xBD, 0xFF, 0xFC, 0x24, 0x6A, 0xFF, 0xFF, 0x72, 0x00, 0x00, 0x00, 0x3F, 
0xFF, 0xFF, 0xA7, 0x2E, 0x4A, 0x4A, 0x19, 0x00, 0x00, 0x00, 0x04, 0x4A, 0x4A, 0x44, 0x4E, 0xFF, 
0xFF, 0x9D, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0x8E, 0x00, 0xD8, 0xFF, 0xF8, 0x0A, 0x00, 0x00, 
0xBD, 0xFF, 0xFC, 0x22, 0x00, 0x6A, 0xFF, 0xFF, 0x66, 0x00, 0x1D, 0xFF, 0xFF, 0xAA, 0x00, 0x00, 
0x06, 0xEB, 0xFF, 0xC5, 0x00, 0x7B, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0x26, 
0xDA, 0xFF, 0xC5, 0x00, 0x00, 0x00, 0x00, 0x15, 0xFA, 0xFF, 0xC1, 0xFF, 0xFF, 0x52, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 0xDA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xFF, 
0xFF, 0xFF, 0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xAE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xAE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x13, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 
0x46, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xE9, 0x06, 0x13, 0x13, 0x13, 0x13, 0x66, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x00, 0x00, 
0x02, 0xDA, 0xFF, 0xE3, 0x06, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0x61, 0x00, 0x00, 0x00, 
0x00, 0x0C, 0xEB, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0x41, 0x00, 0x00, 
0x00, 0x00, 0x19, 0xF8, 0xFF, 0xB6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x2A, 0x00, 
0x00, 0x00, 0x00, 0x2A, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB6, 0xFF, 0xF6, 0x1B, 
0x00, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xC5, 0x63, 0x63, 0x63, 0x63, 0x63, 0x9B, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x6A, 
0x94, 0x94, 0x94, 0x8C, 0xB4, 0xFF, 0xFF, 0xFF, 0xF2, 0xB4, 0xFF, 0xC5, 0x63, 0x5F, 0xB4, 0xFF, 
0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 
0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 
0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 
0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 0xFF, 0xA1, 0x00, 0x00, 0xB4, 
0xFF, 0xF8, 0xEB, 0xDF, 0xB4, 0xFF, 0xFF, 0xFF, 0xF2, 0x08, 0x0C, 0x0C, 0x0C, 0x0C, 0x28, 0x57, 
0x4E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xD4, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 0xFF, 0xE1, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x17, 0xFC, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xA1, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFA, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0xEB, 0xFF, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0xFF, 0xC7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x2E, 0xFF, 0xFF, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD0, 0xFF, 0x88, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xE5, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xFC, 
0xFF, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x48, 0xFF, 0xFA, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x06, 0x02, 0x13, 0x13, 
0x13, 0x13, 0x0C, 0xF2, 0xFF, 0xFF, 0xFF, 0xB4, 0xD8, 0xE5, 0xF6, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 
0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 
0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 
0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 
0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 0xA1, 0xFF, 0xB4, 0x5F, 0x63, 
0xC5, 0xFF, 0xB4, 0xF2, 0xFF, 0xFF, 0xFF, 0xB4, 0x8C, 0x94, 0x94, 0x94, 0x6A, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x4A, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x90, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xF0, 0xFF, 0xFA, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x7B, 0xFF, 0xFF, 0xFF, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xE9, 0xFF, 0x6A, 0xFF, 
0xFA, 0x1D, 0x00, 0x00, 0x00, 0x00, 0x70, 0xFF, 0xD0, 0x00, 0xA5, 0xFF, 0x96, 0x00, 0x00, 0x00, 
0x04, 0xE3, 0xFF, 0x57, 0x00, 0x28, 0xFF, 0xFC, 0x1F, 0x00, 0x00, 0x6A, 0xFF, 0xDA, 0x00, 0x00, 
0x00, 0xAC, 0xFF, 0x99, 0x00, 0x02, 0xDF, 0xFF, 0x6A, 0x00, 0x00, 0x00, 0x2E, 0xFF, 0xFC, 0x1F, 
0x2A, 0x94, 0x90, 0x02, 0x00, 0x00, 0x00, 0x00, 0x77, 0x94, 0x4E, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 
0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x2E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x4A, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0xB4, 0x35, 0x1F, 
0x50, 0x50, 0x35, 0x00, 0x00, 0x11, 0xCE, 0xFF, 0xEE, 0x13, 0x00, 0x00, 0x13, 0xD0, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x11, 0xBB, 0xEB, 0x3B, 0x00, 0x00, 0x00, 0x04, 0x0C, 0x04, 0x00, 0x00, 0x00, 
0x00, 0x1B, 0x8C, 0xDF, 0xFF, 0xFF, 0xFF, 0xDD, 0x6C, 0x00, 0x00, 0x1D, 0xFC, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x83, 0x7D, 0x4A, 0x44, 0x63, 0xF2, 0xFF, 0xEB, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xA1, 0xFF, 0xFF, 0x04, 0x00, 0x19, 0x74, 0xA5, 0xAE, 0xB4, 0xE1, 
0xFF, 0xFF, 0x06, 0x39, 0xF2, 0xFF, 0xFF, 0xFF, 0xF8, 0xFC, 0xFF, 0xFF, 0x06, 0xC9, 0xFF, 0xFC, 
0x55, 0x02, 0x00, 0x9B, 0xFF, 0xFF, 0x06, 0xFC, 0xFF, 0xBD, 0x00, 0x00, 0x00, 0xB0, 0xFF, 0xFF, 
0x06, 0xF8, 0xFF, 0xF6, 0x3B, 0x13, 0x72, 0xFF, 0xFF, 0xFF, 0x06, 0xA1, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD8, 0xFF, 0xFF, 0x06, 0x15, 0xC1, 0xFF, 0xFF, 0xF8, 0x7B, 0x24, 0xFF, 0xFF, 0x06, 0x00, 
0x00, 0x15, 0x35, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x44, 0x30, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x0A, 0x06, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xAC, 0x4C, 0xE1, 
0xFF, 0xFC, 0xAA, 0x08, 0x00, 0xF8, 0xFF, 0xDD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9D, 0x00, 0xF8, 
0xFF, 0xFF, 0xBD, 0x4E, 0x70, 0xFC, 0xFF, 0xFC, 0x19, 0xF8, 0xFF, 0xFA, 0x0E, 0x00, 0x00, 0x96, 
0xFF, 0xFF, 0x55, 0xF8, 0xFF, 0xC9, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x7D, 0xF8, 0xFF, 0xB4, 
0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x94, 0xF8, 0xFF, 0xC9, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 
0x7F, 0xF8, 0xFF, 0xFA, 0x0E, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0x55, 0xF8, 0xFF, 0xFF, 0xB0, 0x41, 
0x63, 0xFA, 0xFF, 0xFC, 0x17, 0xF8, 0xFF, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9F, 0x00, 0xF8, 
0xFF, 0x48, 0x77, 0xF4, 0xFF, 0xFF, 0xB4, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x30, 0x15, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x0C, 0x04, 0x00, 0x00, 0x00, 0x00, 0x59, 0xD0, 
0xFF, 0xFF, 0xFF, 0xE3, 0x7F, 0x00, 0x77, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x8C, 0x1F, 0xFC, 
0xFF, 0xFF, 0x85, 0x4C, 0x57, 0x92, 0x2A, 0x70, 0xFF, 0xFF, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA5, 0xFF, 0xFF, 0x3D, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x0C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xAA, 0xFF, 0xFF, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0x79, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x39, 0xFF, 0xFF, 0xFA, 0x7B, 0x46, 0x4A, 0x85, 0x8C, 0x00, 0xA5, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x9B, 0x00, 0x04, 0x81, 0xF0, 0xFF, 0xFF, 0xFF, 0xE3, 0x5B, 0x00, 
0x00, 0x00, 0x00, 0x19, 0x3D, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 
0x44, 0x44, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0x44, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x6A, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x02, 0x00, 0x68, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0x68, 0xEE, 0xFF, 0xFA, 0x99, 0x66, 0xFF, 0xFF, 0x44, 0x00, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xE5, 0xFF, 0xFF, 0x44, 0x00, 0xC9, 0xFF, 0xFF, 0xAA, 0x4C, 0x6E, 0xF8, 0xFF, 0xFF, 0x44, 
0x08, 0xFF, 0xFF, 0xE1, 0x00, 0x00, 0x00, 0x8E, 0xFF, 0xFF, 0x44, 0x46, 0xFF, 0xFF, 0xA5, 0x00, 
0x00, 0x00, 0x52, 0xFF, 0xFF, 0x44, 0x4A, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 
0x44, 0x48, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x44, 0x11, 0xFF, 0xFF, 0xD2, 
0x00, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0x44, 0x00, 0xD8, 0xFF, 0xFF, 0x8A, 0x3F, 0x5D, 0xF2, 0xFF, 
0xFF, 0x44, 0x00, 0x6A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x8C, 
0xFC, 0xFF, 0xFC, 0x8C, 0x0C, 0xF4, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x08, 0x3B, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x15, 0x9B, 0xF8, 0xFF, 0xFF, 0xDA, 0x59, 0x00, 0x00, 0x00, 0x0C, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0x68, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0x9F, 0x22, 0x41, 0xE3, 0xFF, 0xEE, 0x02, 0x00, 
0xE3, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0x44, 0x06, 0xFF, 0xFF, 0xE1, 0x9B, 0x9B, 
0x9B, 0xB8, 0xFF, 0xFF, 0x55, 0x0C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x57, 
0x06, 0xFF, 0xFF, 0xC7, 0x57, 0x57, 0x57, 0x57, 0x57, 0x57, 0x1D, 0x00, 0xDD, 0xFF, 0xEB, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0xD2, 0x57, 0x2E, 0x3F, 0x5D, 0xB0, 
0x00, 0x00, 0x06, 0xC7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x0A, 0x88, 
0xF0, 0xFF, 0xFF, 0xFF, 0xF4, 0x9B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x3D, 0x15, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2C, 0x4A, 0x48, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x1F, 
0xD6, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0xBD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x55, 
0x00, 0x00, 0x02, 0xFC, 0xFF, 0xF0, 0x4A, 0x1B, 0x4C, 0x06, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xAA, 
0x00, 0x00, 0x00, 0x00, 0x4A, 0x96, 0xCC, 0xFF, 0xFF, 0xFA, 0xF2, 0xF2, 0xF2, 0x00, 0xE5, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x55, 0x5D, 0x61, 0xFF, 0xFF, 0xC7, 0x5D, 0x5D, 
0x5D, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 
0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xA7, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x0C, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 
0xEE, 0xFF, 0xF8, 0x79, 0x24, 0xF2, 0xF2, 0x3F, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 
0xFF, 0xFF, 0x44, 0x00, 0xC9, 0xFF, 0xFF, 0xA3, 0x4C, 0x79, 0xFF, 0xFF, 0xFF, 0x44, 0x08, 0xFF, 
0xFF, 0xDF, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x44, 0x46, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 
0x70, 0xFF, 0xFF, 0x44, 0x4A, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x44, 0x48, 
0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x61, 0xFF, 0xFF, 0x44, 0x0E, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0x44, 0x00, 0xD4, 0xFF, 0xFF, 0x94, 0x3F, 0x66, 0xFC, 0xFF, 0xFF, 0x44, 
0x00, 0x59, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x77, 0xFA, 0xFF, 
0xFC, 0x90, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x04, 0x37, 0x08, 0x00, 0xA1, 0xFF, 0xFF, 
0x41, 0x00, 0x2A, 0x02, 0x00, 0x00, 0x00, 0x0A, 0xDF, 0xFF, 0xFC, 0x08, 0x00, 0x9B, 0xF4, 0xAE, 
0x9D, 0x9F, 0xDD, 0xFF, 0xFF, 0xB6, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 
0x1D, 0x00, 0x00, 0x33, 0x9B, 0xC1, 0xEB, 0xE9, 0xB8, 0x7D, 0x13, 0x00, 0x00, 0x41, 0x44, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x08, 0x06, 0x00, 0x00, 0x00, 0xF8, 
0xFF, 0xB2, 0x48, 0xE3, 0xFF, 0xFF, 0xC5, 0x1D, 0x00, 0xF8, 0xFF, 0xDD, 0xF6, 0xFF, 0xFF, 0xFF, 
0xFF, 0xD4, 0x00, 0xF8, 0xFF, 0xFF, 0xC3, 0x4E, 0x61, 0xEE, 0xFF, 0xFF, 0x33, 0xF8, 0xFF, 0xFF, 
0x1D, 0x00, 0x00, 0x8C, 0xFF, 0xFF, 0x4E, 0xF8, 0xFF, 0xE7, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 
0x50, 0xF8, 0xFF, 0xB6, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 
0x00, 0x5D, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0xF8, 
0xFF, 0xB4, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x5D, 
0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 
0x13, 0x5F, 0x59, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA5, 0xFF, 0xFF, 0x72, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B, 0xB0, 
0xA7, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 
0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0x46, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4A, 
0x00, 0x00, 0x00, 0x04, 0x28, 0x4A, 0x9B, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x6A, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 
0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0x4A, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x26, 0x94, 0xFF, 0xFF, 0x7D, 0x0E, 0x00, 0x00, 0xD2, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xF8, 0x3B, 0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x37, 0x63, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xFC, 0xFF, 0xFA, 0x0E, 0x00, 
0x00, 0x00, 0x00, 0x28, 0xFF, 0xFF, 0xFF, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xB4, 0x7D, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAC, 0xF2, 0xF2, 0xF2, 
0xF2, 0xF2, 0x06, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x08, 0x39, 
0x4C, 0xC5, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 
0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x08, 0xE7, 0xFF, 0xFC, 0x02, 0x3F, 
0xB6, 0x9D, 0x9D, 0xD4, 0xFF, 0xFF, 0xCE, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x46, 
0x00, 0x1D, 0xA1, 0xCC, 0xEB, 0xDF, 0xA7, 0x35, 0x00, 0x00, 0x3F, 0x44, 0x3D, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 
0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xF2, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0xEB, 0x00, 
0x00, 0x00, 0xB8, 0xF2, 0xF2, 0x63, 0xF2, 0xFF, 0xEB, 0x00, 0x00, 0x83, 0xFF, 0xFF, 0x9F, 0x00, 
0xF2, 0xFF, 0xEB, 0x00, 0x4C, 0xFF, 0xFF, 0xC9, 0x08, 0x00, 0xF2, 0xFF, 0xE3, 0x1D, 0xEE, 0xFF, 
0xE9, 0x17, 0x00, 0x00, 0xF2, 0xFF, 0xB4, 0xC7, 0xFF, 0xFF, 0x5B, 0x00, 0x00, 0x00, 0xF2, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0xF2, 0xFF, 0xFF, 0xF2, 0xC3, 0xFF, 0xFF, 0x4E, 
0x00, 0x00, 0xF2, 0xFF, 0xF8, 0x30, 0x19, 0xF4, 0xFF, 0xDF, 0x08, 0x00, 0xF2, 0xFF, 0xEB, 0x00, 
0x00, 0x7D, 0xFF, 0xFF, 0x7F, 0x00, 0xF2, 0xFF, 0xEB, 0x00, 0x00, 0x06, 0xDF, 0xFF, 0xF8, 0x22, 
0xF2, 0xFF, 0xEB, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xB0, 0x11, 0x44, 0x44, 0x44, 0x44, 0x44, 
0x11, 0x00, 0x00, 0x00, 0x44, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x2C, 0xB6, 
0xF4, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 
0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 
0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x02, 0x28, 0xB8, 
0xFF, 0xFF, 0x79, 0x0A, 0x00, 0x00, 0xD2, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x3B, 
0xE5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x08, 0x02, 
0x00, 0x00, 0x06, 0x02, 0x00, 0x8C, 0xF2, 0x7B, 0x8E, 0xFF, 0xF2, 0x2C, 0x99, 0xFF, 0xF4, 0x35, 
0x94, 0xFF, 0xDD, 0xFF, 0xFF, 0xFF, 0xD4, 0xFF, 0xFF, 0xFF, 0x9D, 0x94, 0xFF, 0xFF, 0x52, 0xC1, 
0xFF, 0xFC, 0x4C, 0xC9, 0xFF, 0xB2, 0x94, 0xFF, 0xF2, 0x00, 0x9B, 0xFF, 0xEB, 0x00, 0xA1, 0xFF, 
0xB4, 0x94, 0xFF, 0xF2, 0x00, 0x9B, 0xFF, 0xEB, 0x00, 0xA1, 0xFF, 0xB4, 0x94, 0xFF, 0xF2, 0x00, 
0x9B, 0xFF, 0xEB, 0x00, 0xA1, 0xFF, 0xB4, 0x94, 0xFF, 0xF2, 0x00, 0x9B, 0xFF, 0xEB, 0x00, 0xA1, 
0xFF, 0xB4, 0x94, 0xFF, 0xF2, 0x00, 0x9B, 0xFF, 0xEB, 0x00, 0xA1, 0xFF, 0xB4, 0x94, 0xFF, 0xF2, 
0x00, 0x9B, 0xFF, 0xEB, 0x00, 0xA1, 0xFF, 0xB4, 0x94, 0xFF, 0xF2, 0x00, 0x9B, 0xFF, 0xEB, 0x00, 
0xA1, 0xFF, 0xB4, 0x94, 0xFF, 0xF2, 0x00, 0x9B, 0xFF, 0xEB, 0x00, 0xA1, 0xFF, 0xB4, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0A, 0x06, 0x00, 0x00, 0x00, 0xEB, 0xF2, 0x4C, 0x5D, 0xE7, 0xFF, 0xFF, 0xC9, 
0x26, 0x00, 0xF8, 0xFF, 0xDA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD8, 0x00, 0xF8, 0xFF, 0xFF, 0xC1, 
0x4A, 0x5D, 0xEE, 0xFF, 0xFF, 0x37, 0xF8, 0xFF, 0xFF, 0x1D, 0x00, 0x00, 0x8C, 0xFF, 0xFF, 0x50, 
0xF8, 0xFF, 0xE3, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 
0x5D, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 
0xB4, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x5D, 0xFF, 
0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0xF8, 0xFF, 0xB4, 0x00, 
0x00, 0x00, 0x5D, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x02, 0x0C, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x19, 0xA7, 0xFA, 0xFF, 0xFF, 0xCC, 0x41, 0x00, 0x00, 0x00, 0x17, 0xE5, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x9D, 0xFF, 0xFF, 0x9D, 0x46, 0x66, 0xF8, 0xFF, 
0xE7, 0x02, 0x04, 0xFA, 0xFF, 0xDF, 0x00, 0x00, 0x00, 0x90, 0xFF, 0xFF, 0x4E, 0x39, 0xFF, 0xFF, 
0xA1, 0x00, 0x00, 0x00, 0x55, 0xFF, 0xFF, 0x70, 0x4A, 0xFF, 0xFF, 0x9B, 0x00, 0x00, 0x00, 0x4A, 
0xFF, 0xFF, 0x94, 0x3F, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0x77, 0x04, 0xFC, 
0xFF, 0xD2, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFF, 0x4E, 0x00, 0xAA, 0xFF, 0xFF, 0x8C, 0x1F, 0x59, 
0xF2, 0xFF, 0xEB, 0x04, 0x00, 0x1F, 0xE9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x5D, 0x00, 0x00, 
0x00, 0x24, 0xBF, 0xFF, 0xFF, 0xFF, 0xE3, 0x5D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x3B, 
0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x06, 0x00, 0x00, 0x00, 0xEB, 
0xF2, 0x41, 0x4A, 0xDF, 0xFF, 0xFC, 0xAA, 0x08, 0x00, 0xF8, 0xFF, 0xD0, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x9D, 0x00, 0xF8, 0xFF, 0xFF, 0xBD, 0x4E, 0x68, 0xF8, 0xFF, 0xFC, 0x19, 0xF8, 0xFF, 0xFA, 
0x0C, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0x55, 0xF8, 0xFF, 0xBF, 0x00, 0x00, 0x00, 0x5D, 0xFF, 0xFF, 
0x7D, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x94, 0xF8, 0xFF, 0xCC, 0x00, 0x00, 
0x00, 0x5D, 0xFF, 0xFF, 0x7D, 0xF8, 0xFF, 0xFA, 0x0E, 0x00, 0x00, 0x99, 0xFF, 0xFF, 0x55, 0xF8, 
0xFF, 0xFF, 0xB2, 0x44, 0x5D, 0xF6, 0xFF, 0xFC, 0x17, 0xF8, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x96, 0x00, 0xF8, 0xFF, 0xAE, 0x77, 0xF4, 0xFF, 0xFF, 0xAA, 0x08, 0x00, 0xF8, 0xFF, 0xB4, 
0x00, 0x02, 0x35, 0x11, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xB4, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0xB4, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x0C, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0xF4, 0xFF, 0xFA, 
0x8C, 0x08, 0xF0, 0xF2, 0x3F, 0x00, 0x57, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xDA, 0xFF, 0xFF, 0x44, 
0x00, 0xCE, 0xFF, 0xFF, 0xAE, 0x4C, 0x77, 0xFF, 0xFF, 0xFF, 0x44, 0x08, 0xFF, 0xFF, 0xE9, 0x04, 
0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x44, 0x46, 0xFF, 0xFF, 0xAC, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 
0x44, 0x4A, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x50, 0xFF, 0xFF, 0x44, 0x48, 0xFF, 0xFF, 0xA7, 
0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0x44, 0x11, 0xFF, 0xFF, 0xDD, 0x00, 0x00, 0x00, 0x8E, 0xFF, 
0xFF, 0x44, 0x00, 0xD8, 0xFF, 0xFF, 0x90, 0x3F, 0x5D, 0xF2, 0xFF, 0xFF, 0x44, 0x00, 0x68, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xDF, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x8C, 0xFC, 0xFF, 0xFC, 0x90, 0x61, 
0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x08, 0x3B, 0x04, 0x00, 0x6A, 0xFF, 0xFF, 0x44, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x6A, 0xFF, 0xFF, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xFF, 0xFF, 0x44, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4A, 0xB4, 0xB4, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x0A, 0x00, 0x00, 0xDF, 0xF2, 0xF2, 0xF2, 0x79, 0x15, 0xBF, 0xFF, 0xFF, 0xD8, 
0x00, 0xEB, 0xFF, 0xFF, 0xFF, 0xB4, 0xBB, 0xFF, 0xFF, 0xFF, 0xA7, 0x00, 0x15, 0x4A, 0xC5, 0xFF, 
0xFF, 0xFF, 0xD6, 0x9D, 0xA5, 0x48, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0xBB, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 
0xFF, 0xFF, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 
0xC1, 0xFF, 0xFF, 0x41, 0x06, 0x00, 0x00, 0x00, 0x44, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xAC, 0x00, 0x00, 0x4A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x0A, 0x08, 0x00, 0x00, 0x00, 0x00, 0x33, 0xBF, 0xFC, 0xFF, 0xFF, 0xF6, 0xB2, 0x46, 
0x1F, 0xF2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x66, 0x5F, 0xFF, 0xFF, 0x9D, 0x46, 0x48, 0x68, 
0xB4, 0x08, 0x63, 0xFF, 0xFF, 0x9D, 0x08, 0x00, 0x00, 0x00, 0x00, 0x26, 0xF6, 0xFF, 0xFF, 0xF0, 
0x8C, 0x22, 0x00, 0x00, 0x00, 0x37, 0xD4, 0xFF, 0xFF, 0xFF, 0xFC, 0x8C, 0x00, 0x00, 0x00, 0x00, 
0x4C, 0xBD, 0xFF, 0xFF, 0xFF, 0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xFF, 0xFF, 0xB2, 0x8A, 
0x9D, 0x52, 0x35, 0x19, 0x61, 0xFF, 0xFF, 0xB2, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0x66, 0x5F, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x6A, 0x00, 0x00, 0x00, 0x06, 0x33, 0x39, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9D, 0xA1, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x22, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x57, 0xA1, 0xE9, 0xFF, 0xFF, 0xF2, 0xF2, 0xF2, 0xF2, 0x06, 0x06, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x06, 0x02, 0x5D, 0x5D, 0xCC, 0xFF, 0xFF, 
0x5D, 0x5D, 0x5D, 0x5D, 0x02, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 
0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xAE, 0xFF, 0xFF, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9D, 
0xFF, 0xFF, 0x9F, 0x46, 0x46, 0x5D, 0x0C, 0x00, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0x13, 0x00, 0x00, 0x00, 0x00, 0x6A, 0xEE, 0xFF, 0xFF, 0xFF, 0xE9, 0x0C, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1F, 0x3D, 0x0A, 0x00, 0x00, 0x06, 0xF2, 0xF2, 0xA5, 0x00, 0x00, 0x00, 0x8C, 
0xF2, 0xF2, 0x3F, 0x06, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x06, 0xFF, 
0xFF, 0xAE, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x06, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x94, 0xFF, 0xFF, 0x44, 0x06, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x94, 0xFF, 0xFF, 0x44, 0x06, 
0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x96, 0xFF, 0xFF, 0x44, 0x06, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 
0x00, 0xA1, 0xFF, 0xFF, 0x44, 0x04, 0xFF, 0xFF, 0xC9, 0x00, 0x00, 0x00, 0xCC, 0xFF, 0xFF, 0x44, 
0x00, 0xF6, 0xFF, 0xFF, 0x6C, 0x41, 0x81, 0xFF, 0xFF, 0xFF, 0x44, 0x00, 0x96, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xD8, 0xFF, 0xFF, 0x44, 0x00, 0x11, 0xB8, 0xFF, 0xFF, 0xFC, 0x92, 0x0E, 0xFC, 0xFF, 
0x44, 0x00, 0x00, 0x00, 0x13, 0x37, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7D, 0xF2, 0xF2, 0x61, 
0x00, 0x00, 0x00, 0x19, 0xF2, 0xF2, 0xBB, 0x2E, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 0x63, 0xFF, 
0xFF, 0x6E, 0x00, 0xD8, 0xFF, 0xF8, 0x02, 0x00, 0x00, 0xAC, 0xFF, 0xFC, 0x17, 0x00, 0x7F, 0xFF, 
0xFF, 0x46, 0x00, 0x02, 0xF8, 0xFF, 0xBD, 0x00, 0x00, 0x28, 0xFF, 0xFF, 0x90, 0x00, 0x44, 0xFF, 
0xFF, 0x61, 0x00, 0x00, 0x00, 0xD2, 0xFF, 0xDA, 0x00, 0x8E, 0xFF, 0xFA, 0x0C, 0x00, 0x00, 0x00, 
0x7B, 0xFF, 0xFF, 0x24, 0xD8, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x24, 0xFF, 0xFF, 0x70, 0xFF, 
0xFF, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0xCC, 0xFF, 0xEE, 0xFF, 0xF8, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x74, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1D, 0xFF, 0xFF, 
0xFF, 0x4A, 0x00, 0x00, 0x00, 0xE3, 0xF2, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0xF2, 0xF2, 
0x28, 0xB4, 0xFF, 0xA3, 0x00, 0x2C, 0x4A, 0x44, 0x00, 0x52, 0xFF, 0xFC, 0x02, 0xA1, 0xFF, 0xAE, 
0x00, 0xAE, 0xFF, 0xF8, 0x00, 0x5D, 0xFF, 0xEB, 0x00, 0x7B, 0xFF, 0xC3, 0x00, 0xE7, 0xFF, 0xFF, 
0x2A, 0x7F, 0xFF, 0xB2, 0x00, 0x57, 0xFF, 0xEE, 0x08, 0xFF, 0xF8, 0xFF, 0x55, 0x9F, 0xFF, 0xA1, 
0x00, 0x3F, 0xFF, 0xFA, 0x4A, 0xFF, 0xB2, 0xFF, 0x8C, 0xAA, 0xFF, 0x74, 0x00, 0x06, 0xFF, 0xFF, 
0x6C, 0xFF, 0x57, 0xFF, 0xAE, 0xB2, 0xFF, 0x50, 0x00, 0x00, 0xF8, 0xFF, 0xB2, 0xFF, 0x1F, 0xF0, 
0xE3, 0xC1, 0xFF, 0x3D, 0x00, 0x00, 0xCC, 0xFF, 0xF8, 0xF8, 0x00, 0xB0, 0xFC, 0xF8, 0xFF, 0x06, 
0x00, 0x00, 0xAA, 0xFF, 0xFF, 0xC3, 0x00, 0x94, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x94, 0xFF, 
0xFF, 0xA1, 0x00, 0x5B, 0xFF, 0xFF, 0xC1, 0x00, 0x00, 0x30, 0xF2, 0xF2, 0xE5, 0x0E, 0x00, 0x00, 
0x8E, 0xF2, 0xF2, 0x88, 0x00, 0x94, 0xFF, 0xFF, 0x83, 0x00, 0x28, 0xFC, 0xFF, 0xE5, 0x0E, 0x00, 
0x0C, 0xE5, 0xFF, 0xF4, 0x17, 0xB2, 0xFF, 0xFF, 0x4E, 0x00, 0x00, 0x00, 0x52, 0xFF, 0xFF, 0xC1, 
0xFF, 0xFF, 0xA7, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB2, 0xFF, 0xFF, 0xFF, 0xEE, 0x17, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0xFF, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC9, 0xFF, 
0xFF, 0xFF, 0xF4, 0x22, 0x00, 0x00, 0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xB6, 0xFF, 0xFF, 0xB6, 0x00, 
0x00, 0x00, 0x1D, 0xF2, 0xFF, 0xEE, 0x0E, 0xB0, 0xFF, 0xFF, 0x59, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 
0x72, 0x00, 0x26, 0xFC, 0xFF, 0xE9, 0x11, 0x57, 0xFF, 0xFF, 0xDA, 0x02, 0x00, 0x00, 0x94, 0xFF, 
0xFF, 0x9B, 0x7D, 0xF2, 0xF2, 0x81, 0x00, 0x00, 0x00, 0x30, 0xF2, 0xF2, 0xBB, 0x24, 0xFF, 0xFF, 
0xD8, 0x00, 0x00, 0x00, 0x72, 0xFF, 0xFF, 0x72, 0x00, 0xC3, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0xB6, 
0xFF, 0xFF, 0x1B, 0x00, 0x63, 0xFF, 0xFF, 0x74, 0x00, 0x04, 0xFA, 0xFF, 0xC3, 0x00, 0x00, 0x0A, 
0xFA, 0xFF, 0xC1, 0x00, 0x44, 0xFF, 0xFF, 0x6E, 0x00, 0x00, 0x00, 0x9F, 0xFF, 0xFC, 0x13, 0x85, 
0xFF, 0xFF, 0x1B, 0x00, 0x00, 0x00, 0x41, 0xFF, 0xFF, 0x59, 0xC7, 0xFF, 0xC1, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xE1, 0xFF, 0xA3, 0xFC, 0xFF, 0x6A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, 0xEE, 
0xFF, 0xFC, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xFF, 0xBF, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0x68, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xBD, 
0xFF, 0xFC, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0xFF, 0xFF, 0xAE, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x88, 0x94, 0xEE, 0xFF, 0xFF, 0x2E, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 
0xFF, 0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC7, 0xEB, 0xBB, 0x57, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x8C, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xD8, 0x94, 0xFF, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xE5, 0x33, 0x57, 0x57, 0x57, 0x57, 0xDA, 0xFF, 0xFF, 0x72, 0x00, 0x00, 0x00, 
0x00, 0x63, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x2E, 0xF6, 0xFF, 0xDD, 0x0E, 0x00, 0x00, 
0x00, 0x0C, 0xDA, 0xFF, 0xF8, 0x2E, 0x00, 0x00, 0x00, 0x00, 0xA7, 0xFF, 0xFF, 0x66, 0x00, 0x00, 
0x00, 0x00, 0x6E, 0xFF, 0xFF, 0xA1, 0x00, 0x00, 0x00, 0x00, 0x33, 0xFA, 0xFF, 0xF0, 0x52, 0x4A, 
0x4A, 0x4A, 0x4A, 0xB2, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB4, 0xFF, 0xFF, 0xFF, 
0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x15, 0x4C, 0x3D, 0x00, 0x00, 0x00, 0x8C, 
0xFC, 0xFF, 0xBB, 0x00, 0x00, 0x33, 0xFF, 0xFF, 0xF2, 0x81, 0x00, 0x00, 0x55, 0xFF, 0xFF, 0x26, 
0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x06, 0x00, 0x00, 
0x00, 0x57, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x8A, 0xFF, 0xFC, 0x00, 0x00, 0x5F, 0xBB, 0xFF, 
0xFF, 0x9D, 0x00, 0x00, 0x9B, 0xFF, 0xFF, 0x9F, 0x06, 0x00, 0x00, 0x55, 0xA7, 0xFF, 0xFF, 0xB8, 
0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, 0xFC, 0x02, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x06, 0x00, 
0x00, 0x00, 0x57, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 0x57, 0xFF, 0xFF, 0x06, 0x00, 0x00, 0x00, 
0x55, 0xFF, 0xFF, 0x3B, 0x00, 0x00, 0x00, 0x28, 0xFF, 0xFF, 0xFC, 0xA1, 0x00, 0x00, 0x00, 0x72, 
0xF6, 0xFF, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x02, 0x30, 0x35, 0x1B, 0x44, 0x2E, 0x63, 0xFF, 0xAE, 
0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 
0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 
0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 
0x63, 0xFF, 0xAE, 0x63, 0xFF, 0xAE, 0x46, 0xB4, 0x7B, 0x37, 0x35, 0x02, 0x00, 0x00, 0x00, 0x00, 
0xBB, 0xFF, 0xF8, 0x77, 0x00, 0x00, 0x00, 0x9D, 0xFC, 0xFF, 0xFF, 0x28, 0x00, 0x00, 0x00, 0x3B, 
0xFF, 0xFF, 0x55, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 
0x57, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0x83, 0x00, 
0x00, 0x00, 0x00, 0xB4, 0xFF, 0xFF, 0xA7, 0x59, 0x00, 0x00, 0x08, 0x9D, 0xFF, 0xFF, 0x9B, 0x00, 
0x00, 0x9D, 0xFF, 0xFF, 0xBB, 0x5F, 0x00, 0x02, 0xFC, 0xFF, 0x8E, 0x00, 0x00, 0x00, 0x06, 0xFF, 
0xFF, 0x57, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x57, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x57, 
0x00, 0x00, 0x00, 0x26, 0xFF, 0xFF, 0x55, 0x00, 0x00, 0x81, 0xF4, 0xFF, 0xFF, 0x30, 0x00, 0x00, 
0xBB, 0xFF, 0xFC, 0x8C, 0x00, 0x00, 0x00, 0x3D, 0x4A, 0x13, 0x00, 0x00, 0x00, 0x00, 0x08, 0x7B, 
0xA7, 0x8E, 0x37, 0x00, 0x00, 0x00, 0x3D, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xD0, 0x99, 0xA5, 0xF6, 
0xAE, 0xA3, 0x5F, 0xA1, 0xF6, 0xFF, 0xFF, 0xFF, 0xDA, 0x24, 0x00, 0x00, 0x00, 0x0C, 0x5B, 0x96, 
0x6E, 0x11, 0x00, 0x21, 0x4E, 0x6F, 0x74, 0x6F, 0x20, 0x53, 0x61, 0x6E, 0x73, 0x20, 0x4D, 0x6F, 
0x6E, 0x6F, 0x20, 0x53, 0x65, 0x6D, 0x69, 0x43, 0x6F, 0x6E, 0x64, 0x65, 0x6E, 0x73, 0x65, 0x64, 
0x20, 0x42, 0x6F, 0x6C, 0x64, 0x00, 0x1E, 0x4E, 0x6F, 0x74, 0x6F, 0x53, 0x61, 0x6E, 0x73, 0x4D, 
0x6F, 0x6E, 0x6F, 0x2D, 0x53, 0x65, 0x6D, 0x69, 0x43, 0x6F, 0x6E, 0x64, 0x65, 0x6E, 0x73, 0x65, 
0x64, 0x42, 0x6F, 0x6C, 0x64, 0x01,
};
