// Created by http://oleddisplay.squix.ch/ Consider a donation
// In case of problems make sure that you are using the font file with the correct version!
const uint8_t Orbitron_Light_32Bitmaps[] PROGMEM = {

	// Bitmap Data:
	0x00, // ' '
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE0,0x00,0x3F, // '!'
	0xE7,0xE7,0xE7,0xE7, // '"'
	0x00,0x70,0x0E,0x00,0x60,0x0C,0x00,0xE0,0x1C,0x00,0xE0,0x1C,0x00,0xC0,0x18,0x01,0xC0,0x38,0x7F,0xFF,0xFF,0x7F,0xFF,0xFF,0x03,0x80,0x70,0x03,0x80,0x70,0x03,0x00,0x60,0x07,0x00,0xE0,0x07,0x00,0xE0,0x06,0x00,0xE0,0x06,0x00,0xC0,0x0E,0x01,0xC0,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0x1C,0x03,0x80,0x18,0x03,0x80,0x38,0x03,0x00,0x38,0x07,0x00,0x38,0x07,0x00,0x30,0x06,0x00, // '#'
	0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x01,0xFF,0xFF,0xC7,0xFF,0xFF,0xDC,0x07,0x01,0xF8,0x0E,0x03,0xF0,0x1C,0x07,0xE0,0x38,0x01,0xC0,0x70,0x03,0x80,0xE0,0x07,0x01,0xC0,0x0E,0x03,0x80,0x1C,0x07,0x00,0x1F,0xFF,0xFE,0x1F,0xFF,0xFE,0x00,0x38,0x0E,0x00,0x70,0x1C,0x00,0xE0,0x38,0x01,0xC0,0x70,0x03,0x80,0xE0,0x07,0x01,0xF8,0x0E,0x03,0xF0,0x1C,0x07,0xE0,0x38,0x0E,0xFF,0xFF,0xF8,0xFF,0xFF,0xE0,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00, // '$'
	0x00,0x00,0x00,0x03,0xF8,0x00,0x04,0x7F,0xC0,0x00,0xCC,0x06,0x00,0x1C,0xC0,0x60,0x03,0xCC,0x06,0x00,0x78,0xC0,0x60,0x0F,0x0C,0x06,0x03,0xC0,0xC0,0x60,0x78,0x0C,0x06,0x0F,0x00,0x7F,0xC1,0xE0,0x03,0xF8,0x3C,0x00,0x00,0x07,0x80,0x00,0x00,0xF0,0x00,0x00,0x1E,0x1F,0xC0,0x07,0xC3,0xFE,0x00,0xF0,0x60,0x30,0x1E,0x06,0x03,0x03,0xC0,0x60,0x30,0x78,0x06,0x03,0x0F,0x00,0x60,0x31,0xE0,0x06,0x03,0x1C,0x00,0x60,0x31,0x80,0x03,0xFE,0x00,0x00,0x1F,0xC0, // '%'
	0x1F,0xFF,0xF0,0x03,0xFF,0xFF,0x80,0x70,0x00,0x1C,0x07,0x00,0x01,0xC0,0x70,0x00,0x1C,0x07,0x00,0x00,0x00,0x70,0x00,0x00,0x07,0x00,0x00,0x00,0x70,0x00,0x00,0x03,0xC0,0x00,0x00,0x1E,0x00,0x00,0x07,0x78,0x00,0x00,0xE3,0xE0,0x00,0x0E,0x0F,0x80,0xE0,0xE0,0x3C,0x0E,0x0E,0x00,0xF0,0xE0,0xE0,0x07,0xCE,0x0E,0x00,0x1E,0xE0,0xE0,0x00,0x7E,0x0E,0x00,0x01,0xF0,0xE0,0x00,0x0F,0x8E,0x00,0x00,0xFE,0x7F,0xFF,0xFC,0x73,0xFF,0xFF,0x81, // '&'
	0xFF,0xF0, // '''
	0x3B,0xF9,0xCE,0x73,0x9C,0xE7,0x39,0xCE,0x73,0x9C,0xE7,0x39,0xCE,0x71,0xE7, // '('
	0xE7,0x8E,0x73,0x9C,0xE7,0x39,0xCE,0x73,0x9C,0xE7,0x39,0xCE,0x73,0x9F,0xDC, // ')'
	0x03,0x80,0x07,0x00,0x0E,0x00,0x1C,0x27,0x39,0xCF,0xFF,0x8F,0xFE,0x03,0xE0,0x0F,0xE0,0x1D,0xC0,0x79,0xC1,0xE3,0xC1,0x83,0x00,0x00,0x00, // '*'
	0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0xFF,0xFF,0xFF,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00, // '+'
	0xFF,0xFF,0xA0, // ','
	0xFF,0xFF,0xFF, // '-'
	0xFC, // '.'
	0x00,0x00,0x00,0x01,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x0E,0x00,0x1C,0x00,0x38,0x00,0x30,0x00,0x70,0x00,0xE0,0x01,0xC0,0x03,0x80,0x03,0x00,0x07,0x00,0x0E,0x00,0x1C,0x00,0x38,0x00,0x30,0x00,0x70,0x00,0xE0,0x00,0xC0,0x00,0x80,0x00,0x00,0x00, // '/'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x7F,0x00,0x01,0xFE,0x00,0x07,0xFC,0x00,0x1F,0xF8,0x00,0x7B,0xF0,0x01,0xE7,0xE0,0x07,0x8F,0xC0,0x1E,0x1F,0x80,0x78,0x3F,0x01,0xE0,0x7E,0x07,0x80,0xFC,0x1E,0x01,0xF8,0x78,0x03,0xF1,0xE0,0x07,0xE7,0x80,0x0F,0xDE,0x00,0x1F,0xF8,0x00,0x3F,0xE0,0x00,0x7F,0x80,0x00,0xFE,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // '0'
	0x03,0xC1,0xF0,0xFC,0x7F,0x3D,0xCE,0x77,0x1C,0x07,0x01,0xC0,0x70,0x1C,0x07,0x01,0xC0,0x70,0x1C,0x07,0x01,0xC0,0x70,0x1C,0x07,0x01,0xC0,0x70,0x1C,0x07, // '1'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x73,0xFF,0xFF,0xCF,0xFF,0xFF,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x3F,0xFF,0xFF,0xFF,0xFF,0xFF, // '2'
	0x3F,0xFF,0xF0,0xFF,0xFF,0xF3,0x80,0x00,0x77,0x00,0x00,0xEE,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x3F,0xFF,0xE0,0x7F,0xFF,0xC0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // '3'
	0x00,0x01,0xE0,0x00,0x0F,0x80,0x00,0x7E,0x00,0x03,0xF8,0x00,0x1F,0xE0,0x00,0xFB,0x80,0x07,0xCE,0x00,0x3E,0x38,0x01,0xF0,0xE0,0x0F,0x83,0x80,0x78,0x0E,0x03,0xC0,0x38,0x1E,0x00,0xE0,0xF0,0x03,0x87,0x80,0x0E,0x3C,0x00,0x38,0xFF,0xFF,0xFF,0xFF,0xFF,0xF0,0x00,0x0E,0x00,0x00,0x38,0x00,0x00,0xE0,0x00,0x03,0x80,0x00,0x0E,0x00,0x00,0x38, // '4'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0xFF,0xFF,0xCF,0xFF,0xFF,0xC0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // '5'
	0x3F,0xFF,0xE0,0xFF,0xFF,0xC3,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0xFF,0xFF,0xCF,0xFF,0xFF,0xDC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // '6'
	0xFF,0xFF,0xCF,0xFF,0xFE,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x70,0x00,0x07, // '7'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3B,0xFF,0xFF,0xE7,0xFF,0xFF,0xDC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // '8'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x77,0xFF,0xFF,0xE7,0xFF,0xFF,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // '9'
	0xFC,0x00,0x00,0x00,0x00,0x00,0x1F,0x80, // ':'
	0xFC,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0xA0, // ';'
	0x00,0x08,0x00,0xC0,0x1E,0x03,0xF0,0x7E,0x07,0xC0,0xF8,0x1F,0x80,0xF0,0x07,0x00,0x3E,0x00,0xF8,0x01,0xF0,0x07,0xE0,0x0F,0xC0,0x1F,0x00,0x38,0x00,0xC0,0x00, // '<'
	0xFF,0xFF,0xFF,0xFF,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xFF,0xFF,0xFF,0xFF,0x80, // '='
	0x80,0x06,0x00,0x3C,0x01,0xF8,0x03,0xE0,0x07,0xC0,0x1F,0x80,0x3F,0x00,0x78,0x01,0xC0,0x3E,0x03,0xE0,0x7C,0x0F,0xC0,0xF8,0x1F,0x00,0xF0,0x06,0x00,0x00,0x00, // '>'
	0xFF,0xFF,0xE7,0xFF,0xFF,0x80,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x1C,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x38,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x70,0x3F,0xFF,0x03,0xFF,0xF0,0x38,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x01,0xC0,0x00, // '?'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x7F,0x07,0xE1,0xFF,0x0F,0xC6,0x03,0x1F,0x8C,0x06,0x3F,0x18,0x0C,0x7E,0x30,0x18,0xFC,0x60,0x31,0xF8,0xC0,0x63,0xF1,0x80,0xC7,0xE1,0xFF,0xFF,0xC1,0xFF,0xFF,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x1F,0xFF,0xFF,0x9F,0xFF,0xFF, // '@'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07, // 'A'
	0xFF,0xFF,0xF1,0xFF,0xFF,0xF3,0x80,0x00,0x77,0x00,0x00,0xEE,0x00,0x01,0xDC,0x00,0x03,0xB8,0x00,0x07,0x70,0x00,0x0E,0xE0,0x00,0x1D,0xC0,0x00,0x3B,0x80,0x00,0x77,0xFF,0xFF,0xEF,0xFF,0xFF,0xDC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xFF,0xFF,0xFF,0x7F,0xFF,0xFC, // 'B'
	0x3F,0xFF,0xFE,0xFF,0xFF,0xFF,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x1F,0xFF,0xFF,0x9F,0xFF,0xFF, // 'C'
	0xFF,0xFF,0xF9,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xFF,0xFF,0xFF,0x7F,0xFF,0xFC, // 'D'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x1C,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x38,0x00,0x01,0xFF,0xFF,0x8F,0xFF,0xFC,0x70,0x00,0x03,0x80,0x00,0x1C,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x38,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0xFF,0xFF,0xFF,0xFF,0xFF, // 'E'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x1C,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x38,0x00,0x01,0xFF,0xFF,0x8F,0xFF,0xFC,0x70,0x00,0x03,0x80,0x00,0x1C,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x38,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x1C,0x00,0x00, // 'F'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x1F,0xFC,0x00,0x3F,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // 'G'
	0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07,0xE0,0x00,0x07, // 'H'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF, // 'I'
	0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // 'J'
	0xE0,0x00,0x1D,0xC0,0x00,0x73,0x80,0x01,0xC7,0x00,0x07,0x8E,0x00,0x1E,0x1C,0x00,0x38,0x38,0x00,0xE0,0x70,0x03,0x80,0xE0,0x0F,0x01,0xC0,0x1C,0x03,0x80,0x70,0x07,0xFF,0xC0,0x0F,0xFF,0x80,0x1C,0x03,0x80,0x38,0x03,0x80,0x70,0x07,0x00,0xE0,0x07,0x01,0xC0,0x07,0x03,0x80,0x07,0x07,0x00,0x0F,0x0E,0x00,0x0F,0x1C,0x00,0x0E,0x38,0x00,0x0E,0x70,0x00,0x0E, // 'K'
	0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x3F,0xFF,0xFF,0xFF,0xFF,0xFF, // 'L'
	0xF0,0x00,0x03,0xFE,0x00,0x01,0xFF,0xC0,0x00,0xFF,0xF8,0x00,0x7F,0xEE,0x00,0x1D,0xF9,0xC0,0x0E,0x7E,0x38,0x07,0x1F,0x8F,0x03,0xC7,0xE1,0xC0,0xE1,0xF8,0x38,0x70,0x7E,0x07,0x38,0x1F,0x81,0xFE,0x07,0xE0,0x3F,0x01,0xF8,0x07,0x80,0x7E,0x00,0xC0,0x1F,0x80,0x20,0x07,0xE0,0x00,0x01,0xF8,0x00,0x00,0x7E,0x00,0x00,0x1F,0x80,0x00,0x07,0xE0,0x00,0x01,0xF8,0x00,0x00,0x7E,0x00,0x00,0x1F,0x80,0x00,0x07, // 'M'
	0xF0,0x00,0x0F,0xF0,0x00,0x1F,0xF0,0x00,0x3F,0xF0,0x00,0x7E,0xE0,0x00,0xFC,0xE0,0x01,0xF8,0xE0,0x03,0xF1,0xE0,0x07,0xE1,0xC0,0x0F,0xC1,0xC0,0x1F,0x81,0xC0,0x3F,0x03,0xC0,0x7E,0x03,0xC0,0xFC,0x03,0x81,0xF8,0x03,0x83,0xF0,0x03,0x87,0xE0,0x07,0x8F,0xC0,0x07,0x1F,0x80,0x07,0x3F,0x00,0x07,0x7E,0x00,0x0F,0xFC,0x00,0x0F,0xF8,0x00,0x0F,0xF0,0x00,0x0F, // 'N'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // 'O'
	0xFF,0xFF,0xF9,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFF,0xFF,0xFF,0xBF,0xFF,0xFE,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00, // 'P'
	0x3F,0xFF,0xF8,0x1F,0xFF,0xFF,0x0E,0x00,0x00,0xE3,0x80,0x00,0x38,0xE0,0x00,0x0E,0x38,0x00,0x03,0x8E,0x00,0x00,0xE3,0x80,0x00,0x38,0xE0,0x00,0x0E,0x38,0x00,0x03,0x8E,0x00,0x00,0xE3,0x80,0x00,0x38,0xE0,0x00,0x0E,0x38,0x00,0x03,0x8E,0x00,0x00,0xE3,0x80,0x00,0x38,0xE0,0x00,0x0E,0x38,0x00,0x03,0x8E,0x00,0x00,0xE3,0x80,0x00,0x38,0xE0,0x00,0x0E,0x38,0x00,0x03,0x87,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF, // 'Q'
	0xFF,0xFF,0xF9,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFF,0xFF,0xFF,0xBF,0xFF,0xFE,0x70,0x03,0x80,0xE0,0x07,0x81,0xC0,0x07,0x83,0x80,0x07,0x07,0x00,0x07,0x0E,0x00,0x0F,0x1C,0x00,0x0F,0x38,0x00,0x0F,0x70,0x00,0x0E, // 'R'
	0x3F,0xFF,0xF8,0xFF,0xFF,0xFB,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x03,0xFF,0xFF,0xC3,0xFF,0xFF,0xC0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // 'S'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00, // 'T'
	0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xF8,0x00,0x03,0xF0,0x00,0x07,0xE0,0x00,0x0F,0xC0,0x00,0x1F,0x80,0x00,0x3F,0x00,0x00,0x7E,0x00,0x00,0xFC,0x00,0x01,0xDF,0xFF,0xFF,0x1F,0xFF,0xFC, // 'U'
	0xE0,0x00,0x00,0x3D,0xC0,0x00,0x00,0xE3,0x80,0x00,0x07,0x0E,0x00,0x00,0x1C,0x1C,0x00,0x00,0xE0,0x70,0x00,0x03,0x80,0xE0,0x00,0x1C,0x03,0x80,0x00,0x70,0x07,0x00,0x03,0x80,0x1C,0x00,0x1E,0x00,0x38,0x00,0x70,0x00,0x70,0x03,0x80,0x01,0xC0,0x0E,0x00,0x03,0x80,0x70,0x00,0x0E,0x01,0xC0,0x00,0x1C,0x0E,0x00,0x00,0x70,0x38,0x00,0x00,0xE1,0xC0,0x00,0x03,0x87,0x00,0x00,0x07,0x38,0x00,0x00,0x0D,0xC0,0x00,0x00,0x3F,0x00,0x00,0x00,0x78,0x00,0x00,0x01,0xE0,0x00, // 'V'
	0xE0,0x00,0xF0,0x00,0x76,0x00,0x0F,0x00,0x0E,0x70,0x01,0xF0,0x00,0xE7,0x00,0x1F,0x80,0x0E,0x30,0x01,0xF8,0x01,0xC3,0x80,0x39,0x80,0x1C,0x38,0x03,0x9C,0x01,0x81,0x80,0x39,0xC0,0x38,0x1C,0x07,0x0C,0x03,0x81,0xC0,0x70,0xE0,0x30,0x0E,0x06,0x0E,0x07,0x00,0xE0,0xE0,0x70,0x70,0x0E,0x0E,0x07,0x06,0x00,0x70,0xC0,0x70,0xE0,0x07,0x1C,0x03,0x8E,0x00,0x31,0xC0,0x38,0xC0,0x03,0x98,0x03,0x9C,0x00,0x3B,0x80,0x1D,0xC0,0x01,0xB8,0x01,0xF8,0x00,0x1F,0x00,0x0F,0x80,0x01,0xF0,0x00,0xF8,0x00,0x0F,0x00,0x0F,0x00,0x00,0xE0,0x00,0x70,0x00,0x0E,0x00,0x07,0x00, // 'W'
	0x70,0x00,0x1E,0x38,0x00,0x1C,0x3C,0x00,0x38,0x1E,0x00,0x70,0x0E,0x00,0xF0,0x07,0x01,0xE0,0x03,0x81,0xC0,0x01,0xC3,0x80,0x01,0xE7,0x00,0x00,0xEF,0x00,0x00,0x7E,0x00,0x00,0x3C,0x00,0x00,0x3C,0x00,0x00,0x7E,0x00,0x00,0xFF,0x00,0x01,0xE7,0x00,0x01,0xC3,0x80,0x03,0x81,0xC0,0x07,0x01,0xE0,0x0E,0x00,0xF0,0x1E,0x00,0x70,0x3C,0x00,0x38,0x38,0x00,0x1C,0x70,0x00,0x1E, // 'X'
	0x70,0x00,0x07,0x3C,0x00,0x07,0x8E,0x00,0x03,0x83,0x80,0x03,0x80,0xE0,0x03,0x80,0x78,0x03,0xC0,0x1C,0x01,0xC0,0x07,0x01,0xC0,0x03,0xC1,0xE0,0x00,0xF1,0xE0,0x00,0x3D,0xE0,0x00,0x0E,0xE0,0x00,0x07,0xF0,0x00,0x01,0xF0,0x00,0x00,0x70,0x00,0x00,0x38,0x00,0x00,0x1C,0x00,0x00,0x0E,0x00,0x00,0x07,0x00,0x00,0x03,0x80,0x00,0x01,0xC0,0x00,0x00,0xE0,0x00,0x00,0x70,0x00,0x00,0x38,0x00, // 'Y'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x00,0x00,0x38,0x00,0x01,0xF0,0x00,0x07,0xC0,0x00,0x1E,0x00,0x00,0x78,0x00,0x01,0xE0,0x00,0x07,0x80,0x00,0x1E,0x00,0x00,0x78,0x00,0x01,0xE0,0x00,0x07,0x80,0x00,0x1E,0x00,0x00,0x78,0x00,0x01,0xE0,0x00,0x07,0x80,0x00,0x1E,0x00,0x00,0x78,0x00,0x03,0xE0,0x00,0x0F,0x80,0x00,0x1C,0x00,0x00,0x3F,0xFF,0xFF,0xFF,0xFF,0xFF, // 'Z'
	0xFF,0xF9,0xCE,0x73,0x9C,0xE7,0x39,0xCE,0x73,0x9C,0xE7,0x39,0xCE,0x73,0xFF, // '['
	0x00,0x00,0x80,0x00,0xC0,0x00,0xE0,0x00,0x60,0x00,0x70,0x00,0x38,0x00,0x1C,0x00,0x0E,0x00,0x06,0x00,0x07,0x00,0x03,0x80,0x01,0xC0,0x00,0xC0,0x00,0x60,0x00,0x70,0x00,0x38,0x00,0x1C,0x00,0x0C,0x00,0x0E,0x00,0x07,0x00,0x03,0x00,0x01,0x00,0x00, // '\'
	0xFF,0xCE,0x73,0x9C,0xE7,0x39,0xCE,0x73,0x9C,0xE7,0x39,0xCE,0x73,0x9F,0xFF, // ']'
	0x00, // '^'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFC, // '_'
	0xEE,0x67, // '`'
	0xFF,0xFF,0x9F,0xFF,0xF8,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0xFF,0xFF,0xFF,0xFF,0xFF,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xEF,0xFF,0xFC,0xFF,0xFF,0x80, // 'a'
	0xE0,0x00,0x1C,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x3F,0xFF,0xE7,0xFF,0xFE,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0xFF,0xFE,0xFF,0xFF,0x80, // 'b'
	0x3F,0xFF,0xEF,0xFF,0xFF,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE0,0x00,0x1C,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE0,0x00,0x0F,0xFF,0xFC,0xFF,0xFF,0x80, // 'c'
	0x00,0x00,0xE0,0x00,0x1C,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xCF,0xFF,0xFB,0xFF,0xFF,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3B,0xFF,0xFF,0x3F,0xFF,0xE0, // 'd'
	0x3F,0xFF,0x8F,0xFF,0xFB,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xFF,0xFF,0xFF,0xFF,0xFF,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE0,0x00,0x0F,0xFF,0xFC,0xFF,0xFF,0x80, // 'e'
	0x3F,0xEF,0xFF,0x80,0x70,0x0E,0x01,0xC0,0x3F,0xFF,0xFF,0xE0,0x1C,0x03,0x80,0x70,0x0E,0x01,0xC0,0x38,0x07,0x00,0xE0,0x1C,0x03,0x80,0x70,0x0E,0x01,0xC0,0x38,0x07,0x00,0xE0,0x00, // 'f'
	0x3F,0xFF,0x8F,0xFF,0xFB,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xEF,0xFF,0xFC,0xFF,0xFF,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE1,0xFF,0xF8,0x3F,0xFE,0x00, // 'g'
	0xE0,0x00,0x1C,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x3F,0xFF,0xE7,0xFF,0xFE,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xE0, // 'h'
	0xFC,0x00,0x3F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE0, // 'i'
	0x00,0x70,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x70,0x07,0x00,0x7F,0xFE,0xFF,0xC0, // 'j'
	0xE0,0x00,0x1C,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x77,0x00,0x1C,0xE0,0x07,0x9C,0x01,0xE3,0x80,0x78,0x70,0x1E,0x0E,0x07,0x81,0xC0,0xE0,0x3F,0xF8,0x07,0xFF,0x00,0xE0,0x70,0x1C,0x0F,0x03,0x80,0xE0,0x70,0x0E,0x0E,0x00,0xE1,0xC0,0x0E,0x38,0x01,0xE7,0x00,0x1E,0xE0,0x01,0xC0, // 'k'
	0xE1,0xC3,0x87,0x0E,0x1C,0x38,0x70,0xE1,0xC3,0x87,0x0E,0x1C,0x38,0x70,0xE1,0xC3,0x87,0x0E,0x1C,0x38,0x3F,0x3E, // 'l'
	0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xB8,0x01,0xC0,0x0F,0xC0,0x0E,0x00,0x7E,0x00,0x70,0x03,0xF0,0x03,0x80,0x1F,0x80,0x1C,0x00,0xFC,0x00,0xE0,0x07,0xE0,0x07,0x00,0x3F,0x00,0x38,0x01,0xF8,0x01,0xC0,0x0F,0xC0,0x0E,0x00,0x7E,0x00,0x70,0x03,0xF0,0x03,0x80,0x1F,0x80,0x1C,0x00,0xFC,0x00,0xE0,0x07,0xE0,0x07,0x00,0x3F,0x00,0x38,0x01,0xF8,0x01,0xC0,0x0E, // 'm'
	0xFF,0xFF,0x9F,0xFF,0xFB,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0x80, // 'n'
	0x3F,0xFF,0x8F,0xFF,0xFB,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xEF,0xFF,0xF8,0xFF,0xFE,0x00, // 'o'
	0xFF,0xFF,0x9F,0xFF,0xFB,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFF,0xFF,0xFB,0xFF,0xFE,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE0,0x00,0x1C,0x00,0x03,0x80,0x00,0x00, // 'p'
	0x3F,0xFF,0xEF,0xFF,0xFF,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xEF,0xFF,0xFC,0xFF,0xFF,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE0,0x00,0x1C,0x00,0x03,0x80, // 'q'
	0x3F,0xFE,0xFF,0xFF,0x80,0x07,0x00,0x0E,0x00,0x1C,0x00,0x38,0x00,0x70,0x00,0xE0,0x01,0xC0,0x03,0x80,0x07,0x00,0x0E,0x00,0x1C,0x00,0x38,0x00,0x70,0x00,0xE0,0x01,0xC0,0x03,0x80,0x00, // 'r'
	0x3F,0xFF,0x8F,0xFF,0xFB,0x80,0x03,0xF0,0x00,0x7E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0x7F,0xFF,0x87,0xFF,0xF8,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x3F,0x00,0x07,0xE0,0x00,0xEF,0xFF,0xF8,0xFF,0xFE,0x00, // 's'
	0xE0,0x1C,0x03,0x80,0x70,0x0E,0x01,0xC0,0x3F,0xFF,0xFF,0xE0,0x1C,0x03,0x80,0x70,0x0E,0x01,0xC0,0x38,0x07,0x00,0xE0,0x1C,0x03,0x80,0x70,0x0E,0x01,0xC0,0x38,0x03,0xFF,0x3F,0xE0, // 't'
	0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xEF,0xFF,0xF8,0xFF,0xFE,0x00, // 'u'
	0x70,0x00,0x07,0x3C,0x00,0x07,0x0E,0x00,0x03,0x83,0x80,0x03,0x81,0xC0,0x01,0xC0,0x70,0x01,0xC0,0x38,0x00,0xE0,0x0E,0x00,0xE0,0x07,0x00,0x70,0x01,0xC0,0x70,0x00,0xE0,0x38,0x00,0x38,0x38,0x00,0x1C,0x38,0x00,0x07,0x1C,0x00,0x03,0x9C,0x00,0x00,0xEE,0x00,0x00,0x3E,0x00,0x00,0x1F,0x00,0x00,0x07,0x00,0x00, // 'v'
	0xE0,0x03,0xC0,0x07,0x38,0x01,0xF0,0x03,0x9C,0x01,0xF8,0x01,0xCE,0x00,0xFE,0x01,0xC3,0x80,0x77,0x00,0xE1,0xC0,0x73,0x80,0x60,0x70,0x38,0xE0,0x70,0x38,0x3C,0x70,0x38,0x1C,0x1C,0x1C,0x38,0x07,0x0E,0x0E,0x1C,0x03,0x8E,0x07,0x8E,0x00,0xE7,0x01,0xCE,0x00,0x77,0x00,0xF7,0x00,0x3B,0x80,0x3B,0x80,0x0F,0xC0,0x1F,0x80,0x07,0xC0,0x07,0xC0,0x03,0xE0,0x03,0xC0,0x00,0xE0,0x00,0xE0,0x00,0x70,0x00,0x70,0x00, // 'w'
	0x70,0x00,0xE3,0x80,0x1C,0x1C,0x03,0x81,0xE0,0x78,0x0F,0x0F,0x00,0x79,0xE0,0x03,0x9C,0x00,0x1F,0x80,0x00,0xF0,0x00,0x0F,0x00,0x01,0xF8,0x00,0x3F,0x80,0x03,0x9C,0x00,0x70,0xE0,0x0F,0x0F,0x01,0xE0,0x78,0x3C,0x03,0x83,0x80,0x1C,0x70,0x00,0xE0, // 'x'
	0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xFC,0x00,0x1F,0x80,0x03,0xF0,0x00,0x7E,0x00,0x0F,0xC0,0x01,0xF8,0x00,0x3F,0x00,0x07,0xE0,0x00,0xEF,0xFF,0xFC,0xFF,0xFF,0x80,0x00,0x70,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE1,0xFF,0xF8,0x3F,0xFE,0x00, // 'y'
	0xFF,0xFF,0xFF,0xFF,0xFC,0x00,0x03,0x80,0x01,0xF0,0x00,0x78,0x00,0x1E,0x00,0x07,0x80,0x01,0xE0,0x00,0x78,0x00,0x3E,0x00,0x0F,0x00,0x03,0xC0,0x00,0xF0,0x00,0x3C,0x00,0x0F,0x00,0x07,0xC0,0x00,0xE0,0x00,0x1F,0xFF,0xFF,0xFF,0xFF,0x80, // 'z'
	0x0E,0x3C,0xE1,0xC3,0x87,0x0E,0x1C,0x38,0x73,0xC7,0x0E,0x1E,0x0E,0x1C,0x38,0x70,0xE1,0xC3,0x87,0x07,0x87, // '{'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8, // '|'
	0xE3,0xC3,0x8E,0x38,0xE3,0x8E,0x38,0xE1,0xC3,0x0C,0x73,0x8E,0x38,0xE3,0x8E,0x38,0xEF,0x38 // '}'
};
const GFXglyph Orbitron_Light_32Glyphs[] PROGMEM = {
// bitmapOffset, width, height, xAdvance, xOffset, yOffset
	  {     0,   1,   1,  10,    0,    0 }, // ' '
	  {     1,   3,  24,   9,    2,  -24 }, // '!'
	  {    10,   8,   4,  13,    2,  -24 }, // '"'
	  {    14,  24,  24,  27,    1,  -24 }, // '#'
	  {    86,  23,  31,  26,    1,  -27 }, // '$'
	  {   176,  28,  25,  32,    2,  -25 }, // '%'
	  {   264,  28,  24,  31,    2,  -24 }, // '&'
	  {   348,   3,   4,   9,    2,  -24 }, // '''
	  {   350,   5,  24,  10,    2,  -24 }, // '('
	  {   365,   5,  24,  10,    2,  -24 }, // ')'
	  {   380,  15,  14,  17,    0,  -24 }, // '*'
	  {   407,  12,  13,  15,    1,  -16 }, // '+'
	  {   427,   3,   7,   8,    2,   -3 }, // ','
	  {   430,  12,   2,  17,    2,  -10 }, // '-'
	  {   433,   3,   2,   8,    2,   -2 }, // '.'
	  {   434,  16,  24,  19,    1,  -24 }, // '/'
	  {   482,  23,  24,  28,    2,  -24 }, // '0'
	  {   551,  10,  24,  14,    0,  -24 }, // '1'
	  {   581,  23,  24,  27,    2,  -24 }, // '2'
	  {   650,  23,  24,  27,    2,  -24 }, // '3'
	  {   719,  22,  24,  25,    1,  -24 }, // '4'
	  {   785,  23,  24,  27,    2,  -24 }, // '5'
	  {   854,  23,  24,  27,    2,  -24 }, // '6'
	  {   923,  20,  24,  23,    0,  -24 }, // '7'
	  {   983,  23,  24,  28,    2,  -24 }, // '8'
	  {  1052,  23,  24,  28,    2,  -24 }, // '9'
	  {  1121,   3,  19,   8,    2,  -19 }, // ':'
	  {  1129,   3,  23,   8,    2,  -19 }, // ';'
	  {  1138,  13,  19,  17,    1,  -19 }, // '<'
	  {  1169,  17,   9,  22,    2,  -14 }, // '='
	  {  1189,  13,  19,  17,    2,  -19 }, // '>'
	  {  1220,  21,  24,  24,    1,  -24 }, // '?'
	  {  1283,  23,  24,  28,    2,  -24 }, // '@'
	  {  1352,  23,  24,  28,    2,  -24 }, // 'A'
	  {  1421,  23,  24,  28,    2,  -24 }, // 'B'
	  {  1490,  23,  24,  28,    2,  -24 }, // 'C'
	  {  1559,  23,  24,  28,    2,  -24 }, // 'D'
	  {  1628,  21,  24,  26,    2,  -24 }, // 'E'
	  {  1691,  21,  24,  25,    2,  -24 }, // 'F'
	  {  1754,  23,  24,  28,    2,  -24 }, // 'G'
	  {  1823,  24,  24,  29,    2,  -24 }, // 'H'
	  {  1895,   3,  24,   9,    2,  -24 }, // 'I'
	  {  1904,  23,  24,  27,    1,  -24 }, // 'J'
	  {  1973,  23,  24,  27,    2,  -24 }, // 'K'
	  {  2042,  23,  24,  27,    2,  -24 }, // 'L'
	  {  2111,  26,  24,  31,    2,  -24 }, // 'M'
	  {  2189,  23,  24,  28,    2,  -24 }, // 'N'
	  {  2258,  23,  24,  28,    2,  -24 }, // 'O'
	  {  2327,  23,  24,  27,    2,  -24 }, // 'P'
	  {  2396,  26,  24,  30,    2,  -24 }, // 'Q'
	  {  2474,  23,  24,  28,    2,  -24 }, // 'R'
	  {  2543,  23,  24,  28,    2,  -24 }, // 'S'
	  {  2612,  23,  24,  26,    1,  -24 }, // 'T'
	  {  2681,  23,  24,  28,    2,  -24 }, // 'U'
	  {  2750,  30,  24,  33,    1,  -24 }, // 'V'
	  {  2840,  36,  24,  39,    1,  -24 }, // 'W'
	  {  2948,  24,  24,  27,    1,  -24 }, // 'X'
	  {  3020,  25,  24,  27,    0,  -24 }, // 'Y'
	  {  3095,  23,  24,  28,    2,  -24 }, // 'Z'
	  {  3164,   5,  24,  10,    2,  -24 }, // '['
	  {  3179,  16,  24,  19,    1,  -24 }, // '\'
	  {  3227,   5,  24,  10,    2,  -24 }, // ']'
	  {  3242,   1,   1,   1,    0,    0 }, // '^'
	  {  3243,  23,   2,  27,    2,    1 }, // '_'
	  {  3249,   4,   4,   8,    1,  -33 }, // '`'
	  {  3251,  19,  19,  24,    2,  -19 }, // 'a'
	  {  3297,  19,  25,  23,    2,  -25 }, // 'b'
	  {  3357,  19,  19,  24,    2,  -19 }, // 'c'
	  {  3403,  19,  25,  23,    1,  -25 }, // 'd'
	  {  3463,  19,  19,  24,    2,  -19 }, // 'e'
	  {  3509,  11,  25,  14,    2,  -25 }, // 'f'
	  {  3544,  19,  27,  23,    1,  -19 }, // 'g'
	  {  3609,  19,  25,  23,    2,  -25 }, // 'h'
	  {  3669,   3,  25,   8,    2,  -25 }, // 'i'
	  {  3679,  12,  33,   9,   -6,  -25 }, // 'j'
	  {  3729,  19,  25,  22,    2,  -25 }, // 'k'
	  {  3789,   7,  25,  11,    2,  -25 }, // 'l'
	  {  3811,  29,  19,  33,    2,  -19 }, // 'm'
	  {  3880,  19,  19,  24,    2,  -19 }, // 'n'
	  {  3926,  19,  19,  24,    2,  -19 }, // 'o'
	  {  3972,  19,  27,  23,    2,  -19 }, // 'p'
	  {  4037,  19,  27,  23,    1,  -19 }, // 'q'
	  {  4102,  15,  19,  18,    2,  -19 }, // 'r'
	  {  4138,  19,  19,  24,    2,  -19 }, // 's'
	  {  4184,  11,  25,  14,    2,  -25 }, // 't'
	  {  4219,  19,  19,  24,    2,  -19 }, // 'u'
	  {  4265,  25,  19,  26,    0,  -19 }, // 'v'
	  {  4325,  33,  19,  35,    1,  -19 }, // 'w'
	  {  4404,  20,  19,  23,    1,  -19 }, // 'x'
	  {  4452,  19,  27,  23,    1,  -19 }, // 'y'
	  {  4517,  19,  19,  24,    2,  -19 }, // 'z'
	  {  4563,   7,  24,  10,    0,  -24 }, // '{'
	  {  4584,   3,  31,   8,    2,  -27 }, // '|'
	  {  4596,   6,  24,  10,    2,  -24 } // '}'
};
const GFXfont Orbitron_Light_32 PROGMEM = {
(uint8_t  *)Orbitron_Light_32Bitmaps,(GFXglyph *)Orbitron_Light_32Glyphs,0x20, 0x7D, 32};
