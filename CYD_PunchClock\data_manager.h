/*
 * Data Manager Header
 * Handles SD card and SPIFFS file operations
 */

#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include <SD.h>
#include <SPIFFS.h>
#include <FS.h>
#include <ArduinoJson.h>
#include "config.h"

class DataManager {
private:
  bool sdInitialized;
  bool spiffsInitialized;
  
  // File operation statistics
  unsigned long totalReads;
  unsigned long totalWrites;
  unsigned long failedOperations;
  
  // Backup management
  unsigned long lastBackup;
  unsigned long backupInterval;
  
  // Private methods
  bool createDirectoryIfNotExists(String path);
  String generateBackupFilename(String baseName);
  bool copyFile(String source, String destination);
  void cleanupOldBackups(String directory, int maxBackups);
  
public:
  DataManager();
  ~DataManager();
  
  // Initialization
  bool begin();
  void reset();
  bool isSDAvailable() const { return sdInitialized; }
  bool isSPIFFSAvailable() const { return spiffsInitialized; }
  
  // File operations
  bool writeFile(String path, String content);
  bool writeFile(String path, const uint8_t* data, size_t length);
  String readFile(String path);
  bool readFile(String path, uint8_t* buffer, size_t& length);
  bool deleteFile(String path);
  bool fileExists(String path);
  size_t getFileSize(String path);
  
  // Directory operations
  bool createDirectory(String path);
  bool deleteDirectory(String path);
  bool listDirectory(String path, std::vector<String>& files);
  
  // JSON operations
  bool writeJSON(String path, JsonDocument& doc);
  bool readJSON(String path, JsonDocument& doc);
  
  // CSV operations
  bool writeCSV(String path, String header, std::vector<String>& rows);
  bool appendCSV(String path, String row);
  bool readCSV(String path, std::vector<String>& rows);
  
  // Attendance logging
  bool logAttendance(const AttendanceRecord& record);
  bool getAttendanceRecords(String startDate, String endDate, std::vector<AttendanceRecord>& records);
  bool exportAttendanceCSV(String filename, String startDate, String endDate);
  
  // Configuration management
  bool saveConfig(const SystemConfig& config);
  bool loadConfig(SystemConfig& config);
  bool resetConfig();
  
  // Backup operations
  bool createBackup();
  bool restoreBackup(String backupName);
  bool listBackups(std::vector<String>& backups);
  void setBackupInterval(unsigned long interval) { backupInterval = interval; }
  
  // System logs
  bool writeLog(String level, String message);
  bool getSystemLogs(std::vector<String>& logs);
  bool clearLogs();
  
  // Storage information
  uint64_t getTotalSpace();
  uint64_t getUsedSpace();
  uint64_t getFreeSpace();
  float getUsagePercentage();
  
  // Statistics
  unsigned long getTotalReads() const { return totalReads; }
  unsigned long getTotalWrites() const { return totalWrites; }
  unsigned long getFailedOperations() const { return failedOperations; }
  void resetStatistics();
  
  // Maintenance
  bool checkFileSystem();
  bool defragmentStorage();
  bool verifyDataIntegrity();
  
  // Error handling
  String getLastError() const;
  bool hasError() const;
  void clearError();
  
  // Utility functions
  String formatBytes(uint64_t bytes);
  String getCurrentDateString();
  String getCurrentTimeString();
  bool isValidFilename(String filename);
};

#endif // DATA_MANAGER_H
