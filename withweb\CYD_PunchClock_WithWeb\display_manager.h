/*
 * Display Manager Header
 * Handles TFT display and touch screen operations
 */

#ifndef DISPLAY_MANAGER_H
#define DISPLAY_MANAGER_H

// TFT_eSPI configuration for ESP32-2432S028R
#define USER_SETUP_LOADED
#define ILI9341_DRIVER
#define TFT_MISO 19
#define TFT_MOSI 23
#define TFT_SCLK 18
#define TFT_CS   15
#define TFT_DC   2
#define TFT_RST  -1
#define TFT_BL   21
#define LOAD_GLCD
#define LOAD_FONT2
#define LOAD_FONT4
#define SPI_FREQUENCY 27000000

#include <TFT_eSPI.h>
#include <XPT2046_Touchscreen.h>
#include "config.h"

class DisplayManager {
private:
  TFT_eSPI tft;
  XPT2046_Touchscreen touch;
  
  // Touch calibration data
  uint16_t calData[5];
  bool calibrated;
  
  // Screen state
  bool backlightOn;
  uint8_t currentBrightness;
  unsigned long lastTouchTime;
  
  // Touch handling
  TouchPoint lastTouch;
  bool touchPressed;
  unsigned long touchDebounceTime;
  
  // Screen buffer for efficient updates
  bool screenDirty;
  
  // Private methods
  void loadCalibration();
  void saveCalibration();
  void performCalibration();
  bool validateTouch(uint16_t x, uint16_t y);
  void drawButton(uint16_t x, uint16_t y, uint16_t w, uint16_t h, 
                  String text, uint16_t bgColor, uint16_t textColor, bool pressed = false);
  void drawCenteredText(String text, uint16_t x, uint16_t y, uint16_t w, uint16_t h, 
                       uint8_t font, uint16_t color);
  void drawProgressBar(uint16_t x, uint16_t y, uint16_t w, uint16_t h, 
                      uint8_t progress, uint16_t color);

public:
  DisplayManager();
  ~DisplayManager();
  
  // Initialization
  bool begin();
  void reset();
  
  // Backlight control
  void turnOnBacklight();
  void turnOffBacklight();
  void setBrightness(uint8_t brightness);
  void updateBrightness(); // Auto-brightness based on LDR
  
  // Touch handling
  bool isTouched();
  TouchPoint getTouchPoint();
  bool isButtonPressed(uint16_t x, uint16_t y, uint16_t w, uint16_t h);
  
  // Screen management
  void clearScreen();
  void showWelcomeScreen();
  void showMainScreen();
  void updateMainScreen(String currentTime, String lastEmployee, String lastAction);
  void showProcessing();
  void showError(String message);
  void showSuccess(String message);
  void showPunchSuccess(String employeeName, String action, String timestamp);
  
  // Admin screens
  void showAdminLogin();
  void showAdminMenu();
  void showEmployeeList();
  void showEmployeeDetails(Employee& employee);
  void showAddEmployee();
  void showReports();
  
  // Settings screens
  void showSettings();
  void showWiFiSetup();
  void showTimeSettings();
  void showSystemInfo();
  
  // Utility screens
  void showKeyboard(String& input, String prompt);
  void showNumericKeypad(String& input, String prompt);
  void showConfirmDialog(String message, bool& confirmed);
  void showMessageBox(String title, String message, uint16_t color);
  
  // Drawing utilities
  void drawHeader(String title);
  void drawFooter();
  void drawStatusBar();
  void drawTime(String timeStr, uint16_t x, uint16_t y);
  void drawDate(String dateStr, uint16_t x, uint16_t y);
  
  // Screen transitions
  void fadeIn();
  void fadeOut();
  void slideTransition(bool left);
  
  // Debug and diagnostics
  void showDiagnostics();
  void printTouchCalibration();
  void testDisplay();
};

#endif // DISPLAY_MANAGER_H
