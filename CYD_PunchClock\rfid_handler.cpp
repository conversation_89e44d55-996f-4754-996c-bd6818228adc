/*
 * RFID Handler Implementation
 * Manages MFRC522 RFID reader operations
 */

#include "rfid_handler.h"

RFIDHandler::RFIDHandler() : 
  mfrc522(RFID_SDA, RFID_RST),
  initialized(false),
  cardPresent(false),
  lastCardUID(""),
  lastReadTime(0),
  lastCardTime(0),
  debounceTime(RFID_DEBOUNCE_TIME),
  cardDetected(false),
  totalReads(0),
  successfulReads(0),
  failedReads(0) {
}

RFIDHandler::~RFIDHandler() {
  if (initialized) {
    mfrc522.PCD_SoftPowerDown();
  }
}

bool RFIDHandler::begin() {
  Serial.println("Initializing RFID reader...");
  
  // Initialize SPI
  SPI.begin();
  
  // Initialize MFRC522
  mfrc522.PCD_Init();
  
  // Perform self-test
  if (!performSelfTest()) {
    Serial.println("RFID self-test failed!");
    return false;
  }
  
  // Set antenna gain to maximum
  mfrc522.PCD_SetAntennaGain(mfrc522.RxGain_max);
  
  initialized = true;
  Serial.println("RFID reader initialized successfully");
  
  // Print reader information
  printReaderInfo();
  
  return true;
}

void RFIDHandler::reset() {
  if (!initialized) return;
  
  mfrc522.PCD_Reset();
  delay(50);
  mfrc522.PCD_Init();
  
  cardPresent = false;
  lastCardUID = "";
  cardDetected = false;
  
  Serial.println("RFID reader reset");
}

bool RFIDHandler::isCardPresent() {
  if (!initialized) return false;
  
  unsigned long currentTime = millis();
  
  // Check debounce time
  if (currentTime - lastCardTime < debounceTime) {
    return false;
  }
  
  // Look for new cards
  if (!mfrc522.PICC_IsNewCardPresent()) {
    cardPresent = false;
    cardDetected = false;
    return false;
  }
  
  // Select one of the cards
  if (!mfrc522.PICC_ReadCardSerial()) {
    cardPresent = false;
    cardDetected = false;
    failedReads++;
    return false;
  }
  
  cardPresent = true;
  lastCardTime = currentTime;
  totalReads++;
  
  return true;
}

String RFIDHandler::readCardUID() {
  if (!cardPresent || !initialized) {
    return "";
  }
  
  String uid = formatUID(mfrc522.uid.uidByte, mfrc522.uid.size);
  
  // Validate the UID
  if (validateCard(mfrc522.uid.uidByte, mfrc522.uid.size)) {
    lastCardUID = uid;
    successfulReads++;
    lastReadTime = millis();
    
    // Halt the card to prevent multiple reads
    mfrc522.PICC_HaltA();
    mfrc522.PCD_StopCrypto1();
    
    Serial.println("Card UID read: " + uid);
    return uid;
  } else {
    failedReads++;
    Serial.println("Invalid card detected");
    return "";
  }
}

bool RFIDHandler::readCardData(byte* buffer, byte bufferSize) {
  if (!cardPresent || !initialized) {
    return false;
  }
  
  // This is a simplified implementation
  // In a real application, you might want to read specific sectors
  
  MFRC522::StatusCode status;
  byte size = bufferSize;
  
  // Read from block 4 (first data block in sector 1)
  status = mfrc522.MIFARE_Read(4, buffer, &size);
  
  if (status == MFRC522::STATUS_OK) {
    Serial.println("Card data read successfully");
    return true;
  } else {
    Serial.print("Failed to read card data: ");
    Serial.println(mfrc522.GetStatusCodeName(status));
    return false;
  }
}

bool RFIDHandler::authenticateCard(String uid) {
  if (!cardPresent || !initialized) {
    return false;
  }
  
  // Default key for authentication
  MFRC522::MIFARE_Key key;
  for (byte i = 0; i < 6; i++) {
    key.keyByte[i] = 0xFF; // Default key
  }
  
  // Authenticate using key A
  MFRC522::StatusCode status = mfrc522.PCD_Authenticate(
    MFRC522::PICC_CMD_MF_AUTH_KEY_A, 4, &key, &(mfrc522.uid));
  
  if (status == MFRC522::STATUS_OK) {
    Serial.println("Card authenticated successfully");
    return true;
  } else {
    Serial.print("Authentication failed: ");
    Serial.println(mfrc522.GetStatusCodeName(status));
    return false;
  }
}

bool RFIDHandler::writeCardData(byte* data, byte dataSize) {
  if (!cardPresent || !initialized) {
    return false;
  }
  
  // Authenticate first
  if (!authenticateCard(lastCardUID)) {
    return false;
  }
  
  // Write to block 4
  MFRC522::StatusCode status = mfrc522.MIFARE_Write(4, data, 16);
  
  if (status == MFRC522::STATUS_OK) {
    Serial.println("Card data written successfully");
    return true;
  } else {
    Serial.print("Failed to write card data: ");
    Serial.println(mfrc522.GetStatusCodeName(status));
    return false;
  }
}

void RFIDHandler::enableReader() {
  if (!initialized) return;
  
  mfrc522.PCD_SoftPowerUp();
  Serial.println("RFID reader enabled");
}

void RFIDHandler::disableReader() {
  if (!initialized) return;
  
  mfrc522.PCD_SoftPowerDown();
  Serial.println("RFID reader disabled");
}

void RFIDHandler::powerDown() {
  disableReader();
}

void RFIDHandler::powerUp() {
  enableReader();
}

bool RFIDHandler::testReader() {
  if (!initialized) return false;
  
  return performSelfTest();
}

void RFIDHandler::printReaderInfo() {
  if (!initialized) return;
  
  Serial.println("=== RFID Reader Information ===");
  Serial.print("Firmware Version: 0x");
  Serial.println(mfrc522.PCD_ReadRegister(MFRC522::VersionReg), HEX);
  
  String version = getReaderVersion();
  Serial.println("Reader Version: " + version);
  
  Serial.print("Antenna Gain: ");
  Serial.println(mfrc522.PCD_GetAntennaGain());
  
  Serial.println("===============================");
}

void RFIDHandler::printCardInfo() {
  if (!cardPresent || !initialized) return;
  
  Serial.println("=== Card Information ===");
  Serial.print("Card UID: ");
  Serial.println(formatUID(mfrc522.uid.uidByte, mfrc522.uid.size));
  
  Serial.print("Card Type: ");
  MFRC522::PICC_Type piccType = mfrc522.PICC_GetType(mfrc522.uid.sak);
  Serial.println(mfrc522.PICC_GetTypeName(piccType));
  
  Serial.print("UID Size: ");
  Serial.println(mfrc522.uid.size);
  
  Serial.println("========================");
}

String RFIDHandler::getReaderVersion() {
  if (!initialized) return "Unknown";
  
  byte version = mfrc522.PCD_ReadRegister(MFRC522::VersionReg);
  
  switch (version) {
    case 0x88: return "Clone";
    case 0x90: return "v0.0";
    case 0x91: return "v1.0";
    case 0x92: return "v2.0";
    default: return "Unknown (0x" + String(version, HEX) + ")";
  }
}

float RFIDHandler::getSuccessRate() const {
  if (totalReads == 0) return 0.0;
  return (float)successfulReads / totalReads * 100.0;
}

void RFIDHandler::resetStatistics() {
  totalReads = 0;
  successfulReads = 0;
  failedReads = 0;
}

// Private methods

String RFIDHandler::formatUID(byte* uid, byte uidSize) {
  String result = "";
  
  for (byte i = 0; i < uidSize; i++) {
    if (i > 0) result += ":";
    if (uid[i] < 0x10) result += "0";
    result += String(uid[i], HEX);
  }
  
  result.toUpperCase();
  return result;
}

bool RFIDHandler::validateCard(byte* uid, byte uidSize) {
  // Basic validation - check if UID is not all zeros or all FFs
  bool allZeros = true;
  bool allFFs = true;
  
  for (byte i = 0; i < uidSize; i++) {
    if (uid[i] != 0x00) allZeros = false;
    if (uid[i] != 0xFF) allFFs = false;
  }
  
  return !allZeros && !allFFs && uidSize >= 4;
}

void RFIDHandler::resetReader() {
  if (!initialized) return;
  
  mfrc522.PCD_Reset();
  delay(50);
}

bool RFIDHandler::performSelfTest() {
  Serial.println("Performing RFID self-test...");
  
  bool result = mfrc522.PCD_PerformSelfTest();
  
  if (result) {
    Serial.println("RFID self-test: PASSED");
    
    // Re-initialize after self-test
    mfrc522.PCD_Init();
  } else {
    Serial.println("RFID self-test: FAILED");
  }
  
  return result;
}

String RFIDHandler::getLastError() const {
  // This would be implemented with proper error tracking
  return "No error tracking implemented yet";
}

bool RFIDHandler::hasError() const {
  // This would be implemented with proper error tracking
  return false;
}

void RFIDHandler::clearError() {
  // This would be implemented with proper error tracking
}
