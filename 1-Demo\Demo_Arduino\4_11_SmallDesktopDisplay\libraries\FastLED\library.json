{"name": "FastLED", "description": "FastLED is a library for programming addressable rgb led strips (APA102/Dotstar, WS2812/Neopixel, LPD8806, and a dozen others) acting both as a driver and as a library for color management and fast math.", "keywords": "led,noise,rgb,math,fast", "authors": [{"name": "<PERSON>", "url": "https://github.com/focalintent", "maintainer": true}, {"name": "<PERSON>", "url": "https://github.com/kriegsman", "maintainer": true}], "repository": {"type": "git", "url": "https://github.com/FastLED/FastLED.git"}, "version": "3.3.2", "license": "MIT", "homepage": "http://fastled.io", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": "at<PERSON><PERSON><PERSON>, atmelsam, freescalekinetis, nordicnrf51, nxplpc, ststm32, teensy, espressif8266, espressif32, nordicnrf52", "export": {"exclude": ["docs", "extras"]}, "build": {"srcFilter": ["+<*.c>", "+<*.cpp>", "+<*.h>"], "libArchive": false}}