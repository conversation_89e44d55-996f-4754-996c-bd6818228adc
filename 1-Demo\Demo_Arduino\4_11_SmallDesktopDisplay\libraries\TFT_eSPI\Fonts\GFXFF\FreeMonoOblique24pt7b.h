const uint8_t FreeMonoOblique24pt7bBitmaps[] PROGMEM = {
  0x01, 0xC0, 0xF0, 0x3C, 0x0E, 0x03, 0x81, 0xE0, 0x78, 0x1C, 0x07, 0x01,
  0xC0, 0xE0, 0x38, 0x0E, 0x03, 0x00, 0xC0, 0x70, 0x1C, 0x06, 0x01, 0x80,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x0F, 0x83, 0xE0, 0xF8,
  0x1C, 0x00, 0x7E, 0x3F, 0x7E, 0x3F, 0x7C, 0x3E, 0x7C, 0x3E, 0x7C, 0x3E,
  0x78, 0x3C, 0xF8, 0x7C, 0xF0, 0x78, 0xF0, 0x78, 0xF0, 0x78, 0xE0, 0x70,
  0xE0, 0x70, 0xE0, 0x70, 0xC0, 0x60, 0x00, 0x18, 0x30, 0x00, 0x61, 0x80,
  0x01, 0x86, 0x00, 0x04, 0x18, 0x00, 0x30, 0xC0, 0x00, 0xC3, 0x00, 0x03,
  0x0C, 0x00, 0x18, 0x30, 0x00, 0x61, 0x80, 0x01, 0x86, 0x00, 0x06, 0x18,
  0x07, 0xFF, 0xFF, 0x1F, 0xFF, 0xFC, 0x03, 0x0C, 0x00, 0x18, 0x30, 0x00,
  0x61, 0x80, 0x01, 0x86, 0x00, 0x06, 0x18, 0x00, 0x30, 0xC0, 0x1F, 0xFF,
  0xF8, 0x7F, 0xFF, 0xE0, 0x18, 0x30, 0x00, 0x61, 0x80, 0x01, 0x86, 0x00,
  0x06, 0x18, 0x00, 0x30, 0x40, 0x00, 0xC3, 0x00, 0x03, 0x0C, 0x00, 0x18,
  0x30, 0x00, 0x61, 0x80, 0x01, 0x86, 0x00, 0x06, 0x18, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x80, 0x00, 0x3F, 0x00, 0x07, 0xFD, 0x80,
  0x70, 0x7C, 0x06, 0x00, 0xE0, 0x60, 0x02, 0x07, 0x00, 0x10, 0x30, 0x00,
  0x01, 0x80, 0x00, 0x0C, 0x00, 0x00, 0x70, 0x00, 0x01, 0xF0, 0x00, 0x07,
  0xF8, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x18,
  0x00, 0x00, 0xC2, 0x00, 0x06, 0x30, 0x00, 0x61, 0x80, 0x03, 0x1E, 0x00,
  0x30, 0xFC, 0x07, 0x06, 0x7F, 0xF0, 0x00, 0xFE, 0x00, 0x01, 0x80, 0x00,
  0x0C, 0x00, 0x00, 0x60, 0x00, 0x06, 0x00, 0x00, 0x30, 0x00, 0x01, 0x80,
  0x00, 0x00, 0x78, 0x00, 0x07, 0xF8, 0x00, 0x38, 0x60, 0x01, 0xC0, 0xC0,
  0x06, 0x03, 0x00, 0x30, 0x0C, 0x00, 0xC0, 0x30, 0x03, 0x01, 0x80, 0x0C,
  0x0E, 0x00, 0x38, 0x70, 0x00, 0x7F, 0x81, 0xC0, 0xF8, 0x3F, 0x00, 0x07,
  0xC0, 0x01, 0xF8, 0x00, 0x3F, 0x00, 0x07, 0xC0, 0x00, 0x78, 0x00, 0x01,
  0x00, 0x78, 0x00, 0x07, 0xF8, 0x00, 0x38, 0x60, 0x01, 0x80, 0xC0, 0x06,
  0x03, 0x00, 0x30, 0x0C, 0x00, 0xC0, 0x30, 0x03, 0x01, 0x80, 0x0C, 0x0E,
  0x00, 0x18, 0x70, 0x00, 0x7F, 0x80, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00,
  0x0F, 0xF8, 0x03, 0x8E, 0x00, 0xC0, 0x00, 0x38, 0x00, 0x06, 0x00, 0x00,
  0xC0, 0x00, 0x18, 0x00, 0x01, 0x00, 0x00, 0x30, 0x00, 0x06, 0x00, 0x03,
  0xE0, 0x01, 0xCC, 0x0E, 0x60, 0xC3, 0xD8, 0x18, 0x63, 0x03, 0x18, 0xC0,
  0x33, 0x18, 0x06, 0xC3, 0x00, 0x70, 0x60, 0x0E, 0x0C, 0x01, 0xC0, 0xC0,
  0x78, 0x1C, 0x3B, 0xE1, 0xFE, 0x3C, 0x1F, 0x00, 0x00, 0x7E, 0xFD, 0xF3,
  0xE7, 0xCF, 0x3E, 0x78, 0xF1, 0xE3, 0x87, 0x0E, 0x18, 0x00, 0x00, 0x60,
  0x18, 0x07, 0x00, 0xC0, 0x30, 0x0E, 0x01, 0x80, 0x70, 0x0C, 0x03, 0x80,
  0x60, 0x1C, 0x03, 0x80, 0xE0, 0x1C, 0x03, 0x80, 0xF0, 0x1C, 0x03, 0x80,
  0x70, 0x0E, 0x01, 0xC0, 0x38, 0x07, 0x00, 0xE0, 0x1C, 0x03, 0x80, 0x30,
  0x06, 0x00, 0xC0, 0x1C, 0x01, 0x80, 0x30, 0x02, 0x00, 0x01, 0x80, 0x30,
  0x06, 0x00, 0xE0, 0x0C, 0x01, 0x80, 0x30, 0x07, 0x00, 0xE0, 0x1C, 0x03,
  0x80, 0x70, 0x0E, 0x01, 0xC0, 0x38, 0x07, 0x00, 0xE0, 0x38, 0x07, 0x00,
  0xE0, 0x3C, 0x07, 0x00, 0xE0, 0x38, 0x07, 0x01, 0xC0, 0x38, 0x0E, 0x01,
  0x80, 0x70, 0x0C, 0x03, 0x00, 0xC0, 0x10, 0x00, 0x00, 0x20, 0x00, 0x18,
  0x00, 0x06, 0x00, 0x01, 0x80, 0x00, 0xC0, 0x00, 0x30, 0x0E, 0x0C, 0x0B,
  0xF3, 0x3E, 0x3F, 0xFE, 0x01, 0xFC, 0x00, 0x3C, 0x00, 0x1F, 0x00, 0x0E,
  0x60, 0x07, 0x18, 0x01, 0x83, 0x00, 0xC0, 0xC0, 0x60, 0x30, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x30, 0x00, 0x00, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x60, 0x00, 0x01, 0x80, 0x00, 0x06, 0x00, 0x00, 0x30, 0x00,
  0x00, 0xC0, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x30, 0x00, 0x01,
  0x80, 0x00, 0x06, 0x00, 0x00, 0x18, 0x00, 0x00, 0x60, 0x00, 0x01, 0x80,
  0x00, 0x0C, 0x00, 0x00, 0x30, 0x00, 0x00, 0xC0, 0x00, 0x03, 0x00, 0x00,
  0x03, 0xF0, 0x7E, 0x07, 0xC0, 0xF8, 0x0F, 0x81, 0xF0, 0x1E, 0x03, 0xE0,
  0x3C, 0x07, 0x80, 0x70, 0x0F, 0x00, 0xE0, 0x0C, 0x00, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xE0, 0x3C, 0xFF, 0xFF, 0xFF, 0xCF, 0x00, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x06, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x60,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x03, 0x00,
  0x00, 0x07, 0x00, 0x00, 0x06, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x1C, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0x70, 0x00, 0x00, 0x60, 0x00,
  0x00, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x03, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x18, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0x60, 0x00, 0x00, 0xE0, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x0F, 0xF8, 0x01,
  0xC1, 0xC0, 0x38, 0x0E, 0x07, 0x00, 0x60, 0xE0, 0x03, 0x0C, 0x00, 0x31,
  0x80, 0x03, 0x18, 0x00, 0x33, 0x00, 0x03, 0x30, 0x00, 0x33, 0x00, 0x03,
  0x20, 0x00, 0x26, 0x00, 0x06, 0x60, 0x00, 0x66, 0x00, 0x06, 0x40, 0x00,
  0x4C, 0x00, 0x0C, 0xC0, 0x00, 0xCC, 0x00, 0x0C, 0xC0, 0x01, 0x8C, 0x00,
  0x18, 0xC0, 0x01, 0x8C, 0x00, 0x30, 0xC0, 0x07, 0x06, 0x00, 0xE0, 0x60,
  0x1C, 0x03, 0x87, 0x80, 0x3F, 0xF0, 0x00, 0xFC, 0x00, 0x00, 0x0E, 0x00,
  0x0F, 0x00, 0x0F, 0x80, 0x0E, 0xC0, 0x1C, 0xC0, 0x1C, 0x60, 0x1C, 0x30,
  0x08, 0x18, 0x00, 0x1C, 0x00, 0x0C, 0x00, 0x06, 0x00, 0x03, 0x00, 0x01,
  0x80, 0x01, 0xC0, 0x00, 0xC0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00,
  0x18, 0x00, 0x0C, 0x00, 0x06, 0x00, 0x03, 0x00, 0x01, 0x80, 0x01, 0x80,
  0x00, 0xC0, 0x00, 0x60, 0x00, 0x30, 0x1F, 0xFF, 0xFF, 0xFF, 0xF8, 0x00,
  0x07, 0xE0, 0x00, 0x3F, 0xE0, 0x01, 0xE0, 0xE0, 0x07, 0x00, 0xE0, 0x1C,
  0x00, 0xE0, 0x30, 0x00, 0xC0, 0xC0, 0x01, 0x81, 0x00, 0x03, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0xC0, 0x00, 0x03,
  0x00, 0x00, 0x1C, 0x00, 0x00, 0x70, 0x00, 0x01, 0xC0, 0x00, 0x07, 0x00,
  0x00, 0x38, 0x00, 0x00, 0xE0, 0x00, 0x03, 0x80, 0x00, 0x0E, 0x00, 0x00,
  0x70, 0x00, 0x01, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x3C, 0x00, 0x00, 0xE0,
  0x00, 0xC3, 0x80, 0x01, 0x87, 0xFF, 0xFF, 0x0F, 0xFF, 0xFC, 0x00, 0x00,
  0x0F, 0xC0, 0x01, 0xFF, 0xC0, 0x1E, 0x07, 0x80, 0xE0, 0x06, 0x03, 0x00,
  0x0C, 0x00, 0x00, 0x30, 0x00, 0x00, 0xC0, 0x00, 0x03, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x60, 0x00, 0x03, 0x80, 0x00, 0x1C, 0x00, 0x00, 0xE0, 0x00,
  0xFE, 0x00, 0x03, 0xF0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0xE0, 0x00, 0x01,
  0x80, 0x00, 0x03, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x30, 0x00, 0x00, 0xC0,
  0x00, 0x03, 0x00, 0x00, 0x18, 0x00, 0x00, 0xE3, 0x00, 0x07, 0x0E, 0x00,
  0x38, 0x1E, 0x03, 0xC0, 0x3F, 0xFC, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x03,
  0xE0, 0x00, 0xF8, 0x00, 0x1B, 0x00, 0x06, 0x60, 0x01, 0x8C, 0x00, 0x63,
  0x00, 0x18, 0x60, 0x07, 0x0C, 0x00, 0xC1, 0x80, 0x30, 0x30, 0x0C, 0x0C,
  0x03, 0x01, 0x80, 0xC0, 0x30, 0x18, 0x06, 0x06, 0x00, 0xC1, 0x80, 0x30,
  0x60, 0x06, 0x18, 0x00, 0xC3, 0xFF, 0xFE, 0x7F, 0xFF, 0xC0, 0x00, 0xC0,
  0x00, 0x18, 0x00, 0x03, 0x00, 0x00, 0x60, 0x00, 0x18, 0x00, 0x03, 0x00,
  0x0F, 0xFC, 0x01, 0xFF, 0x80, 0x01, 0xFF, 0xF8, 0x0F, 0xFF, 0xC0, 0x40,
  0x00, 0x06, 0x00, 0x00, 0x30, 0x00, 0x01, 0x80, 0x00, 0x0C, 0x00, 0x00,
  0xC0, 0x00, 0x06, 0x00, 0x00, 0x30, 0x00, 0x01, 0xBF, 0xC0, 0x0F, 0xFF,
  0x80, 0xF8, 0x1E, 0x02, 0x00, 0x30, 0x00, 0x01, 0xC0, 0x00, 0x06, 0x00,
  0x00, 0x30, 0x00, 0x01, 0x80, 0x00, 0x0C, 0x00, 0x00, 0x60, 0x00, 0x06,
  0x00, 0x00, 0x30, 0x00, 0x03, 0x80, 0x00, 0x18, 0xC0, 0x01, 0x87, 0x00,
  0x38, 0x1E, 0x07, 0x80, 0x7F, 0xF8, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x03,
  0xF0, 0x00, 0xFF, 0xC0, 0x1F, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00, 0x01,
  0x80, 0x00, 0x18, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00, 0x00, 0xC0, 0x00,
  0x0E, 0x00, 0x00, 0x60, 0x00, 0x07, 0x0F, 0x80, 0x31, 0xFF, 0x01, 0x9C,
  0x3C, 0x0D, 0x80, 0x60, 0xD8, 0x03, 0x87, 0x80, 0x0C, 0x38, 0x00, 0x61,
  0xC0, 0x03, 0x0C, 0x00, 0x18, 0x60, 0x00, 0xC3, 0x00, 0x0C, 0x18, 0x00,
  0x60, 0xE0, 0x06, 0x03, 0x00, 0x30, 0x1C, 0x07, 0x00, 0x70, 0x70, 0x01,
  0xFF, 0x00, 0x07, 0xE0, 0x00, 0x7F, 0xFF, 0xDF, 0xFF, 0xFC, 0x00, 0x0F,
  0x00, 0x03, 0x00, 0x01, 0x80, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00,
  0x06, 0x00, 0x03, 0x00, 0x00, 0xC0, 0x00, 0x60, 0x00, 0x18, 0x00, 0x0C,
  0x00, 0x03, 0x00, 0x01, 0x80, 0x00, 0x60, 0x00, 0x30, 0x00, 0x0C, 0x00,
  0x06, 0x00, 0x01, 0x80, 0x00, 0xC0, 0x00, 0x60, 0x00, 0x18, 0x00, 0x0C,
  0x00, 0x03, 0x00, 0x01, 0x80, 0x00, 0x60, 0x00, 0x00, 0x3F, 0x00, 0x0F,
  0xFC, 0x01, 0xC1, 0xE0, 0x70, 0x06, 0x06, 0x00, 0x30, 0xC0, 0x03, 0x1C,
  0x00, 0x31, 0x80, 0x03, 0x18, 0x00, 0x31, 0x80, 0x06, 0x18, 0x00, 0xE0,
  0xC0, 0x1C, 0x0F, 0x07, 0x80, 0x3F, 0xE0, 0x03, 0xFE, 0x00, 0xE0, 0x70,
  0x18, 0x03, 0x83, 0x00, 0x1C, 0x60, 0x00, 0xC6, 0x00, 0x0C, 0xC0, 0x00,
  0xCC, 0x00, 0x0C, 0xC0, 0x00, 0xCC, 0x00, 0x18, 0xC0, 0x03, 0x8E, 0x00,
  0x70, 0x60, 0x0E, 0x07, 0x83, 0xC0, 0x3F, 0xF0, 0x00, 0xFC, 0x00, 0x00,
  0x0F, 0x80, 0x00, 0xFF, 0x80, 0x0F, 0x07, 0x00, 0x70, 0x0E, 0x03, 0x80,
  0x18, 0x0C, 0x00, 0x70, 0x60, 0x00, 0xC1, 0x80, 0x03, 0x0C, 0x00, 0x0C,
  0x30, 0x00, 0x30, 0xC0, 0x01, 0xC3, 0x00, 0x0F, 0x0C, 0x00, 0x6C, 0x38,
  0x03, 0xF0, 0x60, 0x1D, 0x81, 0xE1, 0xE6, 0x03, 0xFE, 0x18, 0x03, 0xE0,
  0xC0, 0x00, 0x03, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x60, 0x00, 0x03, 0x00,
  0x00, 0x1C, 0x00, 0x00, 0xE0, 0x00, 0x07, 0x00, 0x00, 0x38, 0x00, 0x03,
  0xC0, 0x00, 0x7C, 0x00, 0xFF, 0xC0, 0x01, 0xF8, 0x00, 0x00, 0x07, 0x83,
  0xF1, 0xFC, 0x7F, 0x1F, 0x83, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x7E, 0x3F, 0x8F, 0xE3, 0xF0, 0x78,
  0x00, 0x00, 0x3C, 0x00, 0xFC, 0x03, 0xF8, 0x07, 0xF0, 0x0F, 0xC0, 0x0F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x7E, 0x00, 0xFC, 0x03, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0x3E,
  0x00, 0xF8, 0x01, 0xE0, 0x07, 0x80, 0x0F, 0x00, 0x3C, 0x00, 0x70, 0x01,
  0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x3C, 0x00, 0x01,
  0xE0, 0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00,
  0x00, 0xF0, 0x00, 0x07, 0x80, 0x00, 0x3C, 0x00, 0x01, 0xE0, 0x00, 0x03,
  0xC0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xF0, 0x00, 0x00,
  0x70, 0x00, 0x00, 0x78, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00, 0x00,
  0x1E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x0C, 0x00, 0x3F, 0xFF, 0xFF, 0x9F,
  0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFE, 0x7F, 0xFF, 0xFF,
  0x00, 0x06, 0x00, 0x00, 0x07, 0x80, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xF0,
  0x00, 0x00, 0x3C, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x07, 0x80, 0x00, 0x01,
  0xC0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x1E, 0x00, 0x00,
  0x3C, 0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x3C,
  0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x3C, 0x00,
  0x00, 0x70, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x07, 0xF0, 0x3F, 0xFC, 0x78,
  0x1E, 0xC0, 0x07, 0xC0, 0x03, 0xC0, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00,
  0x06, 0x00, 0x06, 0x00, 0x1C, 0x00, 0x38, 0x00, 0xE0, 0x07, 0xC0, 0x07,
  0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x7E, 0x00, 0xFE, 0x00, 0xFE,
  0x00, 0x7C, 0x00, 0x00, 0x3F, 0x00, 0x1F, 0xF0, 0x07, 0x07, 0x01, 0xC0,
  0x70, 0x60, 0x06, 0x1C, 0x00, 0xC3, 0x00, 0x18, 0xC0, 0x03, 0x18, 0x00,
  0x66, 0x00, 0xFC, 0xC0, 0x7F, 0x98, 0x1C, 0x66, 0x06, 0x0C, 0xC1, 0x81,
  0x98, 0x30, 0x33, 0x0C, 0x0E, 0x61, 0x81, 0x98, 0x30, 0x33, 0x06, 0x06,
  0x60, 0xF0, 0xCC, 0x0F, 0xF9, 0x80, 0x7F, 0x30, 0x00, 0x06, 0x00, 0x00,
  0xC0, 0x00, 0x18, 0x00, 0x03, 0x80, 0x00, 0x30, 0x00, 0x07, 0x00, 0x00,
  0x70, 0x18, 0x0F, 0xFE, 0x00, 0x7F, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x0F,
  0xFE, 0x00, 0x00, 0x06, 0xC0, 0x00, 0x00, 0xCC, 0x00, 0x00, 0x31, 0x80,
  0x00, 0x06, 0x30, 0x00, 0x01, 0x86, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x0C,
  0x1C, 0x00, 0x03, 0x01, 0x80, 0x00, 0x40, 0x30, 0x00, 0x18, 0x06, 0x00,
  0x06, 0x00, 0xC0, 0x00, 0xC0, 0x18, 0x00, 0x30, 0x01, 0x80, 0x07, 0xFF,
  0xF0, 0x01, 0xFF, 0xFE, 0x00, 0x60, 0x00, 0xC0, 0x0C, 0x00, 0x18, 0x03,
  0x00, 0x03, 0x00, 0x40, 0x00, 0x30, 0x18, 0x00, 0x06, 0x06, 0x00, 0x00,
  0xC0, 0xC0, 0x00, 0x18, 0xFF, 0x80, 0x7F, 0xFF, 0xF0, 0x0F, 0xFC, 0x03,
  0xFF, 0xFC, 0x01, 0xFF, 0xFF, 0xC0, 0x06, 0x00, 0x38, 0x01, 0x80, 0x07,
  0x00, 0xC0, 0x00, 0xC0, 0x30, 0x00, 0x30, 0x0C, 0x00, 0x0C, 0x03, 0x00,
  0x03, 0x00, 0xC0, 0x01, 0x80, 0x60, 0x00, 0xC0, 0x18, 0x01, 0xE0, 0x07,
  0xFF, 0xE0, 0x01, 0xFF, 0xFC, 0x00, 0xE0, 0x03, 0x80, 0x30, 0x00, 0x70,
  0x0C, 0x00, 0x0E, 0x03, 0x00, 0x01, 0x80, 0xC0, 0x00, 0x60, 0x60, 0x00,
  0x18, 0x18, 0x00, 0x06, 0x06, 0x00, 0x03, 0x01, 0x80, 0x01, 0xC0, 0x60,
  0x00, 0xE0, 0x30, 0x00, 0x70, 0xFF, 0xFF, 0xF8, 0x3F, 0xFF, 0xF8, 0x00,
  0x00, 0x0F, 0xE0, 0x00, 0x3F, 0xFC, 0xC0, 0x3C, 0x0F, 0x60, 0x78, 0x01,
  0xF0, 0x70, 0x00, 0x70, 0x70, 0x00, 0x18, 0x30, 0x00, 0x0C, 0x30, 0x00,
  0x06, 0x38, 0x00, 0x02, 0x18, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x00, 0x60,
  0x60, 0x00, 0x60, 0x38, 0x00, 0xE0, 0x0F, 0x01, 0xE0, 0x03, 0xFF, 0xC0,
  0x00, 0x3F, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0x00, 0x0C,
  0x00, 0xF0, 0x03, 0x00, 0x1C, 0x01, 0xC0, 0x03, 0x80, 0x60, 0x00, 0x60,
  0x18, 0x00, 0x1C, 0x06, 0x00, 0x03, 0x01, 0x80, 0x00, 0xC0, 0xC0, 0x00,
  0x30, 0x30, 0x00, 0x0C, 0x0C, 0x00, 0x03, 0x03, 0x00, 0x00, 0xC0, 0xC0,
  0x00, 0x60, 0x60, 0x00, 0x18, 0x18, 0x00, 0x06, 0x06, 0x00, 0x03, 0x01,
  0x80, 0x00, 0xC0, 0xE0, 0x00, 0x70, 0x30, 0x00, 0x18, 0x0C, 0x00, 0x0C,
  0x03, 0x00, 0x06, 0x00, 0xC0, 0x07, 0x00, 0x60, 0x07, 0x80, 0xFF, 0xFF,
  0xC0, 0x3F, 0xFF, 0x80, 0x00, 0x03, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFC,
  0x01, 0x80, 0x01, 0x80, 0x30, 0x00, 0x60, 0x0C, 0x00, 0x0C, 0x01, 0x80,
  0x01, 0x80, 0x30, 0x00, 0x30, 0x06, 0x00, 0x00, 0x00, 0xC0, 0xC0, 0x00,
  0x30, 0x18, 0x00, 0x06, 0x03, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x1F, 0xF8,
  0x00, 0x07, 0x03, 0x00, 0x00, 0xC0, 0x60, 0x00, 0x18, 0x0C, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x18, 0x00, 0x0C, 0x03, 0x00, 0x01,
  0x80, 0x60, 0x00, 0x30, 0x0C, 0x00, 0x0C, 0x01, 0x80, 0x01, 0x80, 0x60,
  0x00, 0x30, 0xFF, 0xFF, 0xFE, 0x1F, 0xFF, 0xFF, 0xC0, 0x03, 0xFF, 0xFF,
  0xF0, 0x7F, 0xFF, 0xFF, 0x00, 0x60, 0x00, 0x30, 0x06, 0x00, 0x06, 0x00,
  0xC0, 0x00, 0x60, 0x0C, 0x00, 0x06, 0x00, 0xC0, 0x00, 0x60, 0x0C, 0x00,
  0x00, 0x00, 0xC0, 0xC0, 0x00, 0x18, 0x0C, 0x00, 0x01, 0x80, 0xC0, 0x00,
  0x1F, 0xFC, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x38, 0x18, 0x00, 0x03, 0x01,
  0x80, 0x00, 0x30, 0x18, 0x00, 0x03, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xFF, 0xFC, 0x00,
  0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x3F, 0xFC, 0xC0, 0x3C,
  0x0F, 0xE0, 0x78, 0x01, 0xF0, 0x70, 0x00, 0x30, 0x70, 0x00, 0x18, 0x70,
  0x00, 0x0C, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x01, 0x80, 0x1F, 0xFE, 0xC0, 0x0F, 0xFF, 0x60, 0x00, 0x06,
  0x30, 0x00, 0x06, 0x18, 0x00, 0x03, 0x0C, 0x00, 0x01, 0x87, 0x00, 0x00,
  0xC1, 0x80, 0x00, 0xE0, 0xE0, 0x00, 0x60, 0x38, 0x00, 0x70, 0x0F, 0x00,
  0xF8, 0x03, 0xFF, 0xF0, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xFC, 0x1F, 0xE0,
  0x7F, 0x83, 0xFC, 0x03, 0x00, 0x06, 0x00, 0x60, 0x01, 0x80, 0x1C, 0x00,
  0x30, 0x03, 0x00, 0x06, 0x00, 0x60, 0x00, 0xC0, 0x0C, 0x00, 0x38, 0x01,
  0x80, 0x06, 0x00, 0x60, 0x00, 0xC0, 0x0C, 0x00, 0x18, 0x01, 0xFF, 0xFF,
  0x00, 0x3F, 0xFF, 0xC0, 0x06, 0x00, 0x18, 0x01, 0x80, 0x03, 0x00, 0x30,
  0x00, 0x60, 0x06, 0x00, 0x0C, 0x00, 0xC0, 0x03, 0x00, 0x38, 0x00, 0x60,
  0x06, 0x00, 0x0C, 0x00, 0xC0, 0x01, 0x80, 0x18, 0x00, 0x70, 0x03, 0x00,
  0x0C, 0x00, 0xE0, 0x01, 0x80, 0xFF, 0x83, 0xFE, 0x1F, 0xF0, 0x7F, 0xC0,
  0x07, 0xFF, 0xFC, 0x1F, 0xFF, 0xF0, 0x00, 0xC0, 0x00, 0x03, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x70, 0x00, 0x01, 0x80, 0x00, 0x06, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x30, 0x00,
  0x00, 0xC0, 0x00, 0x03, 0x00, 0x00, 0x18, 0x00, 0x00, 0x60, 0x00, 0x01,
  0x80, 0x00, 0x06, 0x00, 0x00, 0x38, 0x00, 0x00, 0xC0, 0x00, 0x03, 0x00,
  0x00, 0x0C, 0x00, 0x00, 0x30, 0x00, 0xFF, 0xFF, 0x83, 0xFF, 0xFE, 0x00,
  0x00, 0x0F, 0xFF, 0xF0, 0x01, 0xFF, 0xFF, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x18, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x18,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x38, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x07,
  0x00, 0x20, 0x00, 0x60, 0x06, 0x00, 0x06, 0x00, 0x60, 0x00, 0x60, 0x06,
  0x00, 0x06, 0x00, 0x60, 0x00, 0xC0, 0x0C, 0x00, 0x0C, 0x00, 0xC0, 0x00,
  0xC0, 0x0C, 0x00, 0x18, 0x00, 0xE0, 0x03, 0x00, 0x07, 0x00, 0x70, 0x00,
  0x3C, 0x1C, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x03,
  0xFF, 0x07, 0xF8, 0x3F, 0xF8, 0x3F, 0xC0, 0x18, 0x00, 0x70, 0x00, 0xC0,
  0x07, 0x00, 0x0C, 0x00, 0x60, 0x00, 0x60, 0x0E, 0x00, 0x03, 0x00, 0xE0,
  0x00, 0x18, 0x0C, 0x00, 0x00, 0xC1, 0xC0, 0x00, 0x0C, 0x1C, 0x00, 0x00,
  0x61, 0x80, 0x00, 0x03, 0x3C, 0x00, 0x00, 0x1B, 0x78, 0x00, 0x01, 0xF0,
  0xE0, 0x00, 0x0F, 0x03, 0x80, 0x00, 0x60, 0x0C, 0x00, 0x03, 0x00, 0x70,
  0x00, 0x18, 0x01, 0x80, 0x01, 0x80, 0x0C, 0x00, 0x0C, 0x00, 0x60, 0x00,
  0x60, 0x01, 0x80, 0x03, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x60, 0x01, 0x80,
  0x03, 0x00, 0xFF, 0xE0, 0x1F, 0x87, 0xFF, 0x00, 0x7C, 0x00, 0x07, 0xFF,
  0xE0, 0x03, 0xFF, 0xF0, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x70, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x00,
  0x00, 0xC0, 0x03, 0x00, 0x60, 0x01, 0x80, 0x60, 0x00, 0xC0, 0x30, 0x00,
  0x60, 0x18, 0x00, 0x30, 0x0C, 0x00, 0x30, 0x0E, 0x00, 0x18, 0x06, 0x00,
  0x0C, 0xFF, 0xFF, 0xFE, 0x7F, 0xFF, 0xFF, 0x00, 0x07, 0xF0, 0x00, 0x3F,
  0x07, 0xF0, 0x00, 0x7F, 0x01, 0xB0, 0x00, 0xD8, 0x01, 0xB0, 0x00, 0xD8,
  0x01, 0x98, 0x01, 0x98, 0x01, 0x98, 0x03, 0x30, 0x01, 0x98, 0x03, 0x30,
  0x03, 0x18, 0x06, 0x30, 0x03, 0x1C, 0x0C, 0x30, 0x03, 0x0C, 0x0C, 0x30,
  0x03, 0x0C, 0x18, 0x60, 0x07, 0x0C, 0x30, 0x60, 0x06, 0x0C, 0x30, 0x60,
  0x06, 0x06, 0x60, 0x60, 0x06, 0x06, 0xC0, 0x60, 0x06, 0x06, 0xC0, 0xC0,
  0x0C, 0x07, 0x80, 0xC0, 0x0C, 0x03, 0x00, 0xC0, 0x0C, 0x00, 0x00, 0xC0,
  0x0C, 0x00, 0x01, 0xC0, 0x0C, 0x00, 0x01, 0x80, 0x18, 0x00, 0x01, 0x80,
  0x18, 0x00, 0x01, 0x80, 0x18, 0x00, 0x01, 0x80, 0xFF, 0x80, 0x3F, 0xE0,
  0xFF, 0x80, 0x3F, 0xE0, 0x07, 0xE0, 0x0F, 0xFC, 0x3F, 0x80, 0x3F, 0xF0,
  0x0F, 0x00, 0x06, 0x00, 0x3C, 0x00, 0x10, 0x01, 0x98, 0x00, 0xC0, 0x06,
  0x60, 0x03, 0x00, 0x19, 0xC0, 0x0C, 0x00, 0x63, 0x00, 0x30, 0x01, 0x0C,
  0x01, 0x80, 0x0C, 0x18, 0x06, 0x00, 0x30, 0x60, 0x18, 0x00, 0xC1, 0xC0,
  0x60, 0x03, 0x03, 0x01, 0x00, 0x08, 0x0C, 0x0C, 0x00, 0x60, 0x18, 0x30,
  0x01, 0x80, 0x60, 0xC0, 0x06, 0x01, 0xC3, 0x00, 0x18, 0x03, 0x18, 0x00,
  0xC0, 0x0C, 0x60, 0x03, 0x00, 0x19, 0x80, 0x0C, 0x00, 0x66, 0x00, 0x30,
  0x01, 0xD8, 0x00, 0x80, 0x03, 0xC0, 0x06, 0x00, 0x0F, 0x00, 0xFF, 0xC0,
  0x1C, 0x03, 0xFE, 0x00, 0x70, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x7F, 0xF0,
  0x00, 0xF0, 0x78, 0x03, 0x80, 0x1C, 0x07, 0x00, 0x0E, 0x0E, 0x00, 0x06,
  0x0C, 0x00, 0x06, 0x18, 0x00, 0x07, 0x38, 0x00, 0x03, 0x30, 0x00, 0x03,
  0x60, 0x00, 0x03, 0x60, 0x00, 0x03, 0x60, 0x00, 0x03, 0xC0, 0x00, 0x03,
  0xC0, 0x00, 0x03, 0xC0, 0x00, 0x06, 0xC0, 0x00, 0x06, 0xC0, 0x00, 0x06,
  0xC0, 0x00, 0x0C, 0xC0, 0x00, 0x1C, 0xC0, 0x00, 0x18, 0x60, 0x00, 0x30,
  0x60, 0x00, 0x70, 0x70, 0x00, 0xE0, 0x38, 0x01, 0xC0, 0x1E, 0x0F, 0x00,
  0x0F, 0xFE, 0x00, 0x03, 0xF0, 0x00, 0x03, 0xFF, 0xFC, 0x01, 0xFF, 0xFF,
  0xC0, 0x06, 0x00, 0x78, 0x01, 0x80, 0x06, 0x00, 0xC0, 0x01, 0xC0, 0x30,
  0x00, 0x30, 0x0C, 0x00, 0x0C, 0x03, 0x00, 0x03, 0x00, 0xC0, 0x01, 0xC0,
  0x60, 0x00, 0x60, 0x18, 0x00, 0x30, 0x06, 0x00, 0x18, 0x01, 0x80, 0x3C,
  0x00, 0xFF, 0xFE, 0x00, 0x3F, 0xFC, 0x00, 0x0C, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0x60, 0x00, 0x00, 0x18, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x60, 0x00, 0x00, 0x30, 0x00, 0x00,
  0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x7F,
  0xF0, 0x00, 0xF0, 0x78, 0x03, 0x80, 0x1C, 0x07, 0x00, 0x0E, 0x0E, 0x00,
  0x06, 0x0C, 0x00, 0x06, 0x18, 0x00, 0x03, 0x38, 0x00, 0x03, 0x30, 0x00,
  0x03, 0x60, 0x00, 0x03, 0x60, 0x00, 0x03, 0x60, 0x00, 0x03, 0xC0, 0x00,
  0x03, 0xC0, 0x00, 0x03, 0xC0, 0x00, 0x06, 0xC0, 0x00, 0x06, 0xC0, 0x00,
  0x06, 0xC0, 0x00, 0x0C, 0xC0, 0x00, 0x1C, 0xC0, 0x00, 0x18, 0x60, 0x00,
  0x30, 0x60, 0x00, 0x70, 0x30, 0x00, 0xE0, 0x38, 0x01, 0xC0, 0x0E, 0x0F,
  0x00, 0x07, 0xFE, 0x00, 0x03, 0xF0, 0x00, 0x0F, 0x00, 0x00, 0x1F, 0xF8,
  0x30, 0x3F, 0xFF, 0xF0, 0x78, 0x0F, 0x80, 0x07, 0xFF, 0xFC, 0x01, 0xFF,
  0xFF, 0xC0, 0x06, 0x00, 0x78, 0x01, 0x80, 0x0E, 0x00, 0xC0, 0x01, 0xC0,
  0x30, 0x00, 0x30, 0x0C, 0x00, 0x0C, 0x03, 0x00, 0x03, 0x00, 0xC0, 0x00,
  0xC0, 0x60, 0x00, 0x60, 0x18, 0x00, 0x30, 0x06, 0x00, 0x38, 0x01, 0x80,
  0x3C, 0x00, 0xFF, 0xFC, 0x00, 0x3F, 0xFC, 0x00, 0x0C, 0x07, 0x80, 0x03,
  0x00, 0x70, 0x00, 0xC0, 0x0E, 0x00, 0x60, 0x01, 0x80, 0x18, 0x00, 0x70,
  0x06, 0x00, 0x0C, 0x01, 0x80, 0x03, 0x80, 0x60, 0x00, 0x60, 0x30, 0x00,
  0x1C, 0xFF, 0xE0, 0x07, 0xFF, 0xF8, 0x00, 0xF0, 0x00, 0x1F, 0xC0, 0x00,
  0x7F, 0xF3, 0x00, 0xE0, 0x3B, 0x03, 0x80, 0x0F, 0x07, 0x00, 0x0E, 0x06,
  0x00, 0x06, 0x0C, 0x00, 0x06, 0x0C, 0x00, 0x06, 0x0C, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x0E, 0x00, 0x00, 0x07, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00,
  0x7F, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x70, 0x00, 0x00, 0x38, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x18, 0x20, 0x00, 0x18, 0x60, 0x00, 0x18, 0x60,
  0x00, 0x30, 0x60, 0x00, 0x70, 0xF0, 0x00, 0xE0, 0xF8, 0x01, 0xC0, 0xDC,
  0x07, 0x80, 0x8F, 0xFE, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0xFF, 0xFE, 0x3F,
  0xFF, 0xFE, 0x30, 0x18, 0x06, 0x60, 0x18, 0x06, 0x60, 0x18, 0x06, 0x60,
  0x38, 0x0C, 0x60, 0x30, 0x04, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x70, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x01,
  0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0xFF, 0xFE, 0x00, 0xFF,
  0xFC, 0x00, 0x7F, 0xC0, 0xFF, 0xDF, 0xF0, 0x3F, 0xF1, 0x80, 0x00, 0x60,
  0x60, 0x00, 0x30, 0x18, 0x00, 0x0C, 0x06, 0x00, 0x03, 0x03, 0x80, 0x00,
  0xC0, 0xC0, 0x00, 0x30, 0x30, 0x00, 0x18, 0x0C, 0x00, 0x06, 0x03, 0x00,
  0x01, 0x81, 0xC0, 0x00, 0x60, 0x60, 0x00, 0x18, 0x18, 0x00, 0x0C, 0x06,
  0x00, 0x03, 0x01, 0x80, 0x00, 0xC0, 0xC0, 0x00, 0x30, 0x30, 0x00, 0x1C,
  0x0C, 0x00, 0x06, 0x03, 0x00, 0x01, 0x80, 0xC0, 0x00, 0xC0, 0x30, 0x00,
  0x70, 0x0E, 0x00, 0x38, 0x01, 0xC0, 0x1C, 0x00, 0x38, 0x1E, 0x00, 0x07,
  0xFE, 0x00, 0x00, 0x7E, 0x00, 0x00, 0xFF, 0x80, 0x3F, 0xFF, 0xF0, 0x07,
  0xFC, 0xE0, 0x00, 0x0C, 0x0C, 0x00, 0x03, 0x01, 0x80, 0x00, 0x60, 0x30,
  0x00, 0x18, 0x06, 0x00, 0x02, 0x00, 0xC0, 0x00, 0xC0, 0x0C, 0x00, 0x30,
  0x01, 0x80, 0x06, 0x00, 0x30, 0x01, 0x80, 0x06, 0x00, 0x60, 0x00, 0xC0,
  0x0C, 0x00, 0x18, 0x03, 0x00, 0x01, 0x80, 0xC0, 0x00, 0x30, 0x18, 0x00,
  0x06, 0x06, 0x00, 0x00, 0xC0, 0xC0, 0x00, 0x18, 0x30, 0x00, 0x03, 0x8C,
  0x00, 0x00, 0x31, 0x80, 0x00, 0x06, 0x60, 0x00, 0x00, 0xD8, 0x00, 0x00,
  0x1B, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x38, 0x00, 0x00, 0xFF, 0xC0,
  0x7F, 0xFF, 0xF8, 0x0F, 0xF8, 0xC0, 0x00, 0x0C, 0x18, 0x00, 0x01, 0x83,
  0x00, 0x00, 0x30, 0x60, 0x08, 0x0C, 0x0C, 0x07, 0x01, 0x81, 0x81, 0xE0,
  0x30, 0x60, 0x2C, 0x0C, 0x0C, 0x0D, 0x81, 0x81, 0x81, 0x30, 0x30, 0x30,
  0x66, 0x0C, 0x06, 0x08, 0xC1, 0x80, 0xC3, 0x0C, 0x30, 0x18, 0x41, 0x8C,
  0x03, 0x18, 0x31, 0x80, 0x62, 0x06, 0x30, 0x0C, 0xC0, 0xCC, 0x03, 0x10,
  0x19, 0x80, 0x66, 0x03, 0x30, 0x0C, 0x80, 0x6C, 0x01, 0xB0, 0x0D, 0x80,
  0x34, 0x01, 0xB0, 0x07, 0x80, 0x3C, 0x00, 0xE0, 0x07, 0x80, 0x1C, 0x00,
  0xF0, 0x00, 0x03, 0xF8, 0x03, 0xF8, 0x1F, 0xC0, 0x3F, 0xC0, 0x30, 0x00,
  0x30, 0x01, 0xC0, 0x03, 0x00, 0x06, 0x00, 0x30, 0x00, 0x18, 0x03, 0x00,
  0x00, 0xE0, 0x30, 0x00, 0x03, 0x03, 0x00, 0x00, 0x1C, 0x30, 0x00, 0x00,
  0x63, 0x00, 0x00, 0x03, 0xB0, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x30,
  0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x36, 0x00, 0x00, 0x03, 0x38, 0x00,
  0x00, 0x30, 0xC0, 0x00, 0x03, 0x07, 0x00, 0x00, 0x30, 0x18, 0x00, 0x03,
  0x00, 0x60, 0x00, 0x30, 0x03, 0x80, 0x03, 0x00, 0x0C, 0x00, 0x30, 0x00,
  0x70, 0x03, 0x00, 0x01, 0x80, 0xFF, 0x80, 0xFF, 0x07, 0xFC, 0x07, 0xF8,
  0x00, 0x7F, 0x80, 0x7F, 0x7F, 0x00, 0x7F, 0x1C, 0x00, 0x18, 0x0C, 0x00,
  0x30, 0x0C, 0x00, 0x70, 0x06, 0x00, 0xE0, 0x06, 0x00, 0xC0, 0x03, 0x01,
  0x80, 0x03, 0x03, 0x00, 0x01, 0x86, 0x00, 0x01, 0x8C, 0x00, 0x00, 0xD8,
  0x00, 0x00, 0xF0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x60, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
  0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80,
  0x00, 0xFF, 0xFE, 0x00, 0xFF, 0xFC, 0x00, 0x03, 0xFF, 0xFE, 0x07, 0xFF,
  0xF8, 0x0C, 0x00, 0x30, 0x10, 0x00, 0xC0, 0x60, 0x03, 0x80, 0xC0, 0x0E,
  0x01, 0x80, 0x38, 0x03, 0x00, 0xE0, 0x00, 0x03, 0x80, 0x00, 0x0E, 0x00,
  0x00, 0x38, 0x00, 0x00, 0xE0, 0x00, 0x01, 0x80, 0x00, 0x06, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x60, 0x00, 0x01, 0x80, 0x00, 0x06, 0x00, 0x60, 0x18,
  0x00, 0xC0, 0x60, 0x01, 0x81, 0x80, 0x02, 0x06, 0x00, 0x0C, 0x18, 0x00,
  0x18, 0x60, 0x00, 0x30, 0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0x80, 0x01, 0xFE,
  0x03, 0xFC, 0x06, 0x00, 0x08, 0x00, 0x30, 0x00, 0x60, 0x00, 0xC0, 0x01,
  0x80, 0x06, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x30, 0x00, 0x40, 0x01, 0x80,
  0x03, 0x00, 0x06, 0x00, 0x0C, 0x00, 0x10, 0x00, 0x60, 0x00, 0xC0, 0x01,
  0x80, 0x03, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x30, 0x00, 0x60, 0x00, 0x80,
  0x03, 0x00, 0x06, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x20, 0x00, 0xFF, 0x01,
  0xFE, 0x00, 0xC0, 0x30, 0x0E, 0x01, 0x80, 0x60, 0x18, 0x07, 0x00, 0xC0,
  0x30, 0x0C, 0x03, 0x80, 0x60, 0x18, 0x06, 0x00, 0xC0, 0x30, 0x0C, 0x03,
  0x00, 0x60, 0x18, 0x06, 0x01, 0x80, 0x30, 0x0C, 0x03, 0x00, 0xC0, 0x18,
  0x06, 0x01, 0x80, 0x60, 0x0C, 0x03, 0x00, 0xC0, 0x30, 0x04, 0x01, 0xFE,
  0x03, 0xFC, 0x00, 0x10, 0x00, 0x60, 0x00, 0xC0, 0x01, 0x80, 0x03, 0x00,
  0x04, 0x00, 0x18, 0x00, 0x30, 0x00, 0x60, 0x00, 0xC0, 0x03, 0x00, 0x06,
  0x00, 0x0C, 0x00, 0x18, 0x00, 0x30, 0x00, 0xC0, 0x01, 0x80, 0x03, 0x00,
  0x06, 0x00, 0x08, 0x00, 0x30, 0x00, 0x60, 0x00, 0xC0, 0x01, 0x80, 0x06,
  0x00, 0x0C, 0x00, 0x18, 0x00, 0x30, 0x00, 0x60, 0x01, 0x80, 0xFF, 0x01,
  0xFE, 0x00, 0x00, 0x10, 0x00, 0x0C, 0x00, 0x07, 0x80, 0x03, 0x60, 0x01,
  0x8C, 0x00, 0xC3, 0x80, 0xE0, 0x60, 0x70, 0x1C, 0x38, 0x03, 0x1C, 0x00,
  0x6E, 0x00, 0x1F, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xC3, 0x86, 0x0C, 0x18, 0x70, 0xC0, 0x00, 0x3F, 0x80, 0x0F, 0xFF, 0x80,
  0x78, 0x07, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x18, 0x00, 0x00, 0x60, 0x00,
  0x01, 0x80, 0x00, 0x06, 0x00, 0x00, 0x38, 0x03, 0xFC, 0xC0, 0x7F, 0xFF,
  0x07, 0xC0, 0x0C, 0x38, 0x00, 0x31, 0xC0, 0x01, 0xCE, 0x00, 0x06, 0x30,
  0x00, 0x18, 0xC0, 0x00, 0xE3, 0x00, 0x07, 0x8E, 0x00, 0x7C, 0x1C, 0x0F,
  0x3F, 0x3F, 0xF0, 0xFC, 0x7F, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x7C,
  0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x30, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x10, 0x00, 0x00, 0x06, 0x07, 0xE0, 0x00, 0xC3,
  0xFF, 0x00, 0x19, 0xC0, 0xF0, 0x03, 0x60, 0x07, 0x00, 0xD8, 0x00, 0x60,
  0x1E, 0x00, 0x0E, 0x03, 0x80, 0x00, 0xC0, 0x60, 0x00, 0x18, 0x0C, 0x00,
  0x03, 0x03, 0x00, 0x00, 0x60, 0x60, 0x00, 0x0C, 0x0C, 0x00, 0x01, 0x81,
  0x80, 0x00, 0x60, 0x70, 0x00, 0x0C, 0x0E, 0x00, 0x03, 0x01, 0xC0, 0x00,
  0x60, 0x3C, 0x00, 0x18, 0x05, 0x80, 0x06, 0x01, 0xB8, 0x01, 0x83, 0xF3,
  0xC1, 0xE0, 0x7E, 0x3F, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x3F, 0x00,
  0x07, 0xFF, 0x30, 0x38, 0x0F, 0xC1, 0x80, 0x1F, 0x0C, 0x00, 0x18, 0x60,
  0x00, 0x63, 0x00, 0x01, 0x9C, 0x00, 0x06, 0x60, 0x00, 0x01, 0x80, 0x00,
  0x0C, 0x00, 0x00, 0x30, 0x00, 0x00, 0xC0, 0x00, 0x03, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x30, 0x00, 0x00, 0xE0, 0x00, 0x01, 0x80, 0x00, 0xC7, 0x00,
  0x0E, 0x0F, 0x01, 0xF0, 0x1F, 0xFF, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x00,
  0x1F, 0x80, 0x00, 0x0F, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x10, 0x00, 0x00, 0x18, 0x00, 0xFC, 0x0C, 0x01,
  0xFF, 0x86, 0x01, 0xC0, 0xE3, 0x03, 0x80, 0x1B, 0x03, 0x80, 0x05, 0x81,
  0x80, 0x03, 0xC1, 0x80, 0x00, 0xE1, 0x80, 0x00, 0x60, 0xC0, 0x00, 0x30,
  0x60, 0x00, 0x18, 0x60, 0x00, 0x0C, 0x30, 0x00, 0x06, 0x18, 0x00, 0x02,
  0x0C, 0x00, 0x03, 0x06, 0x00, 0x01, 0x83, 0x00, 0x01, 0xC1, 0xC0, 0x01,
  0xE0, 0x60, 0x01, 0xE0, 0x38, 0x01, 0xB0, 0x0F, 0x03, 0x9F, 0x03, 0xFF,
  0x0F, 0x80, 0x7E, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x07, 0xFF, 0x80, 0x78,
  0x0F, 0x03, 0x80, 0x0E, 0x1C, 0x00, 0x18, 0xE0, 0x00, 0x73, 0x00, 0x00,
  0xD8, 0x00, 0x03, 0x60, 0x00, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0x00, 0x00, 0xC0, 0x00, 0x03, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x38, 0x00,
  0x00, 0x60, 0x00, 0x01, 0xC0, 0x00, 0x03, 0x80, 0x03, 0x07, 0x80, 0xF8,
  0x0F, 0xFF, 0x80, 0x0F, 0xF0, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0xFF,
  0xF0, 0x00, 0xF0, 0x00, 0x00, 0x70, 0x00, 0x00, 0x18, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x07, 0xFF, 0xFC, 0x03,
  0xFF, 0xFF, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x60, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01,
  0x80, 0x00, 0x00, 0x60, 0x00, 0x00, 0x18, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x01, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xFF, 0xFC, 0x03, 0xFF, 0xFE,
  0x00, 0x00, 0x7E, 0x00, 0x00, 0xFF, 0x87, 0xC1, 0xE0, 0xF3, 0xE1, 0xC0,
  0x1B, 0x01, 0xC0, 0x07, 0x81, 0xC0, 0x03, 0xC0, 0xC0, 0x00, 0xE0, 0xC0,
  0x00, 0x60, 0x60, 0x00, 0x30, 0x60, 0x00, 0x18, 0x30, 0x00, 0x0C, 0x18,
  0x00, 0x06, 0x0C, 0x00, 0x06, 0x06, 0x00, 0x03, 0x03, 0x00, 0x03, 0x81,
  0xC0, 0x01, 0xC0, 0x60, 0x01, 0xC0, 0x38, 0x03, 0x60, 0x0F, 0x07, 0x30,
  0x03, 0xFF, 0x18, 0x00, 0x7E, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x00, 0x01,
  0x80, 0x00, 0x03, 0x80, 0x03, 0xFF, 0x80, 0x01, 0xFF, 0x00, 0x00, 0x07,
  0xE0, 0x00, 0x07, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x01, 0x83, 0xF0, 0x01,
  0x8F, 0xF8, 0x01, 0x98, 0x1C, 0x03, 0xB0, 0x0E, 0x03, 0x40, 0x06, 0x03,
  0x80, 0x06, 0x03, 0x00, 0x06, 0x03, 0x00, 0x06, 0x07, 0x00, 0x06, 0x06,
  0x00, 0x0E, 0x06, 0x00, 0x0E, 0x06, 0x00, 0x0E, 0x06, 0x00, 0x0C, 0x0C,
  0x00, 0x0C, 0x0C, 0x00, 0x1C, 0x0C, 0x00, 0x1C, 0x0C, 0x00, 0x18, 0x0C,
  0x00, 0x18, 0x18, 0x00, 0x18, 0xFF, 0x01, 0xFF, 0xFF, 0x01, 0xFF, 0x00,
  0x07, 0x00, 0x00, 0xC0, 0x00, 0x38, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x1F,
  0xF0, 0x00, 0x06, 0x00, 0x01, 0xC0, 0x00, 0x30, 0x00, 0x06, 0x00, 0x00,
  0xC0, 0x00, 0x18, 0x00, 0x07, 0x00, 0x00, 0xC0, 0x00, 0x18, 0x00, 0x03,
  0x00, 0x00, 0x60, 0x00, 0x1C, 0x00, 0x03, 0x00, 0x00, 0x60, 0x00, 0x0C,
  0x00, 0x01, 0x80, 0x7F, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x70, 0x00,
  0x07, 0x00, 0x00, 0x70, 0x00, 0x06, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0x03, 0xFF, 0xF0,
  0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x06, 0x00, 0x00, 0x60, 0x00, 0x06,
  0x00, 0x00, 0x60, 0x00, 0x06, 0x00, 0x00, 0xC0, 0x00, 0x0C, 0x00, 0x00,
  0xC0, 0x00, 0x0C, 0x00, 0x01, 0xC0, 0x00, 0x18, 0x00, 0x01, 0x80, 0x00,
  0x18, 0x00, 0x01, 0x80, 0x00, 0x38, 0x00, 0x03, 0x00, 0x00, 0x30, 0x00,
  0x03, 0x00, 0x00, 0x30, 0x00, 0x06, 0x00, 0x00, 0xE0, 0x00, 0x1C, 0x00,
  0x03, 0x80, 0xFF, 0xF0, 0x0F, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x03, 0xE0,
  0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60,
  0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0xFF, 0x00, 0xC1,
  0xFF, 0x00, 0x80, 0x70, 0x01, 0x80, 0xC0, 0x01, 0x83, 0x80, 0x01, 0x87,
  0x00, 0x01, 0x8C, 0x00, 0x03, 0x38, 0x00, 0x03, 0x70, 0x00, 0x03, 0xF8,
  0x00, 0x03, 0x9C, 0x00, 0x03, 0x0C, 0x00, 0x06, 0x0E, 0x00, 0x06, 0x07,
  0x00, 0x06, 0x03, 0x80, 0x06, 0x01, 0x80, 0x04, 0x00, 0xC0, 0x0C, 0x00,
  0xE0, 0xFC, 0x03, 0xFE, 0xFC, 0x03, 0xFC, 0x01, 0xFF, 0x00, 0x3F, 0xE0,
  0x00, 0x0C, 0x00, 0x03, 0x00, 0x00, 0x60, 0x00, 0x0C, 0x00, 0x01, 0x80,
  0x00, 0x70, 0x00, 0x0C, 0x00, 0x01, 0x80, 0x00, 0x30, 0x00, 0x06, 0x00,
  0x01, 0x80, 0x00, 0x30, 0x00, 0x06, 0x00, 0x00, 0xC0, 0x00, 0x18, 0x00,
  0x06, 0x00, 0x00, 0xC0, 0x00, 0x18, 0x00, 0x03, 0x00, 0x00, 0xE0, 0x00,
  0x18, 0x00, 0x03, 0x00, 0x00, 0x60, 0x00, 0x0C, 0x03, 0xFF, 0xFF, 0xFF,
  0xFF, 0xF0, 0x00, 0x1E, 0x07, 0x81, 0xE7, 0xE1, 0xF8, 0x3D, 0x8E, 0xE3,
  0x81, 0xE0, 0xF8, 0x30, 0x38, 0x1E, 0x06, 0x06, 0x03, 0x80, 0xC1, 0x80,
  0x60, 0x18, 0x30, 0x0C, 0x03, 0x06, 0x01, 0x80, 0x60, 0xC0, 0x30, 0x08,
  0x18, 0x0C, 0x03, 0x06, 0x01, 0x80, 0x60, 0xC0, 0x30, 0x0C, 0x18, 0x06,
  0x01, 0x83, 0x00, 0x80, 0x60, 0x40, 0x30, 0x0C, 0x18, 0x06, 0x01, 0x83,
  0x00, 0xC0, 0x30, 0x60, 0x18, 0x06, 0x7F, 0x03, 0xC1, 0xFF, 0xE0, 0xF8,
  0x3E, 0x00, 0x03, 0xE0, 0x1F, 0x1F, 0xF0, 0x3E, 0x60, 0x70, 0x0F, 0x80,
  0x70, 0x3C, 0x00, 0x60, 0x70, 0x00, 0xC0, 0xC0, 0x01, 0x81, 0x80, 0x03,
  0x07, 0x00, 0x06, 0x0C, 0x00, 0x1C, 0x18, 0x00, 0x30, 0x30, 0x00, 0x60,
  0x60, 0x00, 0xC1, 0xC0, 0x01, 0x83, 0x00, 0x06, 0x06, 0x00, 0x0C, 0x0C,
  0x00, 0x18, 0x18, 0x00, 0x30, 0x70, 0x00, 0x67, 0xFC, 0x07, 0xFF, 0xF0,
  0x0F, 0xE0, 0x00, 0x3F, 0x00, 0x07, 0xFF, 0x00, 0x3C, 0x0F, 0x01, 0xC0,
  0x1C, 0x0C, 0x00, 0x38, 0x60, 0x00, 0x63, 0x00, 0x00, 0xDC, 0x00, 0x03,
  0x60, 0x00, 0x0D, 0x80, 0x00, 0x3C, 0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0,
  0x00, 0x1B, 0x00, 0x00, 0x6C, 0x00, 0x03, 0xB0, 0x00, 0x0C, 0x60, 0x00,
  0x61, 0xC0, 0x03, 0x03, 0x80, 0x38, 0x0F, 0x03, 0xC0, 0x0F, 0xFE, 0x00,
  0x0F, 0xC0, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x1F, 0x8F, 0xFE, 0x00, 0xFC,
  0xE0, 0x78, 0x00, 0xCC, 0x00, 0xE0, 0x06, 0xC0, 0x03, 0x00, 0x3C, 0x00,
  0x1C, 0x01, 0xC0, 0x00, 0x60, 0x0C, 0x00, 0x03, 0x00, 0xE0, 0x00, 0x18,
  0x06, 0x00, 0x00, 0xC0, 0x30, 0x00, 0x06, 0x01, 0x80, 0x00, 0x30, 0x0C,
  0x00, 0x03, 0x00, 0xE0, 0x00, 0x18, 0x07, 0x00, 0x01, 0x80, 0x3C, 0x00,
  0x1C, 0x01, 0xE0, 0x01, 0xC0, 0x0D, 0x80, 0x1C, 0x00, 0xCF, 0x03, 0xC0,
  0x06, 0x3F, 0xF8, 0x00, 0x30, 0x7F, 0x00, 0x01, 0x80, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x01, 0x80, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00,
  0x7F, 0xF8, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7F, 0xE1, 0xF0, 0x78,
  0x1C, 0xFC, 0x38, 0x01, 0xB0, 0x1C, 0x00, 0x2C, 0x0E, 0x00, 0x0F, 0x03,
  0x00, 0x01, 0xC1, 0x80, 0x00, 0x60, 0x60, 0x00, 0x18, 0x30, 0x00, 0x06,
  0x0C, 0x00, 0x01, 0x83, 0x00, 0x00, 0x60, 0xC0, 0x00, 0x30, 0x30, 0x00,
  0x0C, 0x0C, 0x00, 0x07, 0x03, 0x80, 0x03, 0xC0, 0x60, 0x01, 0xB0, 0x1C,
  0x00, 0xD8, 0x03, 0xC0, 0xE6, 0x00, 0x7F, 0xF1, 0x80, 0x07, 0xE0, 0x60,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0x00, 0x30, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x06, 0x00, 0x00,
  0x7F, 0xF8, 0x00, 0x1F, 0xFE, 0x00, 0x07, 0xF0, 0x3E, 0x03, 0xF8, 0x7F,
  0xC0, 0x18, 0xF0, 0x60, 0x0C, 0xE0, 0x00, 0x07, 0xE0, 0x00, 0x03, 0xC0,
  0x00, 0x03, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x30, 0x00, 0x00, 0x38, 0x00, 0x00, 0x18, 0x00, 0x00, 0x0C,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x80, 0x00, 0x01,
  0x80, 0x00, 0x3F, 0xFF, 0xF0, 0x1F, 0xFF, 0xF0, 0x00, 0x00, 0x3F, 0x00,
  0x0F, 0xFE, 0xC0, 0xF0, 0x3E, 0x0E, 0x00, 0x70, 0xE0, 0x01, 0x06, 0x00,
  0x08, 0x30, 0x00, 0x41, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x3F, 0xF0, 0x00,
  0x3F, 0xE0, 0x00, 0x07, 0xC0, 0x00, 0x07, 0x00, 0x00, 0x18, 0x00, 0x00,
  0xCC, 0x00, 0x06, 0x60, 0x00, 0x33, 0x00, 0x03, 0x3C, 0x00, 0x71, 0xF8,
  0x0F, 0x0D, 0xFF, 0xF0, 0x01, 0xFC, 0x00, 0x03, 0x00, 0x03, 0x00, 0x01,
  0x80, 0x00, 0xC0, 0x00, 0x60, 0x00, 0x70, 0x03, 0xFF, 0xFF, 0xFF, 0xFF,
  0x0C, 0x00, 0x06, 0x00, 0x06, 0x00, 0x03, 0x00, 0x01, 0x80, 0x00, 0xC0,
  0x00, 0xE0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00, 0x0C, 0x00, 0x0E,
  0x00, 0x06, 0x00, 0x03, 0x00, 0x01, 0x80, 0x00, 0xC0, 0x03, 0x38, 0x0F,
  0x9F, 0xFF, 0x03, 0xF8, 0x00, 0xFC, 0x03, 0xFF, 0xE0, 0x1F, 0xC6, 0x00,
  0x0C, 0x30, 0x00, 0x61, 0x80, 0x03, 0x0C, 0x00, 0x18, 0x60, 0x01, 0x86,
  0x00, 0x0C, 0x30, 0x00, 0x61, 0x80, 0x03, 0x0C, 0x00, 0x18, 0x60, 0x01,
  0x86, 0x00, 0x0C, 0x30, 0x00, 0x61, 0x80, 0x03, 0x0C, 0x00, 0x38, 0x60,
  0x07, 0x83, 0x80, 0x6C, 0x1E, 0x1E, 0x7C, 0x7F, 0xE3, 0xE0, 0xF8, 0x00,
  0x00, 0x7F, 0xC0, 0xFF, 0xFF, 0xF0, 0x3F, 0xF1, 0xC0, 0x00, 0xC0, 0x30,
  0x00, 0x60, 0x0C, 0x00, 0x18, 0x03, 0x00, 0x0C, 0x00, 0xE0, 0x06, 0x00,
  0x18, 0x01, 0x80, 0x06, 0x00, 0xC0, 0x01, 0x80, 0x30, 0x00, 0x60, 0x18,
  0x00, 0x0C, 0x0C, 0x00, 0x03, 0x03, 0x00, 0x00, 0xC1, 0x80, 0x00, 0x30,
  0xC0, 0x00, 0x06, 0x30, 0x00, 0x01, 0x98, 0x00, 0x00, 0x6C, 0x00, 0x00,
  0x1F, 0x00, 0x00, 0x07, 0x80, 0x00, 0xFE, 0x00, 0x7F, 0xFF, 0x00, 0x3F,
  0xCC, 0x00, 0x03, 0x06, 0x00, 0x01, 0x83, 0x00, 0x01, 0x81, 0x81, 0x80,
  0xC0, 0xC1, 0xE0, 0x60, 0x60, 0xF0, 0x60, 0x30, 0xD8, 0x30, 0x18, 0x6C,
  0x30, 0x0C, 0x66, 0x18, 0x06, 0x33, 0x18, 0x03, 0x31, 0x8C, 0x01, 0x98,
  0x66, 0x00, 0xD8, 0x36, 0x00, 0x6C, 0x1B, 0x00, 0x3C, 0x0F, 0x00, 0x1E,
  0x07, 0x80, 0x0E, 0x03, 0x80, 0x07, 0x01, 0xC0, 0x00, 0x07, 0xF0, 0x3F,
  0xC3, 0xFC, 0x0F, 0xF0, 0x38, 0x00, 0x60, 0x07, 0x00, 0x70, 0x00, 0xE0,
  0x38, 0x00, 0x18, 0x1C, 0x00, 0x03, 0x0C, 0x00, 0x00, 0xEE, 0x00, 0x00,
  0x1F, 0x00, 0x00, 0x03, 0x80, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xDC, 0x00,
  0x00, 0xE3, 0x80, 0x00, 0x70, 0x70, 0x00, 0x38, 0x0E, 0x00, 0x18, 0x01,
  0x80, 0x1C, 0x00, 0x30, 0x0E, 0x00, 0x0E, 0x0F, 0xF0, 0x3F, 0xE3, 0xFC,
  0x0F, 0xF8, 0x03, 0xF8, 0x07, 0xF8, 0x3F, 0xC0, 0x3F, 0xC0, 0x60, 0x00,
  0x30, 0x01, 0x80, 0x01, 0x80, 0x0C, 0x00, 0x18, 0x00, 0x60, 0x01, 0x80,
  0x03, 0x80, 0x0C, 0x00, 0x0C, 0x00, 0xC0, 0x00, 0x60, 0x0C, 0x00, 0x03,
  0x00, 0x60, 0x00, 0x0C, 0x06, 0x00, 0x00, 0x60, 0x60, 0x00, 0x03, 0x06,
  0x00, 0x00, 0x1C, 0x30, 0x00, 0x00, 0x63, 0x00, 0x00, 0x03, 0x30, 0x00,
  0x00, 0x19, 0x80, 0x00, 0x00, 0x78, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00,
  0x1C, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x60,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x30, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0x00,
  0x07, 0xFF, 0xF8, 0x3F, 0xFF, 0xC3, 0x00, 0x0C, 0x18, 0x00, 0xC0, 0xC0,
  0x0C, 0x00, 0x00, 0xC0, 0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00,
  0x01, 0xC0, 0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00, 0x18, 0x00, 0x01, 0x80,
  0x00, 0x18, 0x00, 0x01, 0x80, 0x0C, 0x18, 0x00, 0x61, 0x80, 0x02, 0x1F,
  0xFF, 0xF0, 0xFF, 0xFF, 0x80, 0x00, 0x0E, 0x00, 0x7C, 0x01, 0xC0, 0x03,
  0x00, 0x0C, 0x00, 0x18, 0x00, 0x30, 0x00, 0x60, 0x01, 0xC0, 0x03, 0x00,
  0x06, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x60, 0x01, 0xC0, 0x0F, 0x00, 0xF8,
  0x01, 0xF0, 0x00, 0x30, 0x00, 0x30, 0x00, 0x60, 0x00, 0xC0, 0x03, 0x80,
  0x06, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x30, 0x00, 0xE0, 0x01, 0x80, 0x03,
  0x00, 0x06, 0x00, 0x0E, 0x00, 0x0F, 0x00, 0x0E, 0x00, 0x01, 0x80, 0xC0,
  0x60, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x06, 0x03, 0x01, 0x80, 0xC0, 0x40,
  0x60, 0x30, 0x18, 0x0C, 0x0C, 0x06, 0x03, 0x01, 0x80, 0xC0, 0xC0, 0x60,
  0x30, 0x18, 0x08, 0x0C, 0x06, 0x03, 0x01, 0x80, 0x80, 0xC0, 0x60, 0x30,
  0x00, 0x01, 0xC0, 0x03, 0xC0, 0x01, 0xC0, 0x01, 0x80, 0x03, 0x00, 0x06,
  0x00, 0x0C, 0x00, 0x30, 0x00, 0x60, 0x00, 0xC0, 0x01, 0x80, 0x03, 0x00,
  0x0C, 0x00, 0x18, 0x00, 0x38, 0x00, 0x38, 0x00, 0x3E, 0x00, 0x7C, 0x03,
  0xC0, 0x0E, 0x00, 0x18, 0x00, 0x70, 0x00, 0xC0, 0x01, 0x80, 0x03, 0x00,
  0x06, 0x00, 0x18, 0x00, 0x30, 0x00, 0x60, 0x00, 0xC0, 0x03, 0x00, 0x0E,
  0x00, 0xF8, 0x01, 0xC0, 0x00, 0x0F, 0x00, 0x01, 0xFC, 0x03, 0x70, 0xE0,
  0x7E, 0x07, 0x1E, 0xC0, 0x3F, 0x80, 0x01, 0xE0 };

const GFXglyph FreeMonoOblique24pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,  28,    0,    1 },   // 0x20 ' '
  {     0,  10,  30,  28,   12,  -28 },   // 0x21 '!'
  {    38,  16,  14,  28,   10,  -28 },   // 0x22 '"'
  {    66,  22,  32,  28,    6,  -29 },   // 0x23 '#'
  {   154,  21,  33,  28,    6,  -29 },   // 0x24 '$'
  {   241,  22,  29,  28,    6,  -27 },   // 0x25 '%'
  {   321,  19,  25,  28,    6,  -23 },   // 0x26 '&'
  {   381,   7,  14,  28,   16,  -28 },   // 0x27 '''
  {   394,  11,  34,  28,   16,  -27 },   // 0x28 '('
  {   441,  11,  34,  28,    7,  -27 },   // 0x29 ')'
  {   488,  18,  17,  28,   10,  -28 },   // 0x2A '*'
  {   527,  22,  22,  28,    6,  -23 },   // 0x2B '+'
  {   588,  12,  14,  28,    5,   -6 },   // 0x2C ','
  {   609,  22,   2,  28,    6,  -13 },   // 0x2D '-'
  {   615,   7,   6,  28,   11,   -4 },   // 0x2E '.'
  {   621,  24,  35,  28,    5,  -30 },   // 0x2F '/'
  {   726,  20,  30,  28,    7,  -28 },   // 0x30 '0'
  {   801,  17,  29,  28,    6,  -28 },   // 0x31 '1'
  {   863,  23,  29,  28,    4,  -28 },   // 0x32 '2'
  {   947,  22,  30,  28,    5,  -28 },   // 0x33 '3'
  {  1030,  19,  28,  28,    7,  -27 },   // 0x34 '4'
  {  1097,  21,  29,  28,    6,  -27 },   // 0x35 '5'
  {  1174,  21,  30,  28,    9,  -28 },   // 0x36 '6'
  {  1253,  18,  28,  28,   10,  -27 },   // 0x37 '7'
  {  1316,  20,  30,  28,    7,  -28 },   // 0x38 '8'
  {  1391,  22,  30,  28,    6,  -28 },   // 0x39 '9'
  {  1474,  10,  21,  28,   11,  -19 },   // 0x3A ':'
  {  1501,  15,  27,  28,    5,  -19 },   // 0x3B ';'
  {  1552,  23,  22,  28,    6,  -23 },   // 0x3C '<'
  {  1616,  25,   9,  28,    4,  -17 },   // 0x3D '='
  {  1645,  24,  22,  28,    4,  -23 },   // 0x3E '>'
  {  1711,  16,  28,  28,   11,  -26 },   // 0x3F '?'
  {  1767,  19,  32,  28,    7,  -28 },   // 0x40 '@'
  {  1843,  27,  26,  28,    1,  -25 },   // 0x41 'A'
  {  1931,  26,  26,  28,    2,  -25 },   // 0x42 'B'
  {  2016,  25,  28,  28,    5,  -26 },   // 0x43 'C'
  {  2104,  26,  26,  28,    2,  -25 },   // 0x44 'D'
  {  2189,  27,  26,  28,    2,  -25 },   // 0x45 'E'
  {  2277,  28,  26,  28,    2,  -25 },   // 0x46 'F'
  {  2368,  25,  28,  28,    5,  -26 },   // 0x47 'G'
  {  2456,  27,  26,  28,    3,  -25 },   // 0x48 'H'
  {  2544,  22,  26,  28,    6,  -25 },   // 0x49 'I'
  {  2616,  28,  27,  28,    5,  -25 },   // 0x4A 'J'
  {  2711,  29,  26,  28,    2,  -25 },   // 0x4B 'K'
  {  2806,  25,  26,  28,    3,  -25 },   // 0x4C 'L'
  {  2888,  32,  26,  28,    1,  -25 },   // 0x4D 'M'
  {  2992,  30,  26,  28,    2,  -25 },   // 0x4E 'N'
  {  3090,  24,  28,  28,    5,  -26 },   // 0x4F 'O'
  {  3174,  26,  26,  28,    2,  -25 },   // 0x50 'P'
  {  3259,  24,  32,  28,    5,  -26 },   // 0x51 'Q'
  {  3355,  26,  26,  28,    2,  -25 },   // 0x52 'R'
  {  3440,  24,  28,  28,    5,  -26 },   // 0x53 'S'
  {  3524,  24,  26,  28,    7,  -25 },   // 0x54 'T'
  {  3602,  26,  27,  28,    6,  -25 },   // 0x55 'U'
  {  3690,  27,  26,  28,    6,  -25 },   // 0x56 'V'
  {  3778,  27,  26,  28,    6,  -25 },   // 0x57 'W'
  {  3866,  29,  26,  28,    2,  -25 },   // 0x58 'X'
  {  3961,  24,  26,  28,    7,  -25 },   // 0x59 'Y'
  {  4039,  23,  26,  28,    5,  -25 },   // 0x5A 'Z'
  {  4114,  15,  34,  28,   12,  -27 },   // 0x5B '['
  {  4178,  10,  35,  28,   12,  -30 },   // 0x5C '\'
  {  4222,  15,  34,  28,    6,  -27 },   // 0x5D ']'
  {  4286,  18,  12,  28,    9,  -28 },   // 0x5E '^'
  {  4313,  28,   2,  28,   -1,    5 },   // 0x5F '_'
  {  4320,   6,   7,  28,   13,  -29 },   // 0x60 '`'
  {  4326,  22,  22,  28,    4,  -20 },   // 0x61 'a'
  {  4387,  27,  29,  28,    1,  -27 },   // 0x62 'b'
  {  4485,  22,  22,  28,    6,  -20 },   // 0x63 'c'
  {  4546,  25,  29,  28,    5,  -27 },   // 0x64 'd'
  {  4637,  22,  22,  28,    5,  -20 },   // 0x65 'e'
  {  4698,  26,  28,  28,    5,  -27 },   // 0x66 'f'
  {  4789,  25,  30,  28,    5,  -20 },   // 0x67 'g'
  {  4883,  24,  28,  28,    3,  -27 },   // 0x68 'h'
  {  4967,  19,  29,  28,    5,  -28 },   // 0x69 'i'
  {  5036,  20,  38,  28,    4,  -28 },   // 0x6A 'j'
  {  5131,  24,  28,  28,    3,  -27 },   // 0x6B 'k'
  {  5215,  19,  28,  28,    5,  -27 },   // 0x6C 'l'
  {  5282,  27,  21,  28,    1,  -20 },   // 0x6D 'm'
  {  5353,  23,  21,  28,    3,  -20 },   // 0x6E 'n'
  {  5414,  22,  22,  28,    5,  -20 },   // 0x6F 'o'
  {  5475,  29,  30,  28,   -1,  -20 },   // 0x70 'p'
  {  5584,  26,  30,  28,    5,  -20 },   // 0x71 'q'
  {  5682,  25,  20,  28,    4,  -19 },   // 0x72 'r'
  {  5745,  21,  22,  28,    5,  -20 },   // 0x73 's'
  {  5803,  17,  27,  28,    7,  -25 },   // 0x74 't'
  {  5861,  21,  21,  28,    6,  -19 },   // 0x75 'u'
  {  5917,  26,  20,  28,    5,  -19 },   // 0x76 'v'
  {  5982,  25,  20,  28,    6,  -19 },   // 0x77 'w'
  {  6045,  26,  20,  28,    3,  -19 },   // 0x78 'x'
  {  6110,  29,  29,  28,    1,  -19 },   // 0x79 'y'
  {  6216,  21,  20,  28,    5,  -19 },   // 0x7A 'z'
  {  6269,  15,  34,  28,   10,  -27 },   // 0x7B '{'
  {  6333,   9,  35,  28,   12,  -28 },   // 0x7C '|'
  {  6373,  15,  34,  28,    8,  -27 },   // 0x7D '}'
  {  6437,  20,   6,  28,    7,  -15 } }; // 0x7E '~'

const GFXfont FreeMonoOblique24pt7b PROGMEM = {
  (uint8_t  *)FreeMonoOblique24pt7bBitmaps,
  (GFXglyph *)FreeMonoOblique24pt7bGlyphs,
  0x20, 0x7E, 47 };

// Approx. 7124 bytes
