






Started logging.
2022/06/29 16:47:42 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\3_4-3_Flash_PNG\Flash_PNG\inols.log
2022/06/29 16:47:42 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/29 16:47:42 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server520385891
2022/06/29 16:47:42 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server520385891\sketch
2022/06/29 16:47:42 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/29 16:47:42 IDE --> initialize 0 [93m locked[0m
2022/06/29 16:47:42 IDE --> initialize 0 [93m unlocked[0m
2022/06/29 16:47:42 INIT--- initializing workbench
2022/06/29 16:47:42 INIT--- [93m locked[0m
2022/06/29 16:47:42     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG)
2022/06/29 16:47:42 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/29 16:47:42 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\054266214 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server520385891 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\3_4-3_Flash_PNG\Flash_PNG
2022/06/29 16:47:42 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/29 16:47:44 INIT--- initializing workbench (done)
2022/06/29 16:47:44 INIT--- [93m unlocked[0m
2022/06/29 16:47:44 IDE --> initialized notif1 [93m read-locked[0m
2022/06/29 16:47:44 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/29 16:47:44 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/29 16:47:44 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/29 16:47:44 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/29 16:47:44 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/29 16:47:44 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m







Started logging.
2022/06/30 11:46:49 logging to c:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\3_4-3_Flash_PNG\Flash_PNG\inols.log
2022/06/30 11:46:49 Initial board configuration: {"ESP32 Dev Module" esp32:esp32:esp32}
2022/06/30 11:46:49 Language server build path: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server922575643
2022/06/30 11:46:49 Language server build sketch root: C:\Users\<USER>\AppData\Local\Temp\arduino-language-server922575643\sketch
2022/06/30 11:46:49 [92mIDE --> LS     CL: REQUEST initialize 0: [0m
2022/06/30 11:46:49 IDE --> initialize 0 [93m locked[0m
2022/06/30 11:46:49 IDE --> initialize 0 [93m unlocked[0m
2022/06/30 11:46:49 INIT--- initializing workbench
2022/06/30 11:46:49 INIT--- [93m locked[0m
2022/06/30 11:46:49     --> initialize(file:///c%3A/Users/<USER>/Desktop/ESP32-2432S028%E4%BE%8B%E5%AD%90/arduino/Source%20code/3_4-3_Flash_PNG/Flash_PNG)
2022/06/30 11:46:49 [91mIDE <-- LS     CL: ANSWER UNBOUND (0): [0m
2022/06/30 11:46:49 running:  d:\arduino\Arduino IDE\resources\app\node_modules\arduino-ide-extension\build\arduino-cli.exe --config-file c:\Users\<USER>\.arduinoIDE\arduino-cli.yaml compile --fqbn esp32:esp32:esp32 --only-compilation-database --clean --source-override C:\Users\<USER>\AppData\Local\Temp\067787710 --build-path C:\Users\<USER>\AppData\Local\Temp\arduino-language-server922575643 --format json C:\Users\<USER>\Desktop\ESP32-2432S028例子\arduino\Source code\3_4-3_Flash_PNG\Flash_PNG
2022/06/30 11:46:49 [92mIDE --> LS     CL: NOTIFICATION initialized: [0m
2022/06/30 11:46:51 INIT--- initializing workbench (done)
2022/06/30 11:46:51 INIT--- [93m unlocked[0m
2022/06/30 11:46:51 IDE --> initialized notif1 [93m read-locked[0m
2022/06/30 11:46:51 IDE --> initialized notif1 notification is not propagated to clangd
2022/06/30 11:46:51 IDE --> initialized notif1 [93m read-unlocked[0m
2022/06/30 11:46:51 [92mIDE --> LS     CL: NOTIFICATION textDocument/didOpen: [0m
2022/06/30 11:46:51 IDE --> textDocument/didOpen notif2 [93m locked[0m
2022/06/30 11:46:51 IDE --> textDocument/didOpen notif2 (throttled: waiting for clangd)
2022/06/30 11:46:51 IDE --> textDocument/didOpen notif2 [93m unlocked (waiting clangd)[0m
