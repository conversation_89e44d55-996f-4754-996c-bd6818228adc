// User_Setup.h for ESP32-2432S028R (CYD)
// Copy this file to your TFT_eSPI library folder

#define USER_SETUP_ID 206

// Driver
#define ILI9341_DRIVER

// ESP32 pins for ESP32-2432S028R
#define TFT_MISO 19
#define TFT_MOSI 23
#define TFT_SCLK 18
#define TFT_CS   15
#define TFT_DC   2
#define TFT_RST  -1  // Not connected

// Backlight control
#define TFT_BL   21
#define TFT_BACKLIGHT_ON HIGH

// Fonts
#define LOAD_GLCD
#define LOAD_FONT2
#define LOAD_FONT4
#define LOAD_FONT6
#define LOAD_FONT7
#define LOAD_FONT8
#define LOAD_GFXFF

#define SMOOTH_FONT

// SPI frequency
#define SPI_FREQUENCY  27000000
#define SPI_READ_FREQUENCY  20000000
#define SPI_TOUCH_FREQUENCY  2500000
