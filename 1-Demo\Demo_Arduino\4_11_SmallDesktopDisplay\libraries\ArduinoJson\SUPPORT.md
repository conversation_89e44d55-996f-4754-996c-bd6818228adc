# ArduinoJson Support

First off, thank you very much for using ArduinoJson.

We'll be very happy to help you, but first please read the following.

## Before asking for help

1. Read the [FAQ](https://arduinojson.org/faq/?utm_source=github&utm_medium=support)
2. Search in the [API Reference](https://arduinojson.org/api/?utm_source=github&utm_medium=support)

If you did not find the answer, please create a [new issue on GitHub](https://github.com/bblanchon/ArduinoJson/issues/new).

It is OK to add a comment to a currently opened issue, but please avoid adding comments to a closed issue.

## Before hitting the Submit button

Please provide all the relevant information:

* Good title
* Short description of the problem
* Target platform
* Compiler model and version
* [MVCE](https://stackoverflow.com/help/mcve)
* Compiler output

Good questions get fast answers!
